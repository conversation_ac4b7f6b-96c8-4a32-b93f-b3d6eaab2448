# KnowledgeBot - Corporate Memory System
# Kubernetes Backend Deployment Configuration
# 
# This file defines the deployment, service, and configuration for the FastAPI backend

apiVersion: apps/v1
kind: Deployment
metadata:
  name: knowledgebot-backend
  namespace: knowledgebot
  labels:
    app: knowledgebot-backend
    component: api
    version: v1
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: knowledgebot-backend
  template:
    metadata:
      labels:
        app: knowledgebot-backend
        component: api
        version: v1
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: knowledgebot-backend
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000
      containers:
      - name: backend
        image: knowledgebot/backend:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8000
          name: http
          protocol: TCP
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: knowledgebot-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: knowledgebot-secrets
              key: redis-url
        - name: JWT_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: knowledgebot-secrets
              key: jwt-secret-key
        - name: MEMVID_MODEL_PATH
          value: "./models/memvid"
        - name: MEMVID_MODEL_NAME
          value: "sentence-transformers/all-MiniLM-L6-v2"
        - name: PINECONE_API_KEY
          valueFrom:
            secretKeyRef:
              name: knowledgebot-secrets
              key: pinecone-api-key
        - name: S3_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: knowledgebot-secrets
              key: s3-access-key
        - name: S3_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: knowledgebot-secrets
              key: s3-secret-key
        envFrom:
        - configMapRef:
            name: knowledgebot-config
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: storage
          mountPath: /app/storage
        - name: logs
          mountPath: /app/logs
        - name: config
          mountPath: /app/config
          readOnly: true
      volumes:
      - name: storage
        persistentVolumeClaim:
          claimName: knowledgebot-storage
      - name: logs
        emptyDir: {}
      - name: config
        configMap:
          name: knowledgebot-config
      nodeSelector:
        kubernetes.io/os: linux
      tolerations:
      - key: "node.kubernetes.io/not-ready"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300
      - key: "node.kubernetes.io/unreachable"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300

---
apiVersion: v1
kind: Service
metadata:
  name: knowledgebot-backend-service
  namespace: knowledgebot
  labels:
    app: knowledgebot-backend
    component: api
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "8000"
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 8000
    protocol: TCP
    name: http
  selector:
    app: knowledgebot-backend

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: knowledgebot-backend
  namespace: knowledgebot
  labels:
    app: knowledgebot-backend

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: knowledgebot-storage
  namespace: knowledgebot
  labels:
    app: knowledgebot-backend
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 100Gi
  storageClassName: fast-ssd

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: knowledgebot-backend-pdb
  namespace: knowledgebot
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: knowledgebot-backend

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: knowledgebot-backend-hpa
  namespace: knowledgebot
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: knowledgebot-backend
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
      - type: Pods
        value: 2
        periodSeconds: 60
      selectPolicy: Max

---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: knowledgebot-backend-netpol
  namespace: knowledgebot
spec:
  podSelector:
    matchLabels:
      app: knowledgebot-backend
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: knowledgebot-frontend
    - podSelector:
        matchLabels:
          app: knowledgebot-nginx
    ports:
    - protocol: TCP
      port: 8000
  egress:
  - to:
    - podSelector:
        matchLabels:
          app: postgres
    ports:
    - protocol: TCP
      port: 5432
  - to:
    - podSelector:
        matchLabels:
          app: redis
    ports:
    - protocol: TCP
      port: 6379
  - to: []
    ports:
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80
