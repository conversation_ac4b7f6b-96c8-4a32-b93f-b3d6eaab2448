# KnowledgeBot - Corporate Memory System
# Docker Compose Configuration for Local Development
# 
# This file sets up all services needed for local development including
# the backend API, frontend, database, cache, and supporting services.

version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: knowledgebot-postgres
    environment:
      POSTGRES_DB: knowledgebot
      POSTGRES_USER: knowledgebot
      POSTGRES_PASSWORD: password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    ports:
      - "5432:5432"
    networks:
      - knowledgebot-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U knowledgebot -d knowledgebot"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache and Message Broker
  redis:
    image: redis:7-alpine
    container_name: knowledgebot-redis
    command: redis-server --appendonly yes --requirepass redis_password
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - knowledgebot-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # MinIO S3-Compatible Storage
  minio:
    image: minio/minio:latest
    container_name: knowledgebot-minio
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    volumes:
      - minio_data:/data
    ports:
      - "9000:9000"
      - "9001:9001"
    networks:
      - knowledgebot-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  # Elasticsearch for Full-Text Search
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: knowledgebot-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - knowledgebot-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5

  # FastAPI Backend
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: development
    container_name: knowledgebot-backend
    environment:
      - ENVIRONMENT=development
      - DATABASE_URL=************************************************/knowledgebot
      - REDIS_URL=redis://:redis_password@redis:6379/0
      - S3_ENDPOINT_URL=http://minio:9000
      - S3_ACCESS_KEY=minioadmin
      - S3_SECRET_KEY=minioadmin123
      - S3_BUCKET_NAME=knowledgebot-documents
      - ELASTICSEARCH_URL=http://elasticsearch:9200
      - CORS_ORIGINS=http://localhost:3000,http://localhost:8080
      - JWT_SECRET_KEY=your-super-secret-jwt-key-for-development
      - MEMVID_MODEL_PATH=./models/memvid
      - MEMVID_MODEL_NAME=sentence-transformers/all-MiniLM-L6-v2
      - PINECONE_API_KEY=${PINECONE_API_KEY:-}
    volumes:
      - ./backend:/app
      - backend_storage:/app/storage
    ports:
      - "8000:8000"
    networks:
      - knowledgebot-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # React Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: development
    container_name: knowledgebot-frontend
    environment:
      - REACT_APP_API_URL=http://localhost:8000/api/v1
      - REACT_APP_ENVIRONMENT=development
      - CHOKIDAR_USEPOLLING=true
    volumes:
      - ./frontend:/app
      - /app/node_modules
    ports:
      - "3000:3000"
    networks:
      - knowledgebot-network
    depends_on:
      - backend
    stdin_open: true
    tty: true

  # Celery Worker for Background Tasks
  celery-worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: development
    container_name: knowledgebot-celery-worker
    command: celery -A app.core.celery worker --loglevel=info
    environment:
      - ENVIRONMENT=development
      - DATABASE_URL=************************************************/knowledgebot
      - REDIS_URL=redis://:redis_password@redis:6379/0
      - CELERY_BROKER_URL=redis://:redis_password@redis:6379/1
      - CELERY_RESULT_BACKEND=redis://:redis_password@redis:6379/2
      - S3_ENDPOINT_URL=http://minio:9000
      - S3_ACCESS_KEY=minioadmin
      - S3_SECRET_KEY=minioadmin123
      - MEMVID_MODEL_PATH=./models/memvid
      - MEMVID_MODEL_NAME=sentence-transformers/all-MiniLM-L6-v2
    volumes:
      - ./backend:/app
      - backend_storage:/app/storage
    networks:
      - knowledgebot-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped

  # Celery Beat Scheduler
  celery-beat:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: development
    container_name: knowledgebot-celery-beat
    command: celery -A app.core.celery beat --loglevel=info
    environment:
      - ENVIRONMENT=development
      - DATABASE_URL=************************************************/knowledgebot
      - REDIS_URL=redis://:redis_password@redis:6379/0
      - CELERY_BROKER_URL=redis://:redis_password@redis:6379/1
      - CELERY_RESULT_BACKEND=redis://:redis_password@redis:6379/2
    volumes:
      - ./backend:/app
    networks:
      - knowledgebot-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped

  # Flower - Celery Monitoring
  flower:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: development
    container_name: knowledgebot-flower
    command: celery -A app.core.celery flower --port=5555
    environment:
      - CELERY_BROKER_URL=redis://:redis_password@redis:6379/1
      - CELERY_RESULT_BACKEND=redis://:redis_password@redis:6379/2
    ports:
      - "5555:5555"
    networks:
      - knowledgebot-network
    depends_on:
      - redis
    restart: unless-stopped

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: knowledgebot-nginx
    volumes:
      - ./infrastructure/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./infrastructure/nginx/default.conf:/etc/nginx/conf.d/default.conf
    ports:
      - "8080:80"
    networks:
      - knowledgebot-network
    depends_on:
      - backend
      - frontend
    restart: unless-stopped

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: knowledgebot-prometheus
    volumes:
      - ./infrastructure/monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - knowledgebot-network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: knowledgebot-grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana_data:/var/lib/grafana
      - ./infrastructure/monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./infrastructure/monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    ports:
      - "3001:3000"
    networks:
      - knowledgebot-network
    depends_on:
      - prometheus

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  minio_data:
    driver: local
  elasticsearch_data:
    driver: local
  backend_storage:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  knowledgebot-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
