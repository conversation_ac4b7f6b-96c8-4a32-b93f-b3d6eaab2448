# KnowledgeBot Backend - Environment Configuration Example
# Copy this file to .env and update with your actual values

# Application Configuration
ENVIRONMENT=development
DEBUG=true
VERSION=1.0.0
API_V1_STR=/api/v1

# Server Configuration
HOST=0.0.0.0
PORT=8000
WORKERS=1

# Database Configuration
DATABASE_URL=postgresql+asyncpg://knowledgebot:password@localhost:5432/knowledgebot
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30

# Redis Configuration
REDIS_URL=redis://localhost:6379/0
REDIS_CACHE_TTL=3600

# Security Configuration
JWT_SECRET_KEY=your-super-secret-jwt-key-change-this-in-production
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# Password Configuration
PASSWORD_MIN_LENGTH=8
PASSWORD_REQUIRE_UPPERCASE=true
PASSWORD_REQUIRE_LOWERCASE=true
PASSWORD_REQUIRE_NUMBERS=true
PASSWORD_REQUIRE_SPECIAL=true

# MemVid Configuration (Open Source - No API Key Required)
MEMVID_MODEL_PATH=./models/memvid
MEMVID_MODEL_NAME=sentence-transformers/all-MiniLM-L6-v2
MEMVID_MAX_CHUNK_SIZE=1000
MEMVID_OVERLAP_SIZE=200
MEMVID_BATCH_SIZE=32
MEMVID_DEVICE=cpu

# Vector Database Configuration
VECTOR_DB_TYPE=pinecone

# Pinecone Configuration (Optional)
PINECONE_API_KEY=your-pinecone-api-key
PINECONE_ENVIRONMENT=us-west1-gcp
PINECONE_INDEX_NAME=knowledgebot
PINECONE_DIMENSION=1536

# Weaviate Configuration (Alternative)
WEAVIATE_URL=http://localhost:8080
WEAVIATE_API_KEY=your-weaviate-api-key

# File Storage Configuration
STORAGE_TYPE=local
LOCAL_STORAGE_PATH=./storage/documents

# S3/MinIO Configuration
S3_BUCKET_NAME=knowledgebot-documents
S3_ACCESS_KEY=your-s3-access-key
S3_SECRET_KEY=your-s3-secret-key
S3_ENDPOINT_URL=http://localhost:9000
S3_REGION=us-east-1

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_TLS=true
SMTP_SSL=false
FROM_EMAIL=<EMAIL>
FROM_NAME=KnowledgeBot

# Authentication & SSO
ENABLE_SSO=false

# SAML Configuration
SAML_ENTITY_ID=knowledgebot
SAML_SSO_URL=https://your-idp.com/sso
SAML_SLO_URL=https://your-idp.com/slo
SAML_X509_CERT=your-saml-certificate
SAML_PRIVATE_KEY=your-saml-private-key

# OAuth Configuration
OAUTH_CLIENT_ID=your-oauth-client-id
OAUTH_CLIENT_SECRET=your-oauth-client-secret
OAUTH_REDIRECT_URI=http://localhost:8000/auth/oauth/callback

# LDAP Configuration
LDAP_SERVER=ldap://your-ldap-server.com
LDAP_PORT=389
LDAP_USE_SSL=false
LDAP_BIND_DN=cn=admin,dc=example,dc=com
LDAP_BIND_PASSWORD=your-ldap-password
LDAP_USER_SEARCH_BASE=ou=users,dc=example,dc=com
LDAP_USER_SEARCH_FILTER=(uid={username})

# Document Processing
ENABLE_VIRUS_SCANNING=true
ENABLE_DOCUMENT_OCR=true
ENABLE_DATA_ENCRYPTION=true
MAX_FILE_SIZE_MB=100
ALLOWED_FILE_TYPES=pdf,docx,pptx,txt,md,html,csv,xlsx

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=60
RATE_LIMIT_BURST_SIZE=10

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE=./logs/knowledgebot.log

# Monitoring & Observability
ENABLE_METRICS=true
METRICS_PORT=9090
ENABLE_TRACING=false
JAEGER_ENDPOINT=http://localhost:14268/api/traces

# Background Tasks
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2

# External Integrations
SLACK_BOT_TOKEN=xoxb-your-slack-bot-token
SLACK_SIGNING_SECRET=your-slack-signing-secret

TEAMS_APP_ID=your-teams-app-id
TEAMS_APP_PASSWORD=your-teams-app-password

# Analytics
GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX
MIXPANEL_TOKEN=your-mixpanel-token

# Feature Flags
ENABLE_ANALYTICS=true
ENABLE_NOTIFICATIONS=true
ENABLE_REAL_TIME_UPDATES=true
ENABLE_ADVANCED_SEARCH=true

# Development/Testing
TESTING=false
MOCK_EXTERNAL_SERVICES=false
