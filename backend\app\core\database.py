"""
KnowledgeBot - Corporate Memory System
Database Configuration and Connection Management

This module handles database connections, session management, and table creation
for the PostgreSQL database with SQLAlchemy ORM.
"""

import logging
from typing import AsyncGenerator, Optional
from contextlib import asynccontextmanager

from sqlalchemy import create_engine, MetaData, event
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool

from app.core.config import settings

logger = logging.getLogger(__name__)

# Database metadata and base class
metadata = MetaData()
Base = declarative_base(metadata=metadata)

# Async engine for main database operations
async_engine = create_async_engine(
    str(settings.DATABASE_URL).replace("postgresql://", "postgresql+asyncpg://"),
    pool_size=settings.DATABASE_POOL_SIZE,
    max_overflow=settings.DATABASE_MAX_OVERFLOW,
    pool_pre_ping=True,
    echo=settings.DEBUG,
)

# Sync engine for migrations and admin operations
engine = create_engine(
    str(settings.DATABASE_URL),
    pool_size=settings.DATABASE_POOL_SIZE,
    max_overflow=settings.DATABASE_MAX_OVERFLOW,
    pool_pre_ping=True,
    echo=settings.DEBUG,
)

# Session factories
AsyncSessionLocal = async_sessionmaker(
    async_engine,
    class_=AsyncSession,
    expire_on_commit=False,
    autocommit=False,
    autoflush=False,
)

SessionLocal = sessionmaker(
    engine,
    autocommit=False,
    autoflush=False,
)


class DatabaseManager:
    """
    Database manager for handling connections and transactions.
    """
    
    def __init__(self):
        self.async_engine = async_engine
        self.engine = engine
    
    @asynccontextmanager
    async def get_async_session(self) -> AsyncGenerator[AsyncSession, None]:
        """
        Get async database session with automatic cleanup.
        
        Yields:
            AsyncSession: Database session
        """
        async with AsyncSessionLocal() as session:
            try:
                yield session
                await session.commit()
            except Exception:
                await session.rollback()
                raise
            finally:
                await session.close()
    
    @asynccontextmanager
    def get_sync_session(self) -> Session:
        """
        Get sync database session with automatic cleanup.
        
        Yields:
            Session: Database session
        """
        session = SessionLocal()
        try:
            yield session
            session.commit()
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()
    
    async def health_check(self) -> bool:
        """
        Check database connectivity.
        
        Returns:
            bool: True if database is accessible
        """
        try:
            async with self.get_async_session() as session:
                await session.execute("SELECT 1")
                return True
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return False


# Global database manager instance
db_manager = DatabaseManager()


async def get_async_db() -> AsyncGenerator[AsyncSession, None]:
    """
    Dependency for getting async database session.
    
    Yields:
        AsyncSession: Database session
    """
    async with AsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()


def get_sync_db() -> Session:
    """
    Dependency for getting sync database session.
    
    Returns:
        Session: Database session
    """
    return SessionLocal()


async def create_tables() -> None:
    """
    Create all database tables.
    
    This function is called during application startup.
    """
    try:
        # Import all models to ensure they're registered
        from app.models import user, document, knowledge, audit  # noqa
        
        async with async_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        logger.info("Database tables created successfully")
    except Exception as e:
        logger.error(f"Failed to create database tables: {e}")
        raise


async def drop_tables() -> None:
    """
    Drop all database tables.
    
    WARNING: This will delete all data!
    """
    try:
        async with async_engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)
        
        logger.info("Database tables dropped successfully")
    except Exception as e:
        logger.error(f"Failed to drop database tables: {e}")
        raise


# Database event listeners for logging and monitoring
@event.listens_for(engine, "connect")
def set_sqlite_pragma(dbapi_connection, connection_record):
    """
    Set database connection parameters.
    
    Args:
        dbapi_connection: Database connection
        connection_record: Connection record
    """
    if "sqlite" in str(settings.DATABASE_URL):
        cursor = dbapi_connection.cursor()
        cursor.execute("PRAGMA foreign_keys=ON")
        cursor.close()


@event.listens_for(engine, "before_cursor_execute")
def receive_before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    """
    Log SQL queries in debug mode.
    
    Args:
        conn: Database connection
        cursor: Database cursor
        statement: SQL statement
        parameters: Query parameters
        context: Execution context
        executemany: Whether executing many statements
    """
    if settings.DEBUG:
        logger.debug(f"SQL Query: {statement}")
        if parameters:
            logger.debug(f"Parameters: {parameters}")


class DatabaseTransaction:
    """
    Context manager for database transactions with rollback support.
    """
    
    def __init__(self, session: AsyncSession):
        self.session = session
        self._transaction = None
    
    async def __aenter__(self):
        self._transaction = await self.session.begin()
        return self.session
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if exc_type is not None:
            await self._transaction.rollback()
        else:
            await self._transaction.commit()


class ReadOnlySession:
    """
    Read-only database session for queries that don't modify data.
    """
    
    def __init__(self, session: AsyncSession):
        self.session = session
    
    async def __aenter__(self):
        # Set session to read-only mode
        await self.session.execute("SET TRANSACTION READ ONLY")
        return self.session
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        # Session cleanup is handled by the session factory
        pass


# Utility functions for common database operations
async def execute_raw_query(query: str, parameters: Optional[dict] = None) -> list:
    """
    Execute raw SQL query and return results.
    
    Args:
        query: SQL query string
        parameters: Query parameters
        
    Returns:
        list: Query results
    """
    async with AsyncSessionLocal() as session:
        result = await session.execute(query, parameters or {})
        return result.fetchall()


async def get_table_info(table_name: str) -> dict:
    """
    Get information about a database table.
    
    Args:
        table_name: Name of the table
        
    Returns:
        dict: Table information
    """
    query = """
    SELECT column_name, data_type, is_nullable, column_default
    FROM information_schema.columns
    WHERE table_name = :table_name
    ORDER BY ordinal_position
    """
    
    async with AsyncSessionLocal() as session:
        result = await session.execute(query, {"table_name": table_name})
        columns = result.fetchall()
        
        return {
            "table_name": table_name,
            "columns": [
                {
                    "name": col[0],
                    "type": col[1],
                    "nullable": col[2] == "YES",
                    "default": col[3]
                }
                for col in columns
            ]
        }
