# KnowledgeBot - Corporate Memory System
# Environment Configuration Template
# 
# Copy this file to .env and update the values for your environment

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================

# Environment (development, staging, production)
ENVIRONMENT=development

# Project Information
PROJECT_NAME="KnowledgeBot - Corporate Memory System"
VERSION=1.0.0
API_V1_STR=/api/v1

# Debug Mode
DEBUG=true

# =============================================================================
# SECURITY SETTINGS
# =============================================================================

# JWT Configuration
JWT_SECRET_KEY=your-super-secret-jwt-key-change-this-in-production
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# Encryption Key for sensitive data
ENCRYPTION_KEY=your-encryption-key-32-characters-long

# CORS Origins (comma-separated)
CORS_ORIGINS=http://localhost:3000,http://localhost:8080

# Allowed Hosts (comma-separated)
ALLOWED_HOSTS=localhost,127.0.0.1

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# PostgreSQL Database
POSTGRES_SERVER=localhost
POSTGRES_USER=knowledgebot
POSTGRES_PASSWORD=password
POSTGRES_DB=knowledgebot
POSTGRES_PORT=5432

# Full Database URL (optional - will be constructed from above if not provided)
# DATABASE_URL=postgresql://knowledgebot:password@localhost:5432/knowledgebot

# Database Pool Settings
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30
QUERY_TIMEOUT=30

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================

# Redis Cache and Message Broker
REDIS_URL=redis://localhost:6379/0
REDIS_CACHE_TTL=3600

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2

# =============================================================================
# MEMVID CONFIGURATION (Open Source - No API Key Required)
# =============================================================================

# MemVid Local Model Settings
MEMVID_MODEL_PATH=./models/memvid
MEMVID_MODEL_NAME=sentence-transformers/all-MiniLM-L6-v2
MEMVID_MAX_CHUNK_SIZE=1000
MEMVID_OVERLAP_SIZE=200
MEMVID_BATCH_SIZE=32
MEMVID_DEVICE=cpu

# =============================================================================
# VECTOR DATABASE CONFIGURATION
# =============================================================================

# Vector Database Type (pinecone, weaviate, qdrant)
VECTOR_DB_TYPE=pinecone

# Pinecone Configuration
PINECONE_API_KEY=your-pinecone-api-key
PINECONE_ENVIRONMENT=us-west1-gcp
PINECONE_INDEX_NAME=knowledgebot
PINECONE_DIMENSION=1536

# Weaviate Configuration (Alternative)
WEAVIATE_URL=http://localhost:8080
WEAVIATE_API_KEY=your-weaviate-api-key

# Qdrant Configuration (Alternative)
QDRANT_URL=http://localhost:6333
QDRANT_API_KEY=your-qdrant-api-key

# =============================================================================
# FILE STORAGE CONFIGURATION
# =============================================================================

# Storage Type (s3, minio, local)
STORAGE_TYPE=s3

# S3/MinIO Configuration
S3_BUCKET_NAME=knowledgebot-documents
S3_ACCESS_KEY=your-s3-access-key
S3_SECRET_KEY=your-s3-secret-key
S3_ENDPOINT_URL=http://localhost:9000
S3_REGION=us-east-1

# Local Storage Configuration
LOCAL_STORAGE_PATH=./storage/documents

# =============================================================================
# AUTHENTICATION & SSO
# =============================================================================

# Enable SSO
ENABLE_SSO=false

# SAML Configuration
SAML_ENTITY_ID=knowledgebot
SAML_SSO_URL=https://your-idp.com/sso
SAML_SLO_URL=https://your-idp.com/slo
SAML_X509_CERT=your-saml-certificate
SAML_PRIVATE_KEY=your-saml-private-key

# OAuth Configuration
OAUTH_CLIENT_ID=your-oauth-client-id
OAUTH_CLIENT_SECRET=your-oauth-client-secret
OAUTH_AUTHORIZATION_URL=https://your-oauth-provider.com/oauth/authorize
OAUTH_TOKEN_URL=https://your-oauth-provider.com/oauth/token
OAUTH_USER_INFO_URL=https://your-oauth-provider.com/oauth/userinfo

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================

# SMTP Settings
SMTP_TLS=true
SMTP_PORT=587
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-email-password
EMAILS_FROM_EMAIL=<EMAIL>
EMAILS_FROM_NAME="KnowledgeBot System"

# Email Features
ENABLE_EMAIL_VERIFICATION=true
EMAIL_RESET_TOKEN_EXPIRE_HOURS=48

# =============================================================================
# SLACK INTEGRATION
# =============================================================================

# Slack Bot Configuration
SLACK_BOT_TOKEN=xoxb-your-slack-bot-token
SLACK_SIGNING_SECRET=your-slack-signing-secret
SLACK_APP_TOKEN=xapp-your-slack-app-token

# =============================================================================
# MICROSOFT TEAMS INTEGRATION
# =============================================================================

# Teams Bot Configuration
TEAMS_APP_ID=your-teams-app-id
TEAMS_APP_PASSWORD=your-teams-app-password
TEAMS_TENANT_ID=your-teams-tenant-id

# =============================================================================
# RATE LIMITING
# =============================================================================

# Rate Limiting Settings
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# =============================================================================
# FILE UPLOAD SETTINGS
# =============================================================================

# File Upload Limits
MAX_FILE_SIZE=52428800  # 50MB in bytes
ALLOWED_FILE_TYPES=application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.ms-powerpoint,application/vnd.openxmlformats-officedocument.presentationml.presentation,text/html,text/plain,text/markdown

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Logging Settings
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE=./logs/knowledgebot.log

# =============================================================================
# MONITORING & OBSERVABILITY
# =============================================================================

# Metrics and Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090
ENABLE_TRACING=false
JAEGER_ENDPOINT=http://localhost:14268/api/traces

# Sentry Error Tracking
SENTRY_DSN=your-sentry-dsn

# =============================================================================
# SEARCH CONFIGURATION
# =============================================================================

# Elasticsearch Configuration
ELASTICSEARCH_URL=http://localhost:9200
ELASTICSEARCH_INDEX=knowledgebot

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Feature Toggles
ENABLE_DOCUMENT_OCR=true
ENABLE_REAL_TIME_SEARCH=true
ENABLE_ANALYTICS=true
ENABLE_VIRUS_SCANNING=true
ALLOW_USER_REGISTRATION=false

# =============================================================================
# AUDIT & COMPLIANCE
# =============================================================================

# Audit Settings
AUDIT_LOG_RETENTION_DAYS=2555  # 7 years
ENABLE_DATA_ENCRYPTION=true

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Development Tools (only for development environment)
ENABLE_API_DOCS=true
ENABLE_QUERY_DEVTOOLS=true

# =============================================================================
# FRONTEND CONFIGURATION
# =============================================================================

# React App Settings
REACT_APP_API_URL=http://localhost:8000/api/v1
REACT_APP_ENVIRONMENT=development
REACT_APP_SENTRY_DSN=your-frontend-sentry-dsn
REACT_APP_ANALYTICS_ID=your-analytics-id

# =============================================================================
# KUBERNETES SECRETS (for production deployment)
# =============================================================================

# These should be set as Kubernetes secrets in production
# K8S_DATABASE_URL=postgresql://user:pass@host:port/db
# K8S_REDIS_URL=redis://host:port/db
# K8S_JWT_SECRET_KEY=secret
# K8S_PINECONE_API_KEY=key
# K8S_S3_ACCESS_KEY=key
# K8S_S3_SECRET_KEY=secret
