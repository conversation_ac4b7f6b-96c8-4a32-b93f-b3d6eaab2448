#!/bin/sh
# KnowledgeBot Frontend Docker Entrypoint
# Handles environment variable injection and nginx startup

set -e

# Function to replace environment variables in built files
replace_env_vars() {
    echo "Replacing environment variables in built files..."
    
    # Find all JS files in the build directory
    find /usr/share/nginx/html -name "*.js" -type f -exec sh -c '
        for file do
            # Replace environment variables
            sed -i "s|VITE_API_BASE_URL_PLACEHOLDER|${VITE_API_BASE_URL:-http://localhost:8000/api/v1}|g" "$file"
            sed -i "s|VITE_APP_VERSION_PLACEHOLDER|${VITE_APP_VERSION:-1.0.0}|g" "$file"
            sed -i "s|VITE_APP_ENVIRONMENT_PLACEHOLDER|${VITE_APP_ENVIRONMENT:-production}|g" "$file"
        done
    ' sh {} +
    
    echo "Environment variables replaced successfully"
}

# Function to validate environment variables
validate_env() {
    echo "Validating environment variables..."
    
    # Check required environment variables
    if [ -z "$VITE_API_BASE_URL" ]; then
        echo "Warning: VITE_API_BASE_URL not set, using default: http://localhost:8000/api/v1"
        export VITE_API_BASE_URL="http://localhost:8000/api/v1"
    fi
    
    echo "Environment validation complete"
}

# Function to setup nginx
setup_nginx() {
    echo "Setting up nginx..."
    
    # Create necessary directories
    mkdir -p /var/cache/nginx/client_temp
    mkdir -p /var/cache/nginx/proxy_temp
    mkdir -p /var/cache/nginx/fastcgi_temp
    mkdir -p /var/cache/nginx/uwsgi_temp
    mkdir -p /var/cache/nginx/scgi_temp
    
    # Set permissions
    chmod -R 755 /var/cache/nginx
    
    echo "Nginx setup complete"
}

# Function to test nginx configuration
test_nginx() {
    echo "Testing nginx configuration..."
    nginx -t
    echo "Nginx configuration test passed"
}

# Main execution
main() {
    echo "Starting KnowledgeBot Frontend..."
    echo "Environment: ${VITE_APP_ENVIRONMENT:-production}"
    echo "API Base URL: ${VITE_API_BASE_URL:-http://localhost:8000/api/v1}"
    echo "App Version: ${VITE_APP_VERSION:-1.0.0}"
    
    # Run setup functions
    validate_env
    replace_env_vars
    setup_nginx
    test_nginx
    
    echo "Frontend initialization complete"
    echo "Starting nginx..."
    
    # Execute the main command
    exec "$@"
}

# Run main function
main "$@"
