# KnowledgeBot Frontend

Modern, responsive React frontend for the KnowledgeBot Corporate Memory System. Built with TypeScript, Tailwind CSS, and enterprise-grade architecture for scalability and maintainability.

## 🚀 Features

### 🎨 Modern UI/UX
- **Responsive Design**: Mobile-first approach with seamless desktop experience
- **Dark/Light Mode**: Automatic system preference detection with manual toggle
- **Accessibility**: WCAG 2.1 AA compliant with screen reader support
- **Professional Design**: Clean, modern interface optimized for enterprise use

### 🔐 Authentication & Security
- **JWT Token Management**: Secure authentication with automatic refresh
- **Role-Based Access Control**: Granular permissions and route protection
- **Session Management**: Multiple session support with security monitoring
- **Two-Factor Authentication**: TOTP, SMS, and email 2FA support

### 📄 Document Management
- **Drag & Drop Upload**: Intuitive file upload with progress tracking
- **Multi-format Support**: PDF, DOCX, PPTX, images, and more
- **Document Viewer**: Built-in viewer with metadata display
- **Bulk Operations**: Efficient batch processing capabilities

### 🧠 Knowledge Interface
- **Natural Language Queries**: Intuitive question-asking interface
- **Real-time Search**: Instant search with suggestions and filters
- **Source Citations**: Transparent answer sourcing with confidence scores
- **Query History**: Save and revisit previous queries

### 📊 Analytics Dashboard
- **Usage Metrics**: Comprehensive usage analytics and insights
- **Interactive Charts**: Beautiful visualizations with Recharts
- **Export Functionality**: Data export in multiple formats
- **Real-time Updates**: Live metrics and notifications

### ⚙️ Admin Panel
- **User Management**: Complete user lifecycle management
- **Role & Permission Management**: Flexible RBAC configuration
- **System Health Monitoring**: Real-time system status and metrics
- **Audit Logging**: Comprehensive activity tracking

## 🛠️ Technology Stack

- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite for fast development and optimized builds
- **Styling**: Tailwind CSS with custom design system
- **State Management**: Zustand for global state, React Query for server state
- **Routing**: React Router v6 with protected routes
- **Forms**: React Hook Form with Zod validation
- **UI Components**: Headless UI with custom components
- **Icons**: Heroicons and Lucide React
- **Charts**: Recharts for data visualization
- **HTTP Client**: Axios with interceptors and error handling
- **Testing**: Vitest with React Testing Library
- **Code Quality**: ESLint, Prettier, TypeScript strict mode

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ and npm 9+
- Backend API running on port 8000

### Development Setup

1. **Clone and install dependencies**:
```bash
cd frontend
npm install
```

2. **Configure environment**:
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. **Start development server**:
```bash
npm run dev
```

4. **Open in browser**:
```
http://localhost:3000
```

### Available Scripts

```bash
# Development
npm run dev              # Start development server
npm run build           # Build for production
npm run preview         # Preview production build

# Code Quality
npm run lint            # Run ESLint
npm run lint:fix        # Fix ESLint issues
npm run type-check      # TypeScript type checking
npm run format          # Format code with Prettier
npm run format:check    # Check code formatting

# Testing
npm run test            # Run tests
npm run test:ui         # Run tests with UI
npm run test:coverage   # Run tests with coverage
```

## 🏗️ Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── common/         # Generic components (buttons, modals, etc.)
│   ├── layout/         # Layout components (header, sidebar, etc.)
│   └── auth/           # Authentication components
├── pages/              # Page components
│   ├── auth/           # Authentication pages
│   ├── dashboard/      # Dashboard pages
│   ├── documents/      # Document management pages
│   ├── knowledge/      # Knowledge query pages
│   ├── admin/          # Admin panel pages
│   └── error/          # Error pages
├── hooks/              # Custom React hooks
├── services/           # API services and HTTP client
├── store/              # Global state management
├── types/              # TypeScript type definitions
├── utils/              # Utility functions
├── contexts/           # React contexts
└── assets/             # Static assets
```

## 🎨 Design System

### Colors
- **Primary**: Blue (#3b82f6) - Main brand color
- **Secondary**: Gray (#64748b) - Supporting elements
- **Success**: Green (#22c55e) - Success states
- **Warning**: Yellow (#f59e0b) - Warning states
- **Error**: Red (#ef4444) - Error states

### Typography
- **Font Family**: Inter (system fallback: system-ui, sans-serif)
- **Mono Font**: JetBrains Mono (fallback: Consolas, monospace)
- **Scale**: Tailwind's default type scale

### Spacing
- **Base Unit**: 4px (0.25rem)
- **Scale**: 4px, 8px, 12px, 16px, 20px, 24px, 32px, 40px, 48px, 64px

### Components
- **Buttons**: Multiple variants (primary, secondary, ghost, danger)
- **Forms**: Consistent input styling with validation states
- **Cards**: Flexible card components with headers and footers
- **Navigation**: Responsive sidebar and header navigation

## 🔧 Configuration

### Environment Variables

```bash
# API Configuration
VITE_API_BASE_URL=http://localhost:8000/api/v1

# Application Configuration
VITE_APP_NAME=KnowledgeBot
VITE_APP_VERSION=1.0.0
VITE_APP_ENVIRONMENT=development

# Feature Flags
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_DARK_MODE=true
VITE_ENABLE_NOTIFICATIONS=true

# Theme Configuration
VITE_DEFAULT_THEME=auto
VITE_PRIMARY_COLOR=#3b82f6
```

### Build Configuration

The application uses Vite for building with the following optimizations:

- **Code Splitting**: Automatic route-based and vendor splitting
- **Tree Shaking**: Dead code elimination
- **Asset Optimization**: Image and font optimization
- **Bundle Analysis**: Built-in bundle analyzer
- **TypeScript**: Full TypeScript support with strict mode

## 🐳 Docker Deployment

### Development
```bash
# Build and run development container
docker-compose up frontend-dev

# Access at http://localhost:3001
```

### Production
```bash
# Build production image
docker build -t knowledgebot-frontend .

# Run production container
docker run -p 3000:3000 \
  -e VITE_API_BASE_URL=https://api.yourdomain.com/api/v1 \
  knowledgebot-frontend
```

### Docker Compose
```bash
# Full stack deployment
docker-compose up -d

# Frontend only
docker-compose up frontend
```

## 🧪 Testing

### Unit Tests
```bash
# Run all tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

### E2E Tests
```bash
# Run Playwright tests (if configured)
npm run test:e2e
```

### Testing Strategy
- **Unit Tests**: Component logic and utility functions
- **Integration Tests**: API integration and user flows
- **Accessibility Tests**: Screen reader and keyboard navigation
- **Visual Regression**: Component visual consistency

## 🚀 Performance

### Optimization Features
- **Lazy Loading**: Route-based code splitting
- **Image Optimization**: Automatic image optimization
- **Caching**: Aggressive caching strategies
- **Bundle Splitting**: Vendor and route-based splitting
- **Tree Shaking**: Dead code elimination
- **Compression**: Gzip and Brotli compression

### Performance Metrics
- **First Contentful Paint**: < 1.5s
- **Largest Contentful Paint**: < 2.5s
- **Cumulative Layout Shift**: < 0.1
- **First Input Delay**: < 100ms

## 🔒 Security

### Security Features
- **Content Security Policy**: Strict CSP headers
- **XSS Protection**: Input sanitization and output encoding
- **CSRF Protection**: SameSite cookies and CSRF tokens
- **Secure Headers**: Security headers via nginx
- **Token Security**: Secure JWT token handling

### Security Best Practices
- **Input Validation**: Client and server-side validation
- **Error Handling**: Secure error messages
- **Dependency Scanning**: Regular security audits
- **HTTPS Only**: Enforce HTTPS in production

## 📱 Browser Support

### Supported Browsers
- **Chrome**: 90+
- **Firefox**: 88+
- **Safari**: 14+
- **Edge**: 90+

### Progressive Enhancement
- **Core Functionality**: Works without JavaScript
- **Enhanced Experience**: Full features with JavaScript
- **Offline Support**: Service worker for offline functionality

## 🌐 Internationalization

### i18n Support
- **Multiple Languages**: English (default), with framework for additional languages
- **RTL Support**: Right-to-left language support
- **Date/Time Formatting**: Locale-aware formatting
- **Number Formatting**: Currency and number localization

## 📊 Analytics

### Built-in Analytics
- **User Behavior**: Page views, user interactions
- **Performance Metrics**: Core Web Vitals tracking
- **Error Tracking**: Client-side error monitoring
- **Custom Events**: Business-specific event tracking

## 🔧 Development

### Code Quality
- **TypeScript**: Strict type checking
- **ESLint**: Comprehensive linting rules
- **Prettier**: Consistent code formatting
- **Husky**: Git hooks for quality gates
- **Commitlint**: Conventional commit messages

### Development Workflow
1. **Feature Branch**: Create feature branch from main
2. **Development**: Implement feature with tests
3. **Quality Check**: Run linting, formatting, and tests
4. **Code Review**: Submit pull request for review
5. **Deployment**: Merge and deploy to staging/production

## 🚀 Deployment

### Production Deployment
1. **Build**: `npm run build`
2. **Test**: `npm run test`
3. **Docker**: Build and deploy container
4. **CDN**: Deploy static assets to CDN
5. **Monitor**: Monitor performance and errors

### Environment-Specific Builds
- **Development**: Source maps, dev tools enabled
- **Staging**: Production build with debug info
- **Production**: Optimized build, minified, compressed

## 📚 Documentation

### Additional Resources
- **Component Storybook**: Interactive component documentation
- **API Documentation**: OpenAPI/Swagger integration
- **User Guide**: End-user documentation
- **Admin Guide**: Administrator documentation

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run quality checks
6. Submit a pull request

### Coding Standards
- Follow TypeScript best practices
- Use functional components with hooks
- Implement proper error boundaries
- Write comprehensive tests
- Follow accessibility guidelines

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

- **Documentation**: Check the `/docs` directory
- **Issues**: GitHub Issues for bug reports
- **Discussions**: GitHub Discussions for questions
- **Security**: Report security issues privately

---

Built with ❤️ by the KnowledgeBot team
