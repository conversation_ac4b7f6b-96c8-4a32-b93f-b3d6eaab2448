"""
KnowledgeBot - Corporate Memory System
Knowledge Query API Endpoints

This module handles knowledge queries, search, and AI-powered responses
using MemVid encoding and vector similarity search.
"""

from typing import List, Dict, Any, Optional
from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException, status, Query, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, desc
from sqlalchemy.orm import selectinload

from app.api.deps import get_current_user, get_db, require_permissions
from app.models.user import User
from app.models.document import Document, DocumentChunk, DocumentAccess
from app.schemas.knowledge import (
    KnowledgeQuery,
    KnowledgeResponse,
    SearchQuery,
    SearchResponse,
    SearchSuggestion,
    DocumentSummary,
    RelatedDocument
)
from app.services.memvid_service import MemVidService
from app.services.search_service import SearchService
from app.services.analytics_service import AnalyticsService

router = APIRouter()
memvid_service = MemVidService()
search_service = SearchService()
analytics_service = AnalyticsService()


@router.post("/query", response_model=KnowledgeResponse)
async def query_knowledge(
    query: KnowledgeQuery,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    _: None = Depends(require_permissions(["knowledge.query"]))
) -> Dict[str, Any]:
    """
    Query the knowledge base using natural language.
    
    Args:
        query: Knowledge query request
        background_tasks: FastAPI background tasks
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Dict[str, Any]: AI-generated response with source citations
        
    Raises:
        HTTPException: If query fails
    """
    if not query.question.strip():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Query question cannot be empty"
        )
    
    try:
        # Get user's accessible departments for filtering
        user_departments = [dept.id for dept in current_user.departments]
        
        # Generate query embedding
        query_embedding = await memvid_service.generate_embedding(query.question)
        
        # Perform vector similarity search
        relevant_chunks = await search_service.vector_similarity_search(
            db=db,
            query_embedding=query_embedding,
            user_departments=user_departments,
            limit=query.max_results or 10,
            similarity_threshold=query.similarity_threshold or 0.7,
            filters={
                "department_ids": query.department_ids,
                "document_types": query.document_types,
                "classification_levels": query.classification_levels,
                "date_range": query.date_range
            }
        )
        
        if not relevant_chunks:
            return {
                "answer": "I couldn't find any relevant information to answer your question. Please try rephrasing your query or check if you have access to the relevant documents.",
                "confidence_score": 0.0,
                "sources": [],
                "related_queries": await search_service.get_related_queries(query.question),
                "query_id": None
            }
        
        # Generate contextual answer using MemVid
        context_text = "\n\n".join([
            f"Source: {chunk.document.title}\n{chunk.content}"
            for chunk in relevant_chunks
        ])
        
        ai_response = await memvid_service.generate_answer(
            question=query.question,
            context=context_text,
            max_length=query.max_answer_length or 500
        )
        
        # Prepare source citations
        sources = []
        seen_documents = set()
        
        for chunk in relevant_chunks:
            if chunk.document.id not in seen_documents:
                sources.append({
                    "document_id": chunk.document.id,
                    "title": chunk.document.title,
                    "category": chunk.document.category,
                    "relevance_score": chunk.similarity_score,
                    "excerpt": chunk.content[:200] + "..." if len(chunk.content) > 200 else chunk.content,
                    "page_number": chunk.start_page,
                    "url": f"/api/v1/documents/{chunk.document.id}"
                })
                seen_documents.add(chunk.document.id)
        
        # Log query for analytics
        query_log = DocumentAccess(
            user_id=current_user.id,
            access_type="query",
            access_method="api",
            query_text=query.question
        )
        db.add(query_log)
        await db.commit()
        
        # Schedule analytics update in background
        background_tasks.add_task(
            analytics_service.update_query_analytics,
            query.question,
            len(sources),
            ai_response.confidence_score
        )
        
        return {
            "answer": ai_response.answer,
            "confidence_score": ai_response.confidence_score,
            "sources": sources,
            "related_queries": await search_service.get_related_queries(query.question),
            "query_id": str(query_log.id),
            "processing_time": ai_response.processing_time
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to process knowledge query: {str(e)}"
        )


@router.get("/search", response_model=SearchResponse)
async def search_documents(
    q: str = Query(..., description="Search query"),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    category: Optional[str] = Query(None),
    document_type: Optional[str] = Query(None),
    classification_level: Optional[str] = Query(None),
    department_ids: Optional[List[UUID]] = Query(None),
    date_from: Optional[str] = Query(None),
    date_to: Optional[str] = Query(None),
    sort_by: str = Query("relevance", regex="^(relevance|date|title)$"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    _: None = Depends(require_permissions(["knowledge.search"]))
) -> Dict[str, Any]:
    """
    Search documents using full-text and semantic search.
    
    Args:
        q: Search query
        skip: Number of results to skip
        limit: Maximum number of results
        category: Filter by document category
        document_type: Filter by document type
        classification_level: Filter by classification level
        department_ids: Filter by department IDs
        date_from: Filter by creation date (from)
        date_to: Filter by creation date (to)
        sort_by: Sort results by relevance, date, or title
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Dict[str, Any]: Search results with metadata
    """
    if not q.strip():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Search query cannot be empty"
        )
    
    try:
        # Get user's accessible departments
        user_departments = [dept.id for dept in current_user.departments]
        
        # Perform hybrid search (full-text + semantic)
        search_results = await search_service.hybrid_search(
            db=db,
            query=q,
            user_departments=user_departments,
            skip=skip,
            limit=limit,
            filters={
                "category": category,
                "document_type": document_type,
                "classification_level": classification_level,
                "department_ids": department_ids,
                "date_from": date_from,
                "date_to": date_to
            },
            sort_by=sort_by
        )
        
        # Log search for analytics
        search_log = DocumentAccess(
            user_id=current_user.id,
            access_type="search",
            access_method="api",
            query_text=q
        )
        db.add(search_log)
        await db.commit()
        
        return {
            "results": search_results.results,
            "total": search_results.total,
            "skip": skip,
            "limit": limit,
            "query": q,
            "search_time": search_results.search_time,
            "suggestions": search_results.suggestions,
            "facets": search_results.facets
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Search failed: {str(e)}"
        )


@router.get("/suggestions", response_model=List[SearchSuggestion])
async def get_search_suggestions(
    q: str = Query(..., min_length=2, description="Partial search query"),
    limit: int = Query(10, ge=1, le=20),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    _: None = Depends(require_permissions(["knowledge.search"]))
) -> List[Dict[str, Any]]:
    """
    Get search suggestions for autocomplete.
    
    Args:
        q: Partial search query
        limit: Maximum number of suggestions
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        List[Dict[str, Any]]: Search suggestions
    """
    try:
        suggestions = await search_service.get_search_suggestions(
            db=db,
            partial_query=q,
            user_departments=[dept.id for dept in current_user.departments],
            limit=limit
        )
        
        return suggestions
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get suggestions: {str(e)}"
        )


@router.get("/related/{document_id}", response_model=List[RelatedDocument])
async def get_related_documents(
    document_id: UUID,
    limit: int = Query(5, ge=1, le=20),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    _: None = Depends(require_permissions(["knowledge.read"]))
) -> List[Dict[str, Any]]:
    """
    Get documents related to a specific document.
    
    Args:
        document_id: Source document ID
        limit: Maximum number of related documents
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        List[Dict[str, Any]]: Related documents
        
    Raises:
        HTTPException: If document not found or access denied
    """
    # Check if user has access to the source document
    stmt = select(Document).where(
        Document.id == document_id,
        Document.status == "active"
    )
    result = await db.execute(stmt)
    source_document = result.scalar_one_or_none()
    
    if not source_document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )
    
    # Check access permissions
    user_departments = [dept.id for dept in current_user.departments]
    if not (source_document.is_public or 
            source_document.uploaded_by == current_user.id or
            any(dept.id in user_departments for dept in source_document.departments)):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to this document"
        )
    
    try:
        related_docs = await search_service.find_related_documents(
            db=db,
            document_id=document_id,
            user_departments=user_departments,
            limit=limit
        )
        
        return related_docs
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to find related documents: {str(e)}"
        )


@router.post("/summarize/{document_id}", response_model=DocumentSummary)
async def summarize_document(
    document_id: UUID,
    max_length: int = Query(300, ge=50, le=1000),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    _: None = Depends(require_permissions(["knowledge.read"]))
) -> Dict[str, Any]:
    """
    Generate AI summary of a document.
    
    Args:
        document_id: Document ID to summarize
        max_length: Maximum summary length
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Dict[str, Any]: Document summary
        
    Raises:
        HTTPException: If document not found or access denied
    """
    # Get document with access check
    stmt = select(Document).options(
        selectinload(Document.chunks)
    ).where(
        Document.id == document_id,
        Document.status == "active",
        Document.processing_status == "completed"
    )
    result = await db.execute(stmt)
    document = result.scalar_one_or_none()
    
    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found or not processed"
        )
    
    # Check access permissions
    user_departments = [dept.id for dept in current_user.departments]
    if not (document.is_public or 
            document.uploaded_by == current_user.id or
            any(dept.id in user_departments for dept in document.departments)):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to this document"
        )
    
    try:
        # Generate summary using MemVid
        full_content = "\n\n".join([chunk.content for chunk in document.chunks])
        summary = await memvid_service.generate_summary(
            content=full_content,
            max_length=max_length,
            document_title=document.title
        )
        
        # Log access
        access_log = DocumentAccess(
            document_id=document_id,
            user_id=current_user.id,
            access_type="summarize",
            access_method="api"
        )
        db.add(access_log)
        await db.commit()
        
        return {
            "document_id": document_id,
            "title": document.title,
            "summary": summary.text,
            "key_points": summary.key_points,
            "confidence_score": summary.confidence_score,
            "word_count": len(summary.text.split()),
            "generated_at": summary.generated_at
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate summary: {str(e)}"
        )


@router.get("/analytics/popular-queries")
async def get_popular_queries(
    limit: int = Query(10, ge=1, le=50),
    days: int = Query(30, ge=1, le=365),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    _: None = Depends(require_permissions(["analytics.read"]))
) -> List[Dict[str, Any]]:
    """
    Get popular search queries and knowledge queries.
    
    Args:
        limit: Maximum number of queries to return
        days: Number of days to look back
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        List[Dict[str, Any]]: Popular queries with usage statistics
    """
    try:
        popular_queries = await analytics_service.get_popular_queries(
            db=db,
            limit=limit,
            days=days,
            user_departments=[dept.id for dept in current_user.departments]
        )
        
        return popular_queries
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get popular queries: {str(e)}"
        )
