"""
KnowledgeBot - Corporate Memory System
Search Service with Enterprise Rate Limiting

This module implements hybrid search capabilities combining vector similarity
and full-text search with comprehensive rate limiting for different user tiers.
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
from uuid import UUID
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, desc, text
from sqlalchemy.orm import selectinload
import numpy as np
from dataclasses import dataclass

from app.models.document import Document, DocumentChunk, DocumentAccess
from app.models.user import User
from app.services.memvid_service import MemVidService
from app.core.config import settings
from app.core.security import rate_limiter

logger = logging.getLogger(__name__)


@dataclass
class SearchResult:
    """Search result data structure."""
    document_id: UUID
    title: str
    content_excerpt: str
    relevance_score: float
    document_type: str
    category: str
    created_at: datetime
    url: str
    metadata: Dict[str, Any]


@dataclass
class SearchResponse:
    """Search response with metadata."""
    results: List[SearchResult]
    total: int
    search_time: float
    suggestions: List[str]
    facets: Dict[str, Any]


class RateLimitTier:
    """Rate limiting tiers for different user types."""
    
    # Queries per minute by user role
    RATE_LIMITS = {
        "guest": {"queries": 5, "searches": 10, "uploads": 1},
        "user": {"queries": 20, "searches": 50, "uploads": 5},
        "premium": {"queries": 100, "searches": 200, "uploads": 20},
        "admin": {"queries": 500, "searches": 1000, "uploads": 100},
        "enterprise": {"queries": 1000, "searches": 2000, "uploads": 500}
    }
    
    @classmethod
    def get_user_tier(cls, user: User) -> str:
        """Determine user's rate limit tier based on roles."""
        if user.is_superuser:
            return "enterprise"
        
        user_roles = [role.name.lower() for role in user.roles]
        
        if "admin" in user_roles:
            return "admin"
        elif "premium" in user_roles:
            return "premium"
        elif "user" in user_roles:
            return "user"
        else:
            return "guest"
    
    @classmethod
    def check_rate_limit(cls, user: User, action: str) -> bool:
        """Check if user can perform action based on rate limits."""
        tier = cls.get_user_tier(user)
        limits = cls.RATE_LIMITS.get(tier, cls.RATE_LIMITS["guest"])
        
        limit = limits.get(action, 5)
        key = f"rate_limit:{action}:{user.id}"
        
        return rate_limiter.is_allowed(key, limit, 60)  # 60 seconds window


class SearchService:
    """
    Enterprise search service with hybrid search and rate limiting.
    """
    
    def __init__(self):
        self.memvid_service = MemVidService()
    
    async def vector_similarity_search(
        self,
        db: AsyncSession,
        query_embedding: List[float],
        user_departments: List[UUID],
        limit: int = 10,
        similarity_threshold: float = 0.7,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[DocumentChunk]:
        """
        Perform vector similarity search with departmental filtering.
        
        Args:
            db: Database session
            query_embedding: Query vector embedding
            user_departments: User's accessible departments
            limit: Maximum results to return
            similarity_threshold: Minimum similarity score
            filters: Additional filters
            
        Returns:
            List[DocumentChunk]: Relevant document chunks
        """
        try:
            # Build base query
            query = select(DocumentChunk).options(
                selectinload(DocumentChunk.document)
            ).join(Document)
            
            # Apply departmental access control
            if user_departments:
                query = query.where(
                    or_(
                        Document.is_public == True,
                        Document.departments.any(lambda d: d.id.in_(user_departments))
                    )
                )
            else:
                query = query.where(Document.is_public == True)
            
            # Apply additional filters
            if filters:
                if filters.get("department_ids"):
                    query = query.where(
                        Document.departments.any(lambda d: d.id.in_(filters["department_ids"]))
                    )
                
                if filters.get("document_types"):
                    query = query.where(Document.document_type.in_(filters["document_types"]))
                
                if filters.get("classification_levels"):
                    query = query.where(Document.classification_level.in_(filters["classification_levels"]))
                
                if filters.get("date_range"):
                    date_range = filters["date_range"]
                    if date_range.get("start"):
                        query = query.where(Document.created_at >= date_range["start"])
                    if date_range.get("end"):
                        query = query.where(Document.created_at <= date_range["end"])
            
            # Execute query
            result = await db.execute(query.limit(limit * 3))  # Get more for similarity filtering
            chunks = result.scalars().all()
            
            # Calculate similarities and filter
            similar_chunks = []
            for chunk in chunks:
                if chunk.memvid_embedding:
                    similarity = await self.memvid_service.calculate_similarity(
                        query_embedding, chunk.memvid_embedding
                    )
                    
                    if similarity >= similarity_threshold:
                        chunk.similarity_score = similarity
                        similar_chunks.append(chunk)
            
            # Sort by similarity and return top results
            similar_chunks.sort(key=lambda x: x.similarity_score, reverse=True)
            return similar_chunks[:limit]
            
        except Exception as e:
            logger.error(f"Error in vector similarity search: {e}")
            return []
    
    async def hybrid_search(
        self,
        db: AsyncSession,
        query: str,
        user_departments: List[UUID],
        skip: int = 0,
        limit: int = 20,
        filters: Optional[Dict[str, Any]] = None,
        sort_by: str = "relevance"
    ) -> SearchResponse:
        """
        Perform hybrid search combining vector and full-text search.
        
        Args:
            db: Database session
            query: Search query
            user_departments: User's accessible departments
            skip: Number of results to skip
            limit: Maximum results to return
            filters: Additional filters
            sort_by: Sort criteria
            
        Returns:
            SearchResponse: Search results with metadata
        """
        start_time = datetime.utcnow()
        
        try:
            # Generate query embedding for vector search
            query_embedding = await self.memvid_service.generate_embedding(query)
            
            # Perform vector similarity search
            vector_results = await self.vector_similarity_search(
                db, query_embedding, user_departments, limit * 2, 0.5, filters
            )
            
            # Perform full-text search
            text_results = await self._full_text_search(
                db, query, user_departments, limit * 2, filters
            )
            
            # Combine and rank results
            combined_results = self._combine_search_results(
                vector_results, text_results, query
            )
            
            # Apply sorting
            if sort_by == "date":
                combined_results.sort(key=lambda x: x.created_at, reverse=True)
            elif sort_by == "title":
                combined_results.sort(key=lambda x: x.title.lower())
            # Default is relevance (already sorted)
            
            # Apply pagination
            total = len(combined_results)
            paginated_results = combined_results[skip:skip + limit]
            
            # Generate suggestions and facets
            suggestions = await self._generate_suggestions(db, query, user_departments)
            facets = await self._generate_facets(db, user_departments, filters)
            
            search_time = (datetime.utcnow() - start_time).total_seconds()
            
            return SearchResponse(
                results=paginated_results,
                total=total,
                search_time=search_time,
                suggestions=suggestions,
                facets=facets
            )
            
        except Exception as e:
            logger.error(f"Error in hybrid search: {e}")
            return SearchResponse(
                results=[],
                total=0,
                search_time=0.0,
                suggestions=[],
                facets={}
            )
    
    async def _full_text_search(
        self,
        db: AsyncSession,
        query: str,
        user_departments: List[UUID],
        limit: int,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Document]:
        """Perform full-text search on documents."""
        try:
            # Build full-text search query
            search_query = select(Document).where(
                and_(
                    Document.status == "active",
                    or_(
                        Document.title.ilike(f"%{query}%"),
                        Document.description.ilike(f"%{query}%"),
                        Document.search_vector.ilike(f"%{query}%")
                    )
                )
            )
            
            # Apply departmental access control
            if user_departments:
                search_query = search_query.where(
                    or_(
                        Document.is_public == True,
                        Document.departments.any(lambda d: d.id.in_(user_departments))
                    )
                )
            else:
                search_query = search_query.where(Document.is_public == True)
            
            # Apply filters (similar to vector search)
            if filters:
                if filters.get("category"):
                    search_query = search_query.where(Document.category == filters["category"])
                if filters.get("document_type"):
                    search_query = search_query.where(Document.document_type == filters["document_type"])
            
            result = await db.execute(search_query.limit(limit))
            return result.scalars().all()
            
        except Exception as e:
            logger.error(f"Error in full-text search: {e}")
            return []
    
    def _combine_search_results(
        self,
        vector_results: List[DocumentChunk],
        text_results: List[Document],
        query: str
    ) -> List[SearchResult]:
        """Combine vector and text search results with relevance scoring."""
        combined = {}
        
        # Process vector results
        for chunk in vector_results:
            doc_id = chunk.document.id
            if doc_id not in combined:
                combined[doc_id] = SearchResult(
                    document_id=doc_id,
                    title=chunk.document.title,
                    content_excerpt=chunk.content[:200] + "...",
                    relevance_score=chunk.similarity_score * 0.7,  # Weight vector similarity
                    document_type=chunk.document.document_type or "document",
                    category=chunk.document.category or "general",
                    created_at=chunk.document.created_at,
                    url=f"/api/v1/documents/{doc_id}",
                    metadata={"source": "vector", "chunk_count": 1}
                )
            else:
                # Boost score for multiple matching chunks
                combined[doc_id].relevance_score += chunk.similarity_score * 0.1
                combined[doc_id].metadata["chunk_count"] += 1
        
        # Process text results
        for doc in text_results:
            doc_id = doc.id
            text_score = self._calculate_text_relevance(doc, query)
            
            if doc_id not in combined:
                combined[doc_id] = SearchResult(
                    document_id=doc_id,
                    title=doc.title,
                    content_excerpt=doc.description[:200] + "..." if doc.description else "",
                    relevance_score=text_score * 0.3,  # Weight text search lower
                    document_type=doc.document_type or "document",
                    category=doc.category or "general",
                    created_at=doc.created_at,
                    url=f"/api/v1/documents/{doc_id}",
                    metadata={"source": "text"}
                )
            else:
                # Boost score for text match
                combined[doc_id].relevance_score += text_score * 0.3
                combined[doc_id].metadata["source"] = "hybrid"
        
        # Sort by relevance score
        results = list(combined.values())
        results.sort(key=lambda x: x.relevance_score, reverse=True)
        
        return results
    
    def _calculate_text_relevance(self, document: Document, query: str) -> float:
        """Calculate text relevance score."""
        query_words = set(query.lower().split())
        
        # Check title matches
        title_words = set(document.title.lower().split())
        title_matches = len(query_words.intersection(title_words))
        
        # Check description matches
        desc_matches = 0
        if document.description:
            desc_words = set(document.description.lower().split())
            desc_matches = len(query_words.intersection(desc_words))
        
        # Calculate score
        total_query_words = len(query_words)
        if total_query_words == 0:
            return 0.0
        
        title_score = (title_matches / total_query_words) * 0.7
        desc_score = (desc_matches / total_query_words) * 0.3
        
        return min(title_score + desc_score, 1.0)
    
    async def _generate_suggestions(
        self,
        db: AsyncSession,
        query: str,
        user_departments: List[UUID],
        limit: int = 5
    ) -> List[str]:
        """Generate search suggestions based on popular queries."""
        try:
            # Get popular queries from access logs
            popular_query = select(
                DocumentAccess.query_text,
                func.count(DocumentAccess.id).label('count')
            ).where(
                and_(
                    DocumentAccess.query_text.isnot(None),
                    DocumentAccess.query_text.ilike(f"%{query}%"),
                    DocumentAccess.accessed_at >= datetime.utcnow() - timedelta(days=30)
                )
            ).group_by(DocumentAccess.query_text).order_by(desc('count')).limit(limit)
            
            result = await db.execute(popular_query)
            suggestions = [row[0] for row in result.fetchall()]
            
            return suggestions
            
        except Exception as e:
            logger.error(f"Error generating suggestions: {e}")
            return []
    
    async def _generate_facets(
        self,
        db: AsyncSession,
        user_departments: List[UUID],
        filters: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Generate search facets for filtering."""
        try:
            facets = {}
            
            # Category facets
            category_query = select(
                Document.category,
                func.count(Document.id).label('count')
            ).where(Document.status == "active").group_by(Document.category)
            
            result = await db.execute(category_query)
            facets["categories"] = [
                {"name": row[0], "count": row[1]} 
                for row in result.fetchall() if row[0]
            ]
            
            # Document type facets
            type_query = select(
                Document.document_type,
                func.count(Document.id).label('count')
            ).where(Document.status == "active").group_by(Document.document_type)
            
            result = await db.execute(type_query)
            facets["document_types"] = [
                {"name": row[0], "count": row[1]} 
                for row in result.fetchall() if row[0]
            ]
            
            return facets
            
        except Exception as e:
            logger.error(f"Error generating facets: {e}")
            return {}
    
    async def get_search_suggestions(
        self,
        db: AsyncSession,
        partial_query: str,
        user_departments: List[UUID],
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """Get autocomplete suggestions for search."""
        try:
            suggestions = []
            
            # Get document titles that match
            title_query = select(Document.title).where(
                and_(
                    Document.status == "active",
                    Document.title.ilike(f"%{partial_query}%")
                )
            ).limit(limit // 2)
            
            result = await db.execute(title_query)
            for title in result.scalars():
                suggestions.append({
                    "text": title,
                    "type": "document_title"
                })
            
            # Get popular queries that match
            popular_query = select(DocumentAccess.query_text).where(
                and_(
                    DocumentAccess.query_text.isnot(None),
                    DocumentAccess.query_text.ilike(f"%{partial_query}%")
                )
            ).distinct().limit(limit // 2)
            
            result = await db.execute(popular_query)
            for query_text in result.scalars():
                suggestions.append({
                    "text": query_text,
                    "type": "popular_query"
                })
            
            return suggestions[:limit]
            
        except Exception as e:
            logger.error(f"Error getting search suggestions: {e}")
            return []
    
    async def find_related_documents(
        self,
        db: AsyncSession,
        document_id: UUID,
        user_departments: List[UUID],
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """Find documents related to a specific document."""
        try:
            # Get the source document
            source_doc = await db.get(Document, document_id)
            if not source_doc:
                return []
            
            # Use document title and category for similarity
            query_text = f"{source_doc.title} {source_doc.category or ''}"
            query_embedding = await self.memvid_service.generate_embedding(query_text)
            
            # Find similar documents
            similar_chunks = await self.vector_similarity_search(
                db, query_embedding, user_departments, limit * 2, 0.6
            )
            
            # Group by document and exclude source document
            related_docs = {}
            for chunk in similar_chunks:
                if chunk.document.id != document_id:
                    doc_id = chunk.document.id
                    if doc_id not in related_docs:
                        related_docs[doc_id] = {
                            "document_id": doc_id,
                            "title": chunk.document.title,
                            "category": chunk.document.category,
                            "similarity_score": chunk.similarity_score,
                            "url": f"/api/v1/documents/{doc_id}"
                        }
            
            # Sort by similarity and return top results
            related_list = list(related_docs.values())
            related_list.sort(key=lambda x: x["similarity_score"], reverse=True)
            
            return related_list[:limit]
            
        except Exception as e:
            logger.error(f"Error finding related documents: {e}")
            return []
