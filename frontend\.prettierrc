{"semi": false, "trailingComma": "es5", "singleQuote": true, "printWidth": 100, "tabWidth": 2, "useTabs": false, "quoteProps": "as-needed", "jsxSingleQuote": false, "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "always", "endOfLine": "lf", "embeddedLanguageFormatting": "auto", "htmlWhitespaceSensitivity": "css", "insertPragma": false, "proseWrap": "preserve", "requirePragma": false, "vueIndentScriptAndStyle": false, "plugins": ["prettier-plugin-tailwindcss"], "tailwindConfig": "./tailwind.config.js", "overrides": [{"files": "*.json", "options": {"printWidth": 80}}, {"files": "*.md", "options": {"printWidth": 80, "proseWrap": "always"}}]}