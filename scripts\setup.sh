#!/bin/bash

# KnowledgeBot - Corporate Memory System
# Setup Script for Local Development
# 
# This script sets up the complete development environment including
# all dependencies, databases, and services.

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check system requirements
check_requirements() {
    log_info "Checking system requirements..."
    
    local missing_deps=()
    
    # Check Docker
    if ! command_exists docker; then
        missing_deps+=("docker")
    fi
    
    # Check Docker Compose
    if ! command_exists docker-compose; then
        missing_deps+=("docker-compose")
    fi
    
    # Check Node.js
    if ! command_exists node; then
        missing_deps+=("node.js")
    fi
    
    # Check Python
    if ! command_exists python3; then
        missing_deps+=("python3")
    fi
    
    # Check Git
    if ! command_exists git; then
        missing_deps+=("git")
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_error "Missing required dependencies: ${missing_deps[*]}"
        log_info "Please install the missing dependencies and run this script again."
        exit 1
    fi
    
    log_success "All system requirements met!"
}

# Create environment file
setup_environment() {
    log_info "Setting up environment configuration..."
    
    if [ ! -f .env ]; then
        if [ -f .env.example ]; then
            cp .env.example .env
            log_success "Created .env file from template"
            log_warning "Please review and update the .env file with your specific configuration"
        else
            log_error ".env.example file not found"
            exit 1
        fi
    else
        log_info ".env file already exists, skipping creation"
    fi
}

# Setup backend
setup_backend() {
    log_info "Setting up backend environment..."
    
    cd backend
    
    # Create virtual environment
    if [ ! -d "venv" ]; then
        log_info "Creating Python virtual environment..."
        python3 -m venv venv
        log_success "Virtual environment created"
    fi
    
    # Activate virtual environment
    source venv/bin/activate
    
    # Upgrade pip
    log_info "Upgrading pip..."
    pip install --upgrade pip
    
    # Install dependencies
    log_info "Installing Python dependencies..."
    pip install -r requirements.txt
    
    log_success "Backend setup completed"
    cd ..
}

# Setup frontend
setup_frontend() {
    log_info "Setting up frontend environment..."
    
    cd frontend
    
    # Install Node.js dependencies
    log_info "Installing Node.js dependencies..."
    npm install
    
    log_success "Frontend setup completed"
    cd ..
}

# Setup database
setup_database() {
    log_info "Setting up database..."
    
    # Start PostgreSQL with Docker Compose
    log_info "Starting PostgreSQL database..."
    docker-compose up -d postgres
    
    # Wait for database to be ready
    log_info "Waiting for database to be ready..."
    sleep 10
    
    # Run database migrations
    log_info "Running database migrations..."
    cd backend
    source venv/bin/activate
    alembic upgrade head
    cd ..
    
    log_success "Database setup completed"
}

# Setup storage services
setup_storage() {
    log_info "Setting up storage services..."
    
    # Start MinIO and Redis
    log_info "Starting MinIO and Redis..."
    docker-compose up -d minio redis
    
    # Wait for services to be ready
    log_info "Waiting for storage services to be ready..."
    sleep 15
    
    # Create MinIO bucket
    log_info "Creating MinIO bucket..."
    docker-compose exec minio mc alias set local http://localhost:9000 minioadmin minioadmin123
    docker-compose exec minio mc mb local/knowledgebot-documents || true
    
    log_success "Storage services setup completed"
}

# Setup search services
setup_search() {
    log_info "Setting up search services..."
    
    # Start Elasticsearch
    log_info "Starting Elasticsearch..."
    docker-compose up -d elasticsearch
    
    # Wait for Elasticsearch to be ready
    log_info "Waiting for Elasticsearch to be ready..."
    sleep 30
    
    log_success "Search services setup completed"
}

# Create initial admin user
create_admin_user() {
    log_info "Creating initial admin user..."
    
    cd backend
    source venv/bin/activate
    
    # Create admin user script
    cat > create_admin.py << EOF
import asyncio
from app.core.database import AsyncSessionLocal
from app.models.user import User, Role, Permission
from app.core.security import get_password_hash
from sqlalchemy import select

async def create_admin():
    async with AsyncSessionLocal() as db:
        # Check if admin user exists
        stmt = select(User).where(User.email == "<EMAIL>")
        result = await db.execute(stmt)
        admin_user = result.scalar_one_or_none()
        
        if not admin_user:
            # Create admin user
            admin_user = User(
                email="<EMAIL>",
                hashed_password=get_password_hash("admin123"),
                first_name="Admin",
                last_name="User",
                is_active=True,
                is_verified=True,
                is_superuser=True
            )
            db.add(admin_user)
            await db.commit()
            print("Admin user created successfully!")
            print("Email: <EMAIL>")
            print("Password: admin123")
        else:
            print("Admin user already exists")

if __name__ == "__main__":
    asyncio.run(create_admin())
EOF
    
    python create_admin.py
    rm create_admin.py
    
    cd ..
    log_success "Admin user setup completed"
}

# Start all services
start_services() {
    log_info "Starting all services..."
    
    # Start all services with Docker Compose
    docker-compose up -d
    
    log_info "Waiting for all services to be ready..."
    sleep 30
    
    log_success "All services started successfully!"
}

# Display service URLs
show_service_urls() {
    log_success "KnowledgeBot setup completed successfully!"
    echo ""
    echo "Service URLs:"
    echo "============="
    echo "• Frontend (React):      http://localhost:3000"
    echo "• Backend API:           http://localhost:8000"
    echo "• API Documentation:     http://localhost:8000/docs"
    echo "• MinIO Console:         http://localhost:9001"
    echo "• Flower (Celery):       http://localhost:5555"
    echo "• Grafana Dashboard:     http://localhost:3001"
    echo "• Prometheus:            http://localhost:9090"
    echo ""
    echo "Default Admin Credentials:"
    echo "=========================="
    echo "• Email:    <EMAIL>"
    echo "• Password: admin123"
    echo ""
    echo "Next Steps:"
    echo "==========="
    echo "1. Review and update the .env file with your configuration"
    echo "2. Configure your MemVid API key in .env"
    echo "3. Configure your vector database (Pinecone/Weaviate) in .env"
    echo "4. Access the frontend at http://localhost:3000"
    echo "5. Login with the admin credentials above"
    echo ""
    log_warning "Remember to change the default admin password in production!"
}

# Cleanup function
cleanup() {
    log_info "Cleaning up temporary files..."
    # Add any cleanup tasks here
}

# Main setup function
main() {
    echo "=================================================="
    echo "KnowledgeBot - Corporate Memory System Setup"
    echo "=================================================="
    echo ""
    
    # Trap cleanup on exit
    trap cleanup EXIT
    
    # Run setup steps
    check_requirements
    setup_environment
    setup_backend
    setup_frontend
    setup_database
    setup_storage
    setup_search
    create_admin_user
    start_services
    show_service_urls
}

# Handle command line arguments
case "${1:-}" in
    --help|-h)
        echo "KnowledgeBot Setup Script"
        echo ""
        echo "Usage: $0 [OPTIONS]"
        echo ""
        echo "Options:"
        echo "  --help, -h     Show this help message"
        echo "  --backend      Setup backend only"
        echo "  --frontend     Setup frontend only"
        echo "  --database     Setup database only"
        echo "  --services     Start services only"
        echo ""
        exit 0
        ;;
    --backend)
        check_requirements
        setup_environment
        setup_backend
        ;;
    --frontend)
        check_requirements
        setup_environment
        setup_frontend
        ;;
    --database)
        check_requirements
        setup_environment
        setup_database
        ;;
    --services)
        start_services
        show_service_urls
        ;;
    *)
        main
        ;;
esac
