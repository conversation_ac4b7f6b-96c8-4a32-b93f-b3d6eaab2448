/**
 * KnowledgeBot - Corporate Memory System
 * Protected Route Component
 * 
 * Route wrapper that handles authentication and authorization
 * with permission-based access control and loading states.
 */

import React from 'react'
import { Navigate, useLocation } from 'react-router-dom'
import { useAuth } from '@/hooks/useAuth'
import LoadingSpinner from '@/components/common/LoadingSpinner'

interface ProtectedRouteProps {
  children: React.ReactNode
  requiredPermissions?: string[]
  requireAll?: boolean // If true, user must have ALL permissions; if false, user needs ANY permission
  fallbackPath?: string
  showUnauthorized?: boolean
}

export default function ProtectedRoute({
  children,
  requiredPermissions = [],
  requireAll = false,
  fallbackPath = '/login',
  showUnauthorized = false,
}: ProtectedRouteProps) {
  const { user, isAuthenticated, isLoading, hasPermission, hasAnyPermission, hasAllPermissions } = useAuth()
  const location = useLocation()

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <LoadingSpinner size="lg" text="Checking authentication..." />
      </div>
    )
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated || !user) {
    return (
      <Navigate 
        to={fallbackPath} 
        state={{ from: location.pathname }} 
        replace 
      />
    )
  }

  // Check permissions if required
  if (requiredPermissions.length > 0) {
    const hasRequiredPermissions = requireAll
      ? hasAllPermissions(requiredPermissions)
      : hasAnyPermission(requiredPermissions)

    if (!hasRequiredPermissions) {
      if (showUnauthorized) {
        return <UnauthorizedAccess requiredPermissions={requiredPermissions} />
      }
      
      // Redirect to dashboard or show unauthorized page
      return <Navigate to="/dashboard" replace />
    }
  }

  // User is authenticated and authorized
  return <>{children}</>
}

// Unauthorized access component
function UnauthorizedAccess({ requiredPermissions }: { requiredPermissions: string[] }) {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 px-4">
      <div className="max-w-md w-full text-center">
        <div className="mb-6">
          <div className="mx-auto w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center">
            <svg
              className="w-8 h-8 text-red-600 dark:text-red-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
        </div>

        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
          Access Denied
        </h1>
        
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          You don't have permission to access this page. Please contact your administrator if you believe this is an error.
        </p>

        {import.meta.env.DEV && (
          <div className="mb-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
            <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-2">
              Required Permissions (Development Only)
            </h3>
            <ul className="text-xs text-yellow-700 dark:text-yellow-300 space-y-1">
              {requiredPermissions.map((permission) => (
                <li key={permission} className="font-mono">
                  {permission}
                </li>
              ))}
            </ul>
          </div>
        )}

        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <button
            onClick={() => window.history.back()}
            className="btn btn-secondary"
          >
            Go Back
          </button>
          <a href="/dashboard" className="btn btn-primary">
            Go to Dashboard
          </a>
        </div>
      </div>
    </div>
  )
}

// Role-based route protection
export function RoleProtectedRoute({
  children,
  allowedRoles,
  fallbackPath = '/dashboard',
}: {
  children: React.ReactNode
  allowedRoles: string[]
  fallbackPath?: string
}) {
  const { user, isAuthenticated, isLoading } = useAuth()

  if (isLoading) {
    return <LoadingSpinner size="lg" className="min-h-screen" />
  }

  if (!isAuthenticated || !user) {
    return <Navigate to="/login" replace />
  }

  const userRoles = user.roles?.map(role => role.name) || []
  const hasAllowedRole = allowedRoles.some(role => userRoles.includes(role)) || user.is_superuser

  if (!hasAllowedRole) {
    return <Navigate to={fallbackPath} replace />
  }

  return <>{children}</>
}

// Department-based route protection
export function DepartmentProtectedRoute({
  children,
  allowedDepartments,
  fallbackPath = '/dashboard',
}: {
  children: React.ReactNode
  allowedDepartments: string[]
  fallbackPath?: string
}) {
  const { user, isAuthenticated, isLoading, isInDepartment } = useAuth()

  if (isLoading) {
    return <LoadingSpinner size="lg" className="min-h-screen" />
  }

  if (!isAuthenticated || !user) {
    return <Navigate to="/login" replace />
  }

  const hasAllowedDepartment = allowedDepartments.some(dept => isInDepartment(dept)) || user.is_superuser

  if (!hasAllowedDepartment) {
    return <Navigate to={fallbackPath} replace />
  }

  return <>{children}</>
}

// Admin route protection (shorthand for admin permissions)
export function AdminRoute({ children }: { children: React.ReactNode }) {
  return (
    <ProtectedRoute
      requiredPermissions={['admin.stats.read']}
      showUnauthorized={true}
    >
      {children}
    </ProtectedRoute>
  )
}

// Superuser route protection
export function SuperuserRoute({ children }: { children: React.ReactNode }) {
  const { user, isAuthenticated, isLoading } = useAuth()

  if (isLoading) {
    return <LoadingSpinner size="lg" className="min-h-screen" />
  }

  if (!isAuthenticated || !user) {
    return <Navigate to="/login" replace />
  }

  if (!user.is_superuser) {
    return <UnauthorizedAccess requiredPermissions={['superuser']} />
  }

  return <>{children}</>
}
