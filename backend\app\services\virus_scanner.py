"""
KnowledgeBot - Corporate Memory System
Virus Scanner Service for Enterprise Security

This module provides virus scanning capabilities for uploaded files
using multiple scanning engines and security validation.
"""

import asyncio
import logging
import hashlib
import tempfile
import subprocess
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from pathlib import Path
from datetime import datetime
import aiofiles

from app.core.config import settings

logger = logging.getLogger(__name__)


@dataclass
class ScanResult:
    """Virus scan result data structure."""
    is_infected: bool
    threat_name: Optional[str] = None
    scanner_engine: Optional[str] = None
    scan_time: float = 0.0
    file_hash: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class VirusScanner:
    """
    Enterprise virus scanner with multiple engine support.
    """
    
    def __init__(self):
        self.enabled = settings.ENABLE_VIRUS_SCANNING
        self.scan_engines = []
        self.quarantine_path = Path(settings.LOCAL_STORAGE_PATH) / "quarantine"
        self._initialize_scanners()
    
    def _initialize_scanners(self):
        """Initialize available virus scanning engines."""
        if not self.enabled:
            logger.info("Virus scanning is disabled")
            return
        
        try:
            # Create quarantine directory
            self.quarantine_path.mkdir(parents=True, exist_ok=True)
            
            # Check for ClamAV
            if self._check_clamav_available():
                self.scan_engines.append("clamav")
                logger.info("ClamAV scanner initialized")
            
            # Check for Windows Defender (if on Windows)
            if self._check_defender_available():
                self.scan_engines.append("defender")
                logger.info("Windows Defender scanner initialized")
            
            # Add signature-based scanner as fallback
            self.scan_engines.append("signature")
            
            if not self.scan_engines:
                logger.warning("No virus scanning engines available")
            else:
                logger.info(f"Virus scanner initialized with engines: {self.scan_engines}")
                
        except Exception as e:
            logger.error(f"Error initializing virus scanner: {e}")
            self.enabled = False
    
    def _check_clamav_available(self) -> bool:
        """Check if ClamAV is available."""
        try:
            result = subprocess.run(
                ["clamscan", "--version"],
                capture_output=True,
                text=True,
                timeout=10
            )
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return False
    
    def _check_defender_available(self) -> bool:
        """Check if Windows Defender is available."""
        try:
            import platform
            if platform.system() != "Windows":
                return False
            
            result = subprocess.run(
                ["powershell", "-Command", "Get-MpComputerStatus"],
                capture_output=True,
                text=True,
                timeout=10
            )
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return False
    
    async def scan_content(self, file_content: bytes, filename: str = "unknown") -> ScanResult:
        """
        Scan file content for viruses and malware.
        
        Args:
            file_content: Binary file content
            filename: Original filename for context
            
        Returns:
            ScanResult: Scan results with threat information
        """
        if not self.enabled:
            return ScanResult(is_infected=False, scanner_engine="disabled")
        
        start_time = datetime.utcnow()
        file_hash = hashlib.sha256(file_content).hexdigest()
        
        try:
            # Check against known malware signatures first
            signature_result = await self._scan_with_signatures(file_content, file_hash)
            if signature_result.is_infected:
                return signature_result
            
            # Scan with available engines
            for engine in self.scan_engines:
                if engine == "signature":
                    continue  # Already checked
                
                try:
                    if engine == "clamav":
                        result = await self._scan_with_clamav(file_content, filename)
                    elif engine == "defender":
                        result = await self._scan_with_defender(file_content, filename)
                    else:
                        continue
                    
                    if result.is_infected:
                        # Quarantine infected file
                        await self._quarantine_file(file_content, filename, file_hash, result)
                        return result
                        
                except Exception as e:
                    logger.error(f"Error scanning with {engine}: {e}")
                    continue
            
            # If no threats found
            scan_time = (datetime.utcnow() - start_time).total_seconds()
            return ScanResult(
                is_infected=False,
                scanner_engine=self.scan_engines[0] if self.scan_engines else "none",
                scan_time=scan_time,
                file_hash=file_hash
            )
            
        except Exception as e:
            logger.error(f"Error during virus scan: {e}")
            # Fail secure - treat as infected if scan fails
            return ScanResult(
                is_infected=True,
                threat_name="SCAN_ERROR",
                scanner_engine="error",
                file_hash=file_hash,
                metadata={"error": str(e)}
            )
    
    async def _scan_with_signatures(self, file_content: bytes, file_hash: str) -> ScanResult:
        """
        Scan using known malware signatures and heuristics.
        
        Args:
            file_content: File content to scan
            file_hash: SHA256 hash of the file
            
        Returns:
            ScanResult: Signature scan results
        """
        try:
            # Check against known malware hashes
            if await self._check_malware_hash(file_hash):
                return ScanResult(
                    is_infected=True,
                    threat_name="KNOWN_MALWARE_HASH",
                    scanner_engine="signature",
                    file_hash=file_hash
                )
            
            # Check for suspicious patterns
            suspicious_patterns = [
                b'X5O!P%@AP[4\\PZX54(P^)7CC)7}$EICAR-STANDARD-ANTIVIRUS-TEST-FILE!$H+H*',  # EICAR test
                b'eval(',  # Suspicious JavaScript
                b'<script',  # Potential XSS
                b'powershell',  # PowerShell execution
                b'cmd.exe',  # Command execution
                b'system(',  # System calls
                b'exec(',  # Code execution
            ]
            
            content_lower = file_content.lower()
            for pattern in suspicious_patterns:
                if pattern in content_lower:
                    return ScanResult(
                        is_infected=True,
                        threat_name=f"SUSPICIOUS_PATTERN_{pattern.decode('utf-8', errors='ignore')}",
                        scanner_engine="signature",
                        file_hash=file_hash
                    )
            
            # Check file size limits (potential zip bombs)
            if len(file_content) > 500 * 1024 * 1024:  # 500MB
                return ScanResult(
                    is_infected=True,
                    threat_name="OVERSIZED_FILE",
                    scanner_engine="signature",
                    file_hash=file_hash
                )
            
            return ScanResult(is_infected=False, scanner_engine="signature", file_hash=file_hash)
            
        except Exception as e:
            logger.error(f"Error in signature scanning: {e}")
            return ScanResult(is_infected=False, scanner_engine="signature", file_hash=file_hash)
    
    async def _check_malware_hash(self, file_hash: str) -> bool:
        """Check if file hash is in known malware database."""
        # This would typically check against a threat intelligence database
        # For now, we'll use a simple blacklist
        known_malware_hashes = {
            # EICAR test file hash
            "275a021bbfb6489e54d471899f7db9d1663fc695ec2fe2a2c4538aabf651fd0f",
            # Add more known malware hashes here
        }
        
        return file_hash.lower() in known_malware_hashes
    
    async def _scan_with_clamav(self, file_content: bytes, filename: str) -> ScanResult:
        """
        Scan file using ClamAV.
        
        Args:
            file_content: File content to scan
            filename: Original filename
            
        Returns:
            ScanResult: ClamAV scan results
        """
        try:
            # Create temporary file
            with tempfile.NamedTemporaryFile(delete=False, suffix=Path(filename).suffix) as temp_file:
                temp_file.write(file_content)
                temp_file_path = temp_file.name
            
            try:
                # Run ClamAV scan
                result = await asyncio.create_subprocess_exec(
                    "clamscan",
                    "--no-summary",
                    "--infected",
                    temp_file_path,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                
                stdout, stderr = await result.communicate()
                
                # Parse results
                if result.returncode == 0:
                    # No virus found
                    return ScanResult(is_infected=False, scanner_engine="clamav")
                elif result.returncode == 1:
                    # Virus found
                    output = stdout.decode('utf-8', errors='ignore')
                    threat_name = self._extract_clamav_threat_name(output)
                    return ScanResult(
                        is_infected=True,
                        threat_name=threat_name,
                        scanner_engine="clamav"
                    )
                else:
                    # Error occurred
                    error_msg = stderr.decode('utf-8', errors='ignore')
                    logger.error(f"ClamAV scan error: {error_msg}")
                    return ScanResult(
                        is_infected=True,
                        threat_name="SCAN_ERROR",
                        scanner_engine="clamav",
                        metadata={"error": error_msg}
                    )
                    
            finally:
                # Clean up temporary file
                Path(temp_file_path).unlink(missing_ok=True)
                
        except Exception as e:
            logger.error(f"Error scanning with ClamAV: {e}")
            return ScanResult(
                is_infected=True,
                threat_name="SCAN_ERROR",
                scanner_engine="clamav",
                metadata={"error": str(e)}
            )
    
    def _extract_clamav_threat_name(self, output: str) -> str:
        """Extract threat name from ClamAV output."""
        try:
            # ClamAV output format: filename: threat_name FOUND
            lines = output.strip().split('\n')
            for line in lines:
                if 'FOUND' in line:
                    parts = line.split(':')
                    if len(parts) >= 2:
                        threat_part = parts[1].strip()
                        return threat_part.replace(' FOUND', '')
            
            return "UNKNOWN_THREAT"
            
        except Exception:
            return "UNKNOWN_THREAT"
    
    async def _scan_with_defender(self, file_content: bytes, filename: str) -> ScanResult:
        """
        Scan file using Windows Defender.
        
        Args:
            file_content: File content to scan
            filename: Original filename
            
        Returns:
            ScanResult: Windows Defender scan results
        """
        try:
            # Create temporary file
            with tempfile.NamedTemporaryFile(delete=False, suffix=Path(filename).suffix) as temp_file:
                temp_file.write(file_content)
                temp_file_path = temp_file.name
            
            try:
                # Run Windows Defender scan
                result = await asyncio.create_subprocess_exec(
                    "powershell",
                    "-Command",
                    f"Start-MpScan -ScanType CustomScan -ScanPath '{temp_file_path}'",
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                
                stdout, stderr = await result.communicate()
                
                # Check scan results
                if result.returncode == 0:
                    return ScanResult(is_infected=False, scanner_engine="defender")
                else:
                    # Threat detected or error
                    output = stdout.decode('utf-8', errors='ignore')
                    return ScanResult(
                        is_infected=True,
                        threat_name="DEFENDER_THREAT",
                        scanner_engine="defender",
                        metadata={"output": output}
                    )
                    
            finally:
                # Clean up temporary file
                Path(temp_file_path).unlink(missing_ok=True)
                
        except Exception as e:
            logger.error(f"Error scanning with Windows Defender: {e}")
            return ScanResult(
                is_infected=True,
                threat_name="SCAN_ERROR",
                scanner_engine="defender",
                metadata={"error": str(e)}
            )
    
    async def _quarantine_file(
        self,
        file_content: bytes,
        filename: str,
        file_hash: str,
        scan_result: ScanResult
    ):
        """Quarantine infected file for analysis."""
        try:
            # Create quarantine filename
            timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
            quarantine_filename = f"{timestamp}_{file_hash}_{filename}"
            quarantine_file_path = self.quarantine_path / quarantine_filename
            
            # Save infected file
            async with aiofiles.open(quarantine_file_path, 'wb') as f:
                await f.write(file_content)
            
            # Save scan metadata
            metadata = {
                "original_filename": filename,
                "file_hash": file_hash,
                "threat_name": scan_result.threat_name,
                "scanner_engine": scan_result.scanner_engine,
                "quarantine_time": datetime.utcnow().isoformat(),
                "file_size": len(file_content)
            }
            
            metadata_path = quarantine_file_path.with_suffix('.json')
            async with aiofiles.open(metadata_path, 'w') as f:
                import json
                await f.write(json.dumps(metadata, indent=2))
            
            logger.warning(f"File quarantined: {quarantine_filename} - {scan_result.threat_name}")
            
        except Exception as e:
            logger.error(f"Error quarantining file: {e}")
    
    async def get_scan_statistics(self) -> Dict[str, Any]:
        """Get virus scanning statistics."""
        try:
            stats = {
                "enabled": self.enabled,
                "engines": self.scan_engines,
                "quarantine_count": 0,
                "quarantine_path": str(self.quarantine_path)
            }
            
            # Count quarantined files
            if self.quarantine_path.exists():
                quarantine_files = list(self.quarantine_path.glob("*.json"))
                stats["quarantine_count"] = len(quarantine_files)
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting scan statistics: {e}")
            return {"enabled": self.enabled, "error": str(e)}
    
    async def cleanup_quarantine(self, older_than_days: int = 30):
        """Clean up old quarantined files."""
        try:
            if not self.quarantine_path.exists():
                return
            
            cutoff_time = datetime.utcnow().timestamp() - (older_than_days * 24 * 60 * 60)
            
            for file_path in self.quarantine_path.iterdir():
                if file_path.stat().st_mtime < cutoff_time:
                    file_path.unlink()
                    logger.info(f"Cleaned up old quarantine file: {file_path}")
                    
        except Exception as e:
            logger.error(f"Error cleaning up quarantine: {e}")
    
    def is_enabled(self) -> bool:
        """Check if virus scanning is enabled."""
        return self.enabled
    
    def get_available_engines(self) -> List[str]:
        """Get list of available scanning engines."""
        return self.scan_engines.copy()
