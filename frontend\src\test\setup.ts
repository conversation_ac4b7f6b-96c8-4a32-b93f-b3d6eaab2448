/**
 * KnowledgeBot Frontend - Test Setup
 * Configuration and setup for Vitest testing environment.
 */

import React from 'react'
import { expect, afterEach, vi } from 'vitest'
import { cleanup } from '@testing-library/react'
import * as matchers from '@testing-library/jest-dom/matchers'

// Extend Vitest's expect with jest-dom matchers
expect.extend(matchers)

// Cleanup after each test case
afterEach(() => {
  cleanup()
})

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn(() => ({
  disconnect: vi.fn(),
  observe: vi.fn(),
  unobserve: vi.fn(),
}))

// Mock ResizeObserver
global.ResizeObserver = vi.fn(() => ({
  disconnect: vi.fn(),
  observe: vi.fn(),
  unobserve: vi.fn(),
}))

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
})

// Mock scrollTo
Object.defineProperty(window, 'scrollTo', {
  writable: true,
  value: vi.fn(),
})

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
})

// Mock sessionStorage
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock,
})

// Mock fetch
global.fetch = vi.fn()

// Mock environment variables
vi.mock('import.meta.env', () => ({
  VITE_API_BASE_URL: 'http://localhost:8000/api/v1',
  VITE_APP_NAME: 'KnowledgeBot',
  VITE_APP_VERSION: '1.0.0',
  VITE_APP_ENVIRONMENT: 'test',
  MODE: 'test',
  DEV: false,
  PROD: false,
}))

// Mock react-hot-toast
vi.mock('react-hot-toast', () => ({
  default: {
    success: vi.fn(),
    error: vi.fn(),
    loading: vi.fn(),
    dismiss: vi.fn(),
  },
  toast: {
    success: vi.fn(),
    error: vi.fn(),
    loading: vi.fn(),
    dismiss: vi.fn(),
  },
  Toaster: () => null,
}))

// Mock react-router-dom
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom')
  return {
    ...actual,
    useNavigate: () => vi.fn(),
    useLocation: () => ({
      pathname: '/',
      search: '',
      hash: '',
      state: null,
    }),
    useParams: () => ({}),
  }
})

// Mock Headless UI components
vi.mock('@headlessui/react', () => ({
  Dialog: {
    Root: ({ children }: { children: React.ReactNode }) => children,
    Panel: ({ children }: { children: React.ReactNode }) => children,
    Title: ({ children }: { children: React.ReactNode }) => children,
    Description: ({ children }: { children: React.ReactNode }) => children,
  },
  Menu: {
    Button: ({ children }: { children: React.ReactNode }) => children,
    Items: ({ children }: { children: React.ReactNode }) => children,
    Item: ({ children }: { children: React.ReactNode }) => children,
  },
  Transition: ({ children }: { children: React.ReactNode }) => children,
  Fragment: ({ children }: { children: React.ReactNode }) => children,
}))

// Global test utilities
export const createMockUser = (overrides = {}) => ({
  id: 'test-user-id',
  email: '<EMAIL>',
  username: 'testuser',
  first_name: 'Test',
  last_name: 'User',
  is_active: true,
  is_verified: true,
  is_superuser: false,
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  ...overrides,
})

export const createMockDocument = (overrides = {}) => ({
  id: 'test-doc-id',
  title: 'Test Document',
  content: 'This is a test document',
  file_type: 'pdf',
  file_size: 1024,
  uploaded_at: new Date().toISOString(),
  uploaded_by: createMockUser(),
  ...overrides,
})

export const createMockApiResponse = <T>(data: T, overrides = {}) => ({
  data,
  status: 200,
  statusText: 'OK',
  headers: {},
  config: {},
  ...overrides,
})

// Test wrapper for React Query
export const createQueryWrapper = () => {
  const { QueryClient, QueryClientProvider } = require('@tanstack/react-query')
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  })
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}

// Test wrapper for Router
export const createRouterWrapper = (initialEntries = ['/']) => {
  const { MemoryRouter } = require('react-router-dom')
  
  return ({ children }: { children: React.ReactNode }) => (
    <MemoryRouter initialEntries={initialEntries}>
      {children}
    </MemoryRouter>
  )
}

// Combined test wrapper
export const createTestWrapper = (options: {
  initialEntries?: string[]
  queryClient?: any
} = {}) => {
  const QueryWrapper = createQueryWrapper()
  const RouterWrapper = createRouterWrapper(options.initialEntries)
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryWrapper>
      <RouterWrapper>
        {children}
      </RouterWrapper>
    </QueryWrapper>
  )
}
