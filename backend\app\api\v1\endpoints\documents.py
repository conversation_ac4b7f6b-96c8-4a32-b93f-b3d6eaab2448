"""
KnowledgeBot - Corporate Memory System
Document Management API Endpoints

This module handles document upload, processing, management, and retrieval
with enterprise security and departmental isolation.
"""

import os
import hashlib
from typing import List, Optional, Dict, Any
from uuid import UUID
from fastapi import (
    APIRouter, Depends, HTTPException, status, UploadFile, 
    File, Form, Query, BackgroundTasks
)
from fastapi.responses import FileResponse, StreamingResponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, desc
from sqlalchemy.orm import selectinload

from app.api.deps import get_current_user, get_db, require_permissions
from app.core.config import settings
from app.models.user import User
from app.models.document import Document, DocumentChunk, Tag, DocumentAccess
from app.schemas.document import (
    DocumentResponse,
    DocumentCreate,
    DocumentUpdate,
    DocumentListResponse,
    DocumentChunkResponse,
    TagResponse,
    DocumentSearchRequest,
    DocumentSearchResponse
)
from app.services.document_service import DocumentService
from app.services.memvid_service import MemVidService
from app.services.storage_service import StorageService
from app.services.virus_scanner import VirusScanner

router = APIRouter()
document_service = DocumentService()
memvid_service = MemVidService()
storage_service = StorageService()
virus_scanner = VirusScanner()


@router.post("/upload", response_model=DocumentResponse)
async def upload_document(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    title: Optional[str] = Form(None),
    description: Optional[str] = Form(None),
    category: Optional[str] = Form(None),
    tags: Optional[str] = Form(None),  # Comma-separated tag names
    classification_level: str = Form("internal"),
    department_ids: Optional[str] = Form(None),  # Comma-separated UUIDs
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    _: None = Depends(require_permissions(["documents.create"]))
) -> Document:
    """
    Upload and process a new document.
    
    Args:
        background_tasks: FastAPI background tasks
        file: Uploaded file
        title: Document title (optional, uses filename if not provided)
        description: Document description
        category: Document category
        tags: Comma-separated tag names
        classification_level: Security classification
        department_ids: Comma-separated department UUIDs
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Document: Created document object
        
    Raises:
        HTTPException: If upload fails or file is invalid
    """
    # Validate file
    if not file.filename:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No file provided"
        )
    
    # Check file size
    if file.size and file.size > settings.MAX_FILE_SIZE:
        raise HTTPException(
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            detail=f"File size exceeds maximum allowed size of {settings.MAX_FILE_SIZE} bytes"
        )
    
    # Check file type
    if file.content_type not in settings.ALLOWED_FILE_TYPES:
        raise HTTPException(
            status_code=status.HTTP_415_UNSUPPORTED_MEDIA_TYPE,
            detail=f"File type {file.content_type} is not supported"
        )
    
    # Read file content
    file_content = await file.read()
    file_size = len(file_content)
    
    # Calculate file hash
    file_hash = hashlib.sha256(file_content).hexdigest()
    
    # Check for duplicate files
    stmt = select(Document).where(
        Document.file_hash == file_hash,
        Document.status == "active"
    )
    result = await db.execute(stmt)
    existing_doc = result.scalar_one_or_none()
    
    if existing_doc:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="A document with identical content already exists"
        )
    
    # Virus scan if enabled
    if settings.ENABLE_VIRUS_SCANNING:
        scan_result = await virus_scanner.scan_content(file_content)
        if scan_result.is_infected:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="File failed security scan"
            )
    
    # Store file
    file_path = await storage_service.store_file(
        file_content, 
        file.filename, 
        file.content_type
    )
    
    # Create document record
    document = Document(
        title=title or file.filename,
        filename=os.path.basename(file_path),
        original_filename=file.filename,
        file_path=file_path,
        file_size=file_size,
        file_hash=file_hash,
        mime_type=file.content_type,
        description=description,
        category=category,
        classification_level=classification_level,
        uploaded_by=current_user.id,
        processing_status="pending"
    )
    
    db.add(document)
    await db.commit()
    await db.refresh(document)
    
    # Process tags
    if tags:
        tag_names = [name.strip() for name in tags.split(",")]
        await document_service.add_tags_to_document(db, document.id, tag_names)
    
    # Process department assignments
    if department_ids:
        dept_ids = [UUID(id.strip()) for id in department_ids.split(",")]
        await document_service.assign_document_to_departments(db, document.id, dept_ids)
    
    # Schedule background processing
    background_tasks.add_task(
        process_document_background,
        document.id,
        file_content
    )
    
    return document


@router.get("/", response_model=DocumentListResponse)
async def list_documents(
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    category: Optional[str] = Query(None),
    status: str = Query("active"),
    classification_level: Optional[str] = Query(None),
    search: Optional[str] = Query(None),
    sort_by: str = Query("created_at"),
    sort_order: str = Query("desc"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    _: None = Depends(require_permissions(["documents.read"]))
) -> Dict[str, Any]:
    """
    List documents with filtering and pagination.
    
    Args:
        skip: Number of documents to skip
        limit: Maximum number of documents to return
        category: Filter by category
        status: Filter by status
        classification_level: Filter by classification level
        search: Search term for title/description
        sort_by: Field to sort by
        sort_order: Sort order (asc/desc)
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Dict[str, Any]: Paginated document list
    """
    # Build query with user's department access
    user_departments = [dept.id for dept in current_user.departments]
    
    query = select(Document).options(
        selectinload(Document.tags),
        selectinload(Document.uploader)
    )
    
    # Apply filters
    filters = [Document.status == status]
    
    # Department-based access control
    if not current_user.is_superuser:
        filters.append(
            or_(
                Document.is_public == True,
                Document.uploaded_by == current_user.id,
                Document.departments.any(lambda d: d.id.in_(user_departments))
            )
        )
    
    if category:
        filters.append(Document.category == category)
    
    if classification_level:
        filters.append(Document.classification_level == classification_level)
    
    if search:
        search_filter = or_(
            Document.title.ilike(f"%{search}%"),
            Document.description.ilike(f"%{search}%"),
            Document.keywords.any(search.lower())
        )
        filters.append(search_filter)
    
    query = query.where(and_(*filters))
    
    # Apply sorting
    if sort_order == "desc":
        query = query.order_by(desc(getattr(Document, sort_by)))
    else:
        query = query.order_by(getattr(Document, sort_by))
    
    # Get total count
    count_query = select(func.count()).select_from(
        query.subquery()
    )
    total_result = await db.execute(count_query)
    total = total_result.scalar()
    
    # Apply pagination
    query = query.offset(skip).limit(limit)
    result = await db.execute(query)
    documents = result.scalars().all()
    
    return {
        "documents": documents,
        "total": total,
        "skip": skip,
        "limit": limit,
        "has_more": skip + limit < total
    }


@router.get("/{document_id}", response_model=DocumentResponse)
async def get_document(
    document_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    _: None = Depends(require_permissions(["documents.read"]))
) -> Document:
    """
    Get document by ID with access control.
    
    Args:
        document_id: Document UUID
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Document: Document object
        
    Raises:
        HTTPException: If document not found or access denied
    """
    document = await document_service.get_document_with_access_check(
        db, document_id, current_user
    )
    
    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )
    
    # Log access
    access_log = DocumentAccess(
        document_id=document_id,
        user_id=current_user.id,
        access_type="view",
        access_method="api"
    )
    db.add(access_log)
    await db.commit()
    
    return document


@router.get("/{document_id}/download")
async def download_document(
    document_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    _: None = Depends(require_permissions(["documents.download"]))
) -> FileResponse:
    """
    Download document file.
    
    Args:
        document_id: Document UUID
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        FileResponse: Document file
        
    Raises:
        HTTPException: If document not found or access denied
    """
    document = await document_service.get_document_with_access_check(
        db, document_id, current_user
    )
    
    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )
    
    # Check if file exists
    if not await storage_service.file_exists(document.file_path):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document file not found"
        )
    
    # Log download access
    access_log = DocumentAccess(
        document_id=document_id,
        user_id=current_user.id,
        access_type="download",
        access_method="api"
    )
    db.add(access_log)
    await db.commit()
    
    return FileResponse(
        path=document.file_path,
        filename=document.original_filename,
        media_type=document.mime_type
    )


@router.put("/{document_id}", response_model=DocumentResponse)
async def update_document(
    document_id: UUID,
    document_update: DocumentUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    _: None = Depends(require_permissions(["documents.update"]))
) -> Document:
    """
    Update document metadata.
    
    Args:
        document_id: Document UUID
        document_update: Document update data
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Document: Updated document object
        
    Raises:
        HTTPException: If document not found or access denied
    """
    document = await document_service.get_document_with_access_check(
        db, document_id, current_user, require_write=True
    )
    
    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )
    
    # Update fields
    update_data = document_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(document, field, value)
    
    document.updated_by = current_user.id
    
    await db.commit()
    await db.refresh(document)
    
    return document


@router.delete("/{document_id}")
async def delete_document(
    document_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    _: None = Depends(require_permissions(["documents.delete"]))
) -> Dict[str, str]:
    """
    Soft delete document.
    
    Args:
        document_id: Document UUID
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Dict[str, str]: Deletion confirmation
        
    Raises:
        HTTPException: If document not found or access denied
    """
    document = await document_service.get_document_with_access_check(
        db, document_id, current_user, require_write=True
    )
    
    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )
    
    # Soft delete
    document.status = "deleted"
    document.deleted_at = datetime.utcnow()
    document.deleted_by = current_user.id
    
    await db.commit()
    
    return {"message": "Document successfully deleted"}


async def process_document_background(document_id: UUID, file_content: bytes):
    """
    Background task to process document with MemVid encoding.
    
    Args:
        document_id: Document UUID
        file_content: File content bytes
    """
    async with AsyncSessionLocal() as db:
        try:
            # Update processing status
            stmt = select(Document).where(Document.id == document_id)
            result = await db.execute(stmt)
            document = result.scalar_one()
            
            document.processing_status = "processing"
            document.processing_started_at = datetime.utcnow()
            await db.commit()
            
            # Extract text content
            text_content = await document_service.extract_text_content(
                file_content, document.mime_type
            )
            
            # Process with MemVid
            chunks = await memvid_service.process_document(
                text_content, document.id
            )
            
            # Store chunks in database
            for i, chunk_data in enumerate(chunks):
                chunk = DocumentChunk(
                    document_id=document.id,
                    chunk_index=i,
                    content=chunk_data["content"],
                    content_hash=hashlib.sha256(chunk_data["content"].encode()).hexdigest(),
                    memvid_embedding=chunk_data["embedding"],
                    embedding_model=chunk_data["model"],
                    embedding_dimension=len(chunk_data["embedding"]),
                    word_count=len(chunk_data["content"].split()),
                    character_count=len(chunk_data["content"])
                )
                db.add(chunk)
            
            # Update document
            document.processing_status = "completed"
            document.processing_completed_at = datetime.utcnow()
            document.memvid_encoded = True
            document.total_chunks = len(chunks)
            document.word_count = sum(len(chunk["content"].split()) for chunk in chunks)
            
            await db.commit()
            
        except Exception as e:
            # Update error status
            document.processing_status = "failed"
            document.processing_error = str(e)
            await db.commit()
            raise
