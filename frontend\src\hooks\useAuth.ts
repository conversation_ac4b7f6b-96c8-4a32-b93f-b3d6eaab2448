/**
 * KnowledgeBot - Corporate Memory System
 * useAuth Hook
 * 
 * Custom hook for authentication state management and operations.
 * This is a temporary implementation until we can create the full AuthContext.
 */

import { useState, useEffect } from 'react'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import toast from 'react-hot-toast'

import { authService } from '@/services/authService'
import { User, LoginRequest, RegisterRequest } from '@/types'
import { tokenStorage } from '@/utils/tokenStorage'

interface AuthState {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  permissions: string[]
  departments: string[]
}

export function useAuth() {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isAuthenticated: false,
    isLoading: true,
    permissions: [],
    departments: [],
  })

  const queryClient = useQueryClient()

  // Check authentication status
  const { data: currentUser, isLoading: isCheckingAuth } = useQuery(
    'currentUser',
    authService.getCurrentUser,
    {
      retry: false,
      refetchOnWindowFocus: false,
      onSuccess: (data) => {
        if (data) {
          setAuthState({
            user: data.user,
            isAuthenticated: true,
            isLoading: false,
            permissions: data.permissions || [],
            departments: data.departments || [],
          })
        } else {
          setAuthState({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            permissions: [],
            departments: [],
          })
        }
      },
      onError: () => {
        setAuthState({
          user: null,
          isAuthenticated: false,
          isLoading: false,
          permissions: [],
          departments: [],
        })
        tokenStorage.clearTokens()
      },
    }
  )

  // Login mutation
  const loginMutation = useMutation(authService.login, {
    onSuccess: (data) => {
      tokenStorage.setTokens(data.access_token, data.refresh_token)
      setAuthState({
        user: data.user,
        isAuthenticated: true,
        isLoading: false,
        permissions: data.permissions,
        departments: data.departments,
      })
      queryClient.setQueryData('currentUser', data)
      toast.success('Welcome back!')
    },
    onError: (error: any) => {
      const message = error.response?.data?.detail || 'Login failed'
      toast.error(message)
      throw error
    },
  })

  // Register mutation
  const registerMutation = useMutation(authService.register, {
    onSuccess: () => {
      toast.success('Registration successful! Please check your email to verify your account.')
    },
    onError: (error: any) => {
      const message = error.response?.data?.detail || 'Registration failed'
      toast.error(message)
      throw error
    },
  })

  // Logout mutation
  const logoutMutation = useMutation(authService.logout, {
    onSuccess: () => {
      setAuthState({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        permissions: [],
        departments: [],
      })
      tokenStorage.clearTokens()
      queryClient.clear()
      toast.success('Logged out successfully')
    },
    onError: () => {
      // Even if logout fails on server, clear local state
      setAuthState({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        permissions: [],
        departments: [],
      })
      tokenStorage.clearTokens()
      queryClient.clear()
    },
  })

  // Update loading state
  useEffect(() => {
    setAuthState(prev => ({ ...prev, isLoading: isCheckingAuth }))
  }, [isCheckingAuth])

  // Permission checking functions
  const hasPermission = (permission: string): boolean => {
    return authState.permissions.includes(permission) || authState.user?.is_superuser || false
  }

  const hasAnyPermission = (permissions: string[]): boolean => {
    if (authState.user?.is_superuser) return true
    return permissions.some((permission) => authState.permissions.includes(permission))
  }

  const hasAllPermissions = (permissions: string[]): boolean => {
    if (authState.user?.is_superuser) return true
    return permissions.every((permission) => authState.permissions.includes(permission))
  }

  const isInDepartment = (departmentId: string): boolean => {
    return authState.departments.includes(departmentId)
  }

  return {
    ...authState,
    login: loginMutation.mutateAsync,
    register: registerMutation.mutateAsync,
    logout: logoutMutation.mutateAsync,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    isInDepartment,
  }
}
