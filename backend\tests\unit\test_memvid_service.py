"""
KnowledgeBot Backend - MemVid Service Tests
Unit tests for MemVid service functionality.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
import numpy as np

from app.services.memvid_service import MemVidService


class TestMemVidService:
    """Test cases for MemVidService."""
    
    @pytest.fixture
    def memvid_service(self):
        """Create MemVidService instance."""
        with patch('app.services.memvid_service.SentenceTransformer') as mock_transformer:
            mock_model = MagicMock()
            mock_model.encode.return_value = np.array([[0.1] * 384])
            mock_transformer.return_value = mock_model
            
            service = MemVidService()
            service.model = mock_model
            return service
    
    @pytest.mark.asyncio
    async def test_generate_embedding_success(self, memvid_service):
        """Test successful embedding generation."""
        text = "This is a test document for embedding generation."
        
        embedding = await memvid_service.generate_embedding(text)
        
        assert isinstance(embedding, list)
        assert len(embedding) == 384
        assert all(isinstance(x, float) for x in embedding)
    
    @pytest.mark.asyncio
    async def test_generate_embedding_empty_text(self, memvid_service):
        """Test embedding generation with empty text."""
        with pytest.raises(ValueError, match="Text cannot be empty"):
            await memvid_service.generate_embedding("")
    
    @pytest.mark.asyncio
    async def test_generate_embedding_long_text(self, memvid_service):
        """Test embedding generation with long text."""
        # Create text longer than max chunk size
        long_text = "This is a test. " * 1000
        
        embedding = await memvid_service.generate_embedding(long_text)
        
        assert isinstance(embedding, list)
        assert len(embedding) == 384
    
    @pytest.mark.asyncio
    async def test_chunk_text_basic(self, memvid_service):
        """Test basic text chunking."""
        text = "This is sentence one. This is sentence two. This is sentence three."
        
        chunks = await memvid_service.chunk_text(text, max_chunk_size=50)
        
        assert isinstance(chunks, list)
        assert len(chunks) > 0
        assert all(len(chunk) <= 50 for chunk in chunks)
    
    @pytest.mark.asyncio
    async def test_chunk_text_with_overlap(self, memvid_service):
        """Test text chunking with overlap."""
        text = "This is a longer text that needs to be chunked with overlap. " * 10
        
        chunks = await memvid_service.chunk_text(
            text, max_chunk_size=100, overlap_size=20
        )
        
        assert isinstance(chunks, list)
        assert len(chunks) > 1
        
        # Check overlap exists between consecutive chunks
        for i in range(len(chunks) - 1):
            chunk1_end = chunks[i][-20:]
            chunk2_start = chunks[i + 1][:20]
            # Should have some overlap
            assert len(set(chunk1_end.split()) & set(chunk2_start.split())) > 0
    
    @pytest.mark.asyncio
    async def test_generate_answer_success(self, memvid_service):
        """Test successful answer generation."""
        question = "What is the main topic of this document?"
        context = "This document discusses artificial intelligence and machine learning."
        
        with patch.object(memvid_service, '_generate_answer_with_context') as mock_generate:
            mock_generate.return_value = MagicMock(
                answer="The main topic is artificial intelligence and machine learning.",
                confidence_score=0.85,
                processing_time=0.5
            )
            
            response = await memvid_service.generate_answer(question, context)
            
            assert response.answer is not None
            assert 0 <= response.confidence_score <= 1
            assert response.processing_time > 0
    
    @pytest.mark.asyncio
    async def test_generate_answer_empty_question(self, memvid_service):
        """Test answer generation with empty question."""
        context = "This is some context."
        
        with pytest.raises(ValueError, match="Question cannot be empty"):
            await memvid_service.generate_answer("", context)
    
    @pytest.mark.asyncio
    async def test_generate_answer_empty_context(self, memvid_service):
        """Test answer generation with empty context."""
        question = "What is this about?"
        
        with pytest.raises(ValueError, match="Context cannot be empty"):
            await memvid_service.generate_answer(question, "")
    
    @pytest.mark.asyncio
    async def test_similarity_search(self, memvid_service):
        """Test similarity search functionality."""
        query_embedding = [0.1] * 384
        document_embeddings = [
            ([0.1] * 384, "doc1", "Document 1 content"),
            ([0.2] * 384, "doc2", "Document 2 content"),
            ([0.9] * 384, "doc3", "Document 3 content"),
        ]
        
        results = await memvid_service.similarity_search(
            query_embedding, document_embeddings, top_k=2, threshold=0.5
        )
        
        assert isinstance(results, list)
        assert len(results) <= 2
        assert all('similarity_score' in result for result in results)
        assert all('document_id' in result for result in results)
        assert all('content' in result for result in results)
    
    @pytest.mark.asyncio
    async def test_batch_generate_embeddings(self, memvid_service):
        """Test batch embedding generation."""
        texts = [
            "First document content",
            "Second document content",
            "Third document content"
        ]
        
        embeddings = await memvid_service.batch_generate_embeddings(texts)
        
        assert isinstance(embeddings, list)
        assert len(embeddings) == len(texts)
        assert all(isinstance(emb, list) for emb in embeddings)
        assert all(len(emb) == 384 for emb in embeddings)
    
    @pytest.mark.asyncio
    async def test_preprocess_text(self, memvid_service):
        """Test text preprocessing."""
        raw_text = "  This is a TEST document with EXTRA   spaces and symbols!@#  "
        
        processed = memvid_service._preprocess_text(raw_text)
        
        assert processed.strip() == processed  # No leading/trailing spaces
        assert "  " not in processed  # No double spaces
        assert processed != raw_text  # Should be different from original
    
    @pytest.mark.asyncio
    async def test_calculate_confidence_score(self, memvid_service):
        """Test confidence score calculation."""
        # High similarity scores should give high confidence
        high_similarities = [0.9, 0.85, 0.8]
        high_confidence = memvid_service._calculate_confidence_score(high_similarities)
        assert 0.7 <= high_confidence <= 1.0
        
        # Low similarity scores should give low confidence
        low_similarities = [0.3, 0.2, 0.1]
        low_confidence = memvid_service._calculate_confidence_score(low_similarities)
        assert 0.0 <= low_confidence <= 0.5
        
        # Empty similarities should give zero confidence
        zero_confidence = memvid_service._calculate_confidence_score([])
        assert zero_confidence == 0.0
    
    @pytest.mark.asyncio
    async def test_model_initialization_error(self):
        """Test handling of model initialization errors."""
        with patch('app.services.memvid_service.SentenceTransformer') as mock_transformer:
            mock_transformer.side_effect = Exception("Model loading failed")
            
            with pytest.raises(Exception, match="Model loading failed"):
                MemVidService()
    
    @pytest.mark.asyncio
    async def test_embedding_generation_error(self, memvid_service):
        """Test handling of embedding generation errors."""
        memvid_service.model.encode.side_effect = Exception("Encoding failed")
        
        with pytest.raises(Exception, match="Error generating embedding"):
            await memvid_service.generate_embedding("test text")
    
    def test_preprocess_text_edge_cases(self, memvid_service):
        """Test text preprocessing edge cases."""
        # Empty string
        assert memvid_service._preprocess_text("") == ""
        
        # Only whitespace
        assert memvid_service._preprocess_text("   \n\t   ") == ""
        
        # Special characters
        result = memvid_service._preprocess_text("Hello@#$%World!")
        assert "@#$%" not in result or result.replace("@#$%", "") != result
        
        # Unicode characters
        unicode_text = "Hello 世界 🌍"
        result = memvid_service._preprocess_text(unicode_text)
        assert isinstance(result, str)
    
    @pytest.mark.asyncio
    async def test_chunk_text_edge_cases(self, memvid_service):
        """Test text chunking edge cases."""
        # Very short text
        short_text = "Hi"
        chunks = await memvid_service.chunk_text(short_text, max_chunk_size=100)
        assert len(chunks) == 1
        assert chunks[0] == short_text
        
        # Text exactly at chunk size
        exact_text = "a" * 100
        chunks = await memvid_service.chunk_text(exact_text, max_chunk_size=100)
        assert len(chunks) == 1
        
        # Zero chunk size should raise error
        with pytest.raises(ValueError):
            await memvid_service.chunk_text("test", max_chunk_size=0)
