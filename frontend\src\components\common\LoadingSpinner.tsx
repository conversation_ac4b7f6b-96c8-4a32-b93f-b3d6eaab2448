/**
 * KnowledgeBot - Corporate Memory System
 * Loading Spinner Component
 * 
 * Reusable loading spinner with different sizes and styles
 * for consistent loading states throughout the application.
 */

import React from 'react'
import { clsx } from 'clsx'

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  color?: 'primary' | 'secondary' | 'white'
  className?: string
  text?: string
  fullScreen?: boolean
}

const sizeClasses = {
  sm: 'w-4 h-4',
  md: 'w-6 h-6',
  lg: 'w-8 h-8',
  xl: 'w-12 h-12',
}

const colorClasses = {
  primary: 'border-primary-600',
  secondary: 'border-gray-600',
  white: 'border-white',
}

export default function LoadingSpinner({
  size = 'md',
  color = 'primary',
  className,
  text,
  fullScreen = false,
}: LoadingSpinnerProps) {
  const spinner = (
    <div
      className={clsx(
        'animate-spin rounded-full border-2 border-gray-300',
        sizeClasses[size],
        colorClasses[color],
        'border-t-transparent',
        className
      )}
      role="status"
      aria-label="Loading"
    />
  )

  if (fullScreen) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm">
        <div className="flex flex-col items-center space-y-4">
          <div className={clsx('animate-spin rounded-full border-2 border-gray-300', sizeClasses.xl, colorClasses[color], 'border-t-transparent')} />
          {text && (
            <p className="text-sm text-gray-600 dark:text-gray-400 animate-pulse">
              {text}
            </p>
          )}
        </div>
      </div>
    )
  }

  if (text) {
    return (
      <div className="flex items-center space-x-3">
        {spinner}
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {text}
        </span>
      </div>
    )
  }

  return spinner
}

// Skeleton loading component for content placeholders
export function SkeletonLoader({ 
  className,
  lines = 3,
  avatar = false,
}: {
  className?: string
  lines?: number
  avatar?: boolean
}) {
  return (
    <div className={clsx('animate-pulse', className)}>
      <div className="flex space-x-4">
        {avatar && (
          <div className="rounded-full bg-gray-300 dark:bg-gray-700 h-10 w-10" />
        )}
        <div className="flex-1 space-y-2">
          {Array.from({ length: lines }).map((_, index) => (
            <div
              key={index}
              className={clsx(
                'h-4 bg-gray-300 dark:bg-gray-700 rounded',
                index === lines - 1 ? 'w-3/4' : 'w-full'
              )}
            />
          ))}
        </div>
      </div>
    </div>
  )
}

// Loading overlay for forms and containers
export function LoadingOverlay({
  isLoading,
  children,
  text = 'Loading...',
}: {
  isLoading: boolean
  children: React.ReactNode
  text?: string
}) {
  return (
    <div className="relative">
      {children}
      {isLoading && (
        <div className="absolute inset-0 z-10 flex items-center justify-center bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm rounded-lg">
          <div className="flex flex-col items-center space-y-2">
            <LoadingSpinner size="lg" />
            <span className="text-sm text-gray-600 dark:text-gray-400">
              {text}
            </span>
          </div>
        </div>
      )}
    </div>
  )
}

// Button loading state
export function ButtonSpinner({ size = 'sm' }: { size?: 'sm' | 'md' }) {
  return (
    <LoadingSpinner
      size={size}
      color="white"
      className="mr-2"
    />
  )
}

// Page loading component
export function PageLoader({ text = 'Loading page...' }: { text?: string }) {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
      <div className="text-center">
        <div className="mb-4">
          <LoadingSpinner size="xl" />
        </div>
        <h2 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
          KnowledgeBot
        </h2>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          {text}
        </p>
      </div>
    </div>
  )
}

// Table loading rows
export function TableSkeleton({ 
  rows = 5, 
  columns = 4 
}: { 
  rows?: number
  columns?: number 
}) {
  return (
    <div className="animate-pulse">
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div key={rowIndex} className="flex space-x-4 py-4 border-b border-gray-200 dark:border-gray-700">
          {Array.from({ length: columns }).map((_, colIndex) => (
            <div
              key={colIndex}
              className="flex-1 h-4 bg-gray-300 dark:bg-gray-700 rounded"
            />
          ))}
        </div>
      ))}
    </div>
  )
}

// Card loading skeleton
export function CardSkeleton({ className }: { className?: string }) {
  return (
    <div className={clsx('card animate-pulse', className)}>
      <div className="card-body space-y-4">
        <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-3/4" />
        <div className="space-y-2">
          <div className="h-3 bg-gray-300 dark:bg-gray-700 rounded" />
          <div className="h-3 bg-gray-300 dark:bg-gray-700 rounded w-5/6" />
        </div>
        <div className="flex space-x-2">
          <div className="h-6 bg-gray-300 dark:bg-gray-700 rounded w-16" />
          <div className="h-6 bg-gray-300 dark:bg-gray-700 rounded w-20" />
        </div>
      </div>
    </div>
  )
}
