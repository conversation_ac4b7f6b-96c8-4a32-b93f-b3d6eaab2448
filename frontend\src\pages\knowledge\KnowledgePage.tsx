/**
 * KnowledgeBot - Corporate Memory System
 * Knowledge Query Page Component
 */

import React from 'react'
import { Helmet } from 'react-helmet-async'

export default function KnowledgePage() {
  return (
    <>
      <Helmet>
        <title>Knowledge - KnowledgeBot</title>
      </Helmet>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            Knowledge Query
          </h1>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Ask questions and get intelligent answers from your knowledge base
          </p>
        </div>
        
        <div className="card">
          <div className="card-body text-center py-12">
            <p className="text-gray-600 dark:text-gray-400">
              Knowledge query interface coming soon...
            </p>
          </div>
        </div>
      </div>
    </>
  )
}
