/**
 * KnowledgeBot - Corporate Memory System
 * Knowledge Query Page Component
 *
 * Interactive knowledge query interface with AI-powered responses,
 * source citations, and related suggestions.
 */

import React, { useState, useRef, useEffect } from 'react'
import { Helmet } from 'react-helmet-async'
import { useMutation, useQuery } from '@tanstack/react-query'
import toast from 'react-hot-toast'
import {
  MagnifyingGlassIcon,
  SparklesIcon,
  DocumentTextIcon,
  ClockIcon,
  LightBulbIcon,
} from '@heroicons/react/24/outline'

import { Button } from '@/components/common/Button'
import { Card, CardHeader, CardTitle, CardBody } from '@/components/common/Card'
import { Textarea } from '@/components/common/Form'
import { LoadingSpinner } from '@/components/common/LoadingSpinner'
import { knowledgeService } from '@/services/knowledgeService'
import { useAuth } from '@/hooks/useAuth'

interface KnowledgeResponse {
  answer: string
  confidence_score: number
  sources: Array<{
    document_id: string
    title: string
    content: string
    similarity_score: number
  }>
  related_queries: string[]
  query_id: string
  processing_time: number
}

interface QueryHistory {
  id: string
  question: string
  answer: string
  timestamp: Date
  confidence_score: number
}

export default function KnowledgePage() {
  const { user } = useAuth()
  const [question, setQuestion] = useState('')
  const [queryHistory, setQueryHistory] = useState<QueryHistory[]>([])
  const [selectedQuery, setSelectedQuery] = useState<QueryHistory | null>(null)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  // Query knowledge mutation
  const queryMutation = useMutation({
    mutationFn: (data: { question: string; max_answer_length?: number }) =>
      knowledgeService.queryKnowledge(data),
    onSuccess: (response: KnowledgeResponse) => {
      const newQuery: QueryHistory = {
        id: response.query_id,
        question,
        answer: response.answer,
        timestamp: new Date(),
        confidence_score: response.confidence_score,
      }
      setQueryHistory(prev => [newQuery, ...prev])
      setSelectedQuery(newQuery)
      setQuestion('')

      if (response.confidence_score < 0.5) {
        toast('Low confidence answer - consider rephrasing your question', {
          icon: '⚠️',
        })
      }
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to process your question')
    },
  })

  // Get search suggestions
  const { data: suggestions } = useQuery({
    queryKey: ['search-suggestions', question],
    queryFn: () => knowledgeService.getSearchSuggestions(question.slice(0, -1)),
    enabled: question.length > 2 && question.endsWith(' '),
    staleTime: 30000,
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (!question.trim()) return

    queryMutation.mutate({
      question: question.trim(),
      max_answer_length: 500,
    })
  }

  const handleSuggestionClick = (suggestion: string) => {
    setQuestion(suggestion)
    textareaRef.current?.focus()
  }

  const handleHistoryClick = (query: QueryHistory) => {
    setSelectedQuery(query)
  }

  const getConfidenceColor = (score: number) => {
    if (score >= 0.8) return 'text-green-600 dark:text-green-400'
    if (score >= 0.6) return 'text-yellow-600 dark:text-yellow-400'
    return 'text-red-600 dark:text-red-400'
  }

  const getConfidenceLabel = (score: number) => {
    if (score >= 0.8) return 'High Confidence'
    if (score >= 0.6) return 'Medium Confidence'
    return 'Low Confidence'
  }

  return (
    <>
      <Helmet>
        <title>Knowledge Query - KnowledgeBot</title>
        <meta name="description" content="Ask questions and get AI-powered answers from your knowledge base" />
      </Helmet>

      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 flex items-center justify-center gap-2">
            <SparklesIcon className="h-8 w-8 text-blue-600" />
            Knowledge Query
          </h1>
          <p className="mt-2 text-lg text-gray-600 dark:text-gray-400">
            Ask questions and get intelligent answers from your knowledge base
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Query Interface */}
          <div className="lg:col-span-2 space-y-6">
            {/* Query Form */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MagnifyingGlassIcon className="h-5 w-5" />
                  Ask a Question
                </CardTitle>
              </CardHeader>
              <CardBody>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <Textarea
                    ref={textareaRef}
                    value={question}
                    onChange={(e) => setQuestion(e.target.value)}
                    placeholder="What would you like to know? Ask about documents, policies, procedures, or any topic in your knowledge base..."
                    rows={4}
                    disabled={queryMutation.isPending}
                  />

                  <div className="flex justify-between items-center">
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {question.length}/1000 characters
                    </div>
                    <Button
                      type="submit"
                      disabled={!question.trim() || queryMutation.isPending}
                      loading={queryMutation.isPending}
                      leftIcon={<SparklesIcon className="h-4 w-4" />}
                    >
                      {queryMutation.isPending ? 'Processing...' : 'Ask Question'}
                    </Button>
                  </div>
                </form>

                {/* Suggestions */}
                {suggestions && suggestions.length > 0 && (
                  <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                    <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Suggestions:
                    </p>
                    <div className="flex flex-wrap gap-2">
                      {suggestions.slice(0, 3).map((suggestion: any, index: number) => (
                        <button
                          key={index}
                          onClick={() => handleSuggestionClick(suggestion.suggestion)}
                          className="px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                        >
                          {suggestion.suggestion}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </CardBody>
            </Card>
