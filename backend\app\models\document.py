"""
KnowledgeBot - Corporate Memory System
Document Model and Related Database Tables

This module defines document models for knowledge management including
document metadata, content chunks, and processing status.
"""

from datetime import datetime
from typing import List, Optional
from sqlalchemy import (
    Boolean, Column, DateTime, String, Text, Integer, Float,
    ForeignKey, Table, Index, CheckConstraint
)
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID, JSONB, ARRAY
import uuid

from app.core.database import Base


# Association tables
document_tags = Table(
    'document_tags',
    Base.metadata,
    Column('document_id', UUID(as_uuid=True), ForeignKey('documents.id'), primary_key=True),
    Column('tag_id', UUID(as_uuid=True), ForeignKey('tags.id'), primary_key=True),
    Column('created_at', DateTime, default=datetime.utcnow),
)

document_departments = Table(
    'document_departments',
    Base.metadata,
    Column('document_id', UUID(as_uuid=True), ForeignKey('documents.id'), primary_key=True),
    Column('department_id', UUID(as_uuid=True), ForeignKey('departments.id'), primary_key=True),
    Column('access_level', String(20), default='read'),  # read, write, admin
)


class Document(Base):
    """
    Document model for storing document metadata and processing information.
    """
    __tablename__ = "documents"
    
    # Primary identification
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    title = Column(String(500), nullable=False)
    filename = Column(String(255), nullable=False)
    original_filename = Column(String(255), nullable=False)
    
    # File information
    file_path = Column(String(1000), nullable=False)
    file_size = Column(Integer, nullable=False)
    file_hash = Column(String(64), nullable=False, index=True)  # SHA-256
    mime_type = Column(String(100), nullable=False)
    
    # Document classification
    document_type = Column(String(50), nullable=True)  # policy, procedure, manual, etc.
    category = Column(String(100), nullable=True)
    subcategory = Column(String(100), nullable=True)
    
    # Content information
    language = Column(String(10), default='en')
    page_count = Column(Integer, nullable=True)
    word_count = Column(Integer, nullable=True)
    character_count = Column(Integer, nullable=True)
    
    # Processing status
    processing_status = Column(String(20), default='pending')  # pending, processing, completed, failed
    processing_started_at = Column(DateTime, nullable=True)
    processing_completed_at = Column(DateTime, nullable=True)
    processing_error = Column(Text, nullable=True)
    processing_metadata = Column(JSONB, default=dict)
    
    # MemVid encoding information
    memvid_encoded = Column(Boolean, default=False)
    memvid_model_version = Column(String(50), nullable=True)
    memvid_encoding_metadata = Column(JSONB, default=dict)
    total_chunks = Column(Integer, default=0)
    
    # Access control and security
    is_public = Column(Boolean, default=False)
    is_sensitive = Column(Boolean, default=False)
    classification_level = Column(String(20), default='internal')  # public, internal, confidential, restricted
    access_permissions = Column(JSONB, default=dict)
    
    # Version control
    version = Column(String(20), default='1.0')
    parent_document_id = Column(UUID(as_uuid=True), ForeignKey('documents.id'), nullable=True)
    is_latest_version = Column(Boolean, default=True)
    
    # Document lifecycle
    status = Column(String(20), default='active')  # active, archived, deleted
    published_at = Column(DateTime, nullable=True)
    expires_at = Column(DateTime, nullable=True)
    review_due_date = Column(DateTime, nullable=True)
    
    # Metadata and custom fields
    description = Column(Text, nullable=True)
    summary = Column(Text, nullable=True)
    keywords = Column(ARRAY(String), default=list)
    custom_metadata = Column(JSONB, default=dict)
    
    # Search and discovery
    search_vector = Column(Text, nullable=True)  # For full-text search
    popularity_score = Column(Float, default=0.0)
    relevance_score = Column(Float, default=0.0)
    
    # Audit fields
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    uploaded_by = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False)
    updated_by = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=True)
    
    # Soft delete
    deleted_at = Column(DateTime, nullable=True)
    deleted_by = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=True)
    
    # Relationships
    uploader = relationship("User", foreign_keys=[uploaded_by], backref="uploaded_documents")
    updater = relationship("User", foreign_keys=[updated_by])
    parent_document = relationship("Document", remote_side=[id], backref="versions")
    chunks = relationship("DocumentChunk", back_populates="document", cascade="all, delete-orphan")
    tags = relationship("Tag", secondary=document_tags, back_populates="documents")
    departments = relationship("Department", secondary=document_departments, back_populates="documents")
    
    # Indexes and constraints
    __table_args__ = (
        Index('idx_document_status_type', 'status', 'document_type'),
        Index('idx_document_processing', 'processing_status', 'created_at'),
        Index('idx_document_memvid', 'memvid_encoded', 'processing_status'),
        Index('idx_document_classification', 'classification_level', 'is_public'),
        Index('idx_document_search', 'title', 'category'),
        CheckConstraint('file_size > 0', name='check_positive_file_size'),
        CheckConstraint('page_count >= 0', name='check_non_negative_page_count'),
    )
    
    @property
    def is_processed(self) -> bool:
        """Check if document processing is completed."""
        return self.processing_status == 'completed'
    
    @property
    def processing_duration(self) -> Optional[float]:
        """Get processing duration in seconds."""
        if self.processing_started_at and self.processing_completed_at:
            delta = self.processing_completed_at - self.processing_started_at
            return delta.total_seconds()
        return None


class DocumentChunk(Base):
    """
    Document chunk model for storing processed document segments with MemVid encoding.
    """
    __tablename__ = "document_chunks"
    
    # Primary identification
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    document_id = Column(UUID(as_uuid=True), ForeignKey('documents.id'), nullable=False)
    
    # Chunk information
    chunk_index = Column(Integer, nullable=False)
    chunk_type = Column(String(20), default='text')  # text, table, image, header
    
    # Content
    content = Column(Text, nullable=False)
    content_hash = Column(String(64), nullable=False)  # SHA-256 of content
    
    # Position and context
    start_page = Column(Integer, nullable=True)
    end_page = Column(Integer, nullable=True)
    start_position = Column(Integer, nullable=True)
    end_position = Column(Integer, nullable=True)
    
    # MemVid encoding
    memvid_embedding = Column(ARRAY(Float), nullable=True)  # Vector embedding
    memvid_metadata = Column(JSONB, default=dict)
    embedding_model = Column(String(50), nullable=True)
    embedding_dimension = Column(Integer, nullable=True)
    
    # Content analysis
    language = Column(String(10), nullable=True)
    word_count = Column(Integer, nullable=True)
    character_count = Column(Integer, nullable=True)
    sentiment_score = Column(Float, nullable=True)
    
    # Relationships and references
    parent_chunk_id = Column(UUID(as_uuid=True), ForeignKey('document_chunks.id'), nullable=True)
    related_chunks = Column(ARRAY(UUID), default=list)
    
    # Processing metadata
    processing_metadata = Column(JSONB, default=dict)
    quality_score = Column(Float, nullable=True)
    confidence_score = Column(Float, nullable=True)
    
    # Audit fields
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    document = relationship("Document", back_populates="chunks")
    parent_chunk = relationship("DocumentChunk", remote_side=[id], backref="child_chunks")
    
    # Indexes
    __table_args__ = (
        Index('idx_chunk_document_index', 'document_id', 'chunk_index'),
        Index('idx_chunk_type_page', 'chunk_type', 'start_page'),
        Index('idx_chunk_content_hash', 'content_hash'),
        Index('idx_chunk_memvid', 'document_id', 'memvid_embedding'),
    )


class Tag(Base):
    """
    Tag model for document categorization and organization.
    """
    __tablename__ = "tags"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(100), unique=True, nullable=False)
    display_name = Column(String(200), nullable=True)
    description = Column(Text, nullable=True)
    
    # Tag hierarchy and categorization
    parent_tag_id = Column(UUID(as_uuid=True), ForeignKey('tags.id'), nullable=True)
    tag_type = Column(String(50), default='general')  # general, system, auto, custom
    color = Column(String(7), nullable=True)  # Hex color code
    
    # Usage statistics
    usage_count = Column(Integer, default=0)
    last_used = Column(DateTime, nullable=True)
    
    # Audit fields
    created_at = Column(DateTime, default=datetime.utcnow)
    created_by = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=True)
    
    # Relationships
    documents = relationship("Document", secondary=document_tags, back_populates="tags")
    parent_tag = relationship("Tag", remote_side=[id], backref="child_tags")
    creator = relationship("User", backref="created_tags")
    
    def __repr__(self):
        return f"<Tag {self.name}>"


class DocumentAccess(Base):
    """
    Document access log for audit and analytics.
    """
    __tablename__ = "document_access"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    document_id = Column(UUID(as_uuid=True), ForeignKey('documents.id'), nullable=False)
    user_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=True)
    
    # Access information
    access_type = Column(String(20), nullable=False)  # view, download, search, query
    access_method = Column(String(50), nullable=True)  # web, api, bot, mobile
    
    # Request details
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(Text, nullable=True)
    referer = Column(String(500), nullable=True)
    
    # Context
    query_text = Column(Text, nullable=True)  # For search/query access
    response_time = Column(Float, nullable=True)  # Response time in seconds
    
    # Audit fields
    accessed_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # Relationships
    document = relationship("Document", backref="access_logs")
    user = relationship("User", backref="document_accesses")
    
    # Indexes
    __table_args__ = (
        Index('idx_access_document_time', 'document_id', 'accessed_at'),
        Index('idx_access_user_time', 'user_id', 'accessed_at'),
        Index('idx_access_type_time', 'access_type', 'accessed_at'),
    )
