"""
KnowledgeBot - Corporate Memory System
Slack Bot Integration

This module implements the Slack bot for enterprise knowledge queries
with secure authentication and departmental access control.
"""

import os
import asyncio
import logging
from typing import Dict, Any, Optional, List
from slack_bolt.async_app import AsyncApp
from slack_bolt.adapter.socket_mode.async_handler import AsyncSock<PERSON><PERSON>odeHandler
from slack_sdk.web.async_client import AsyncWebClient
from slack_sdk.errors import SlackApiError

from shared.bot_utils import (
    KnowledgeBotClient,
    format_knowledge_response,
    validate_user_access,
    log_bot_interaction,
    extract_mentions,
    sanitize_input
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Slack app
app = AsyncApp(
    token=os.environ.get("SLACK_BOT_TOKEN"),
    signing_secret=os.environ.get("SLACK_SIGNING_SECRET")
)

# Initialize KnowledgeBot API client
kb_client = KnowledgeBotClient(
    api_url=os.environ.get("KNOWLEDGEBOT_API_URL", "http://localhost:8000/api/v1"),
    api_key=os.environ.get("KNOWLEDGEBOT_API_KEY")
)


class SlackKnowledgeBot:
    """
    Slack bot implementation for KnowledgeBot enterprise knowledge queries.
    """
    
    def __init__(self, app: AsyncApp, kb_client: KnowledgeBotClient):
        self.app = app
        self.kb_client = kb_client
        self.setup_handlers()
    
    def setup_handlers(self):
        """Set up Slack event handlers."""
        
        # Handle app mentions
        @self.app.event("app_mention")
        async def handle_app_mention(event, say, client: AsyncWebClient):
            await self.handle_knowledge_query(event, say, client)
        
        # Handle direct messages
        @self.app.event("message")
        async def handle_direct_message(event, say, client: AsyncWebClient):
            # Only respond to direct messages, not channel messages
            if event.get("channel_type") == "im":
                await self.handle_knowledge_query(event, say, client)
        
        # Handle slash commands
        @self.app.command("/kb")
        async def handle_slash_command(ack, respond, command, client: AsyncWebClient):
            await ack()
            await self.handle_slash_query(respond, command, client)
        
        # Handle interactive components (buttons, modals)
        @self.app.action("knowledge_feedback")
        async def handle_feedback(ack, body, client: AsyncWebClient):
            await ack()
            await self.handle_user_feedback(body, client)
        
        # Handle app home opened
        @self.app.event("app_home_opened")
        async def handle_app_home(event, client: AsyncWebClient):
            await self.update_app_home(event["user"], client)
    
    async def handle_knowledge_query(self, event: Dict[str, Any], say, client: AsyncWebClient):
        """
        Handle knowledge queries from mentions or direct messages.
        
        Args:
            event: Slack event data
            say: Slack say function
            client: Slack web client
        """
        try:
            user_id = event["user"]
            channel_id = event["channel"]
            text = event.get("text", "")
            
            # Extract the actual query (remove bot mention)
            query = self.extract_query_from_text(text)
            
            if not query.strip():
                await say(
                    text="Hi! I'm KnowledgeBot. Ask me anything about your company's knowledge base!",
                    thread_ts=event.get("ts")
                )
                return
            
            # Show typing indicator
            await client.chat_postMessage(
                channel=channel_id,
                text="🤔 Searching the knowledge base...",
                thread_ts=event.get("ts")
            )
            
            # Get user information
            user_info = await self.get_user_info(user_id, client)
            if not user_info:
                await say(
                    text="❌ Sorry, I couldn't verify your identity. Please contact your administrator.",
                    thread_ts=event.get("ts")
                )
                return
            
            # Validate user access
            if not await validate_user_access(user_info["email"], self.kb_client):
                await say(
                    text="❌ You don't have access to the knowledge base. Please contact your administrator.",
                    thread_ts=event.get("ts")
                )
                return
            
            # Query the knowledge base
            response = await self.kb_client.query_knowledge(
                query=query,
                user_email=user_info["email"],
                context={
                    "platform": "slack",
                    "channel_id": channel_id,
                    "user_id": user_id
                }
            )
            
            # Format and send response
            formatted_response = await self.format_slack_response(response, query)
            await say(
                **formatted_response,
                thread_ts=event.get("ts")
            )
            
            # Log interaction
            await log_bot_interaction(
                platform="slack",
                user_id=user_id,
                query=query,
                response=response,
                kb_client=self.kb_client
            )
            
        except Exception as e:
            logger.error(f"Error handling knowledge query: {e}")
            await say(
                text="❌ Sorry, I encountered an error while processing your request. Please try again later.",
                thread_ts=event.get("ts")
            )
    
    async def handle_slash_query(self, respond, command: Dict[str, Any], client: AsyncWebClient):
        """
        Handle slash command queries.
        
        Args:
            respond: Slack respond function
            command: Slash command data
            client: Slack web client
        """
        try:
            user_id = command["user_id"]
            query = command.get("text", "").strip()
            
            if not query:
                await respond({
                    "response_type": "ephemeral",
                    "text": "Please provide a question. Example: `/kb What is our vacation policy?`"
                })
                return
            
            # Get user information
            user_info = await self.get_user_info(user_id, client)
            if not user_info:
                await respond({
                    "response_type": "ephemeral",
                    "text": "❌ Sorry, I couldn't verify your identity."
                })
                return
            
            # Show loading message
            await respond({
                "response_type": "ephemeral",
                "text": "🤔 Searching the knowledge base..."
            })
            
            # Query the knowledge base
            response = await self.kb_client.query_knowledge(
                query=query,
                user_email=user_info["email"],
                context={
                    "platform": "slack",
                    "command": "slash",
                    "user_id": user_id
                }
            )
            
            # Format and send response
            formatted_response = await self.format_slack_response(response, query)
            formatted_response["response_type"] = "in_channel"  # Make visible to channel
            
            await respond(formatted_response)
            
            # Log interaction
            await log_bot_interaction(
                platform="slack",
                user_id=user_id,
                query=query,
                response=response,
                kb_client=self.kb_client
            )
            
        except Exception as e:
            logger.error(f"Error handling slash command: {e}")
            await respond({
                "response_type": "ephemeral",
                "text": "❌ Sorry, I encountered an error while processing your request."
            })
    
    async def format_slack_response(self, response: Dict[str, Any], query: str) -> Dict[str, Any]:
        """
        Format knowledge base response for Slack.
        
        Args:
            response: Knowledge base response
            query: Original query
            
        Returns:
            Dict[str, Any]: Formatted Slack message
        """
        if not response.get("sources"):
            return {
                "text": "🤷‍♂️ I couldn't find any relevant information for your question. Try rephrasing or contact your administrator.",
                "blocks": [
                    {
                        "type": "section",
                        "text": {
                            "type": "mrkdwn",
                            "text": "🤷‍♂️ I couldn't find any relevant information for your question."
                        }
                    },
                    {
                        "type": "context",
                        "elements": [
                            {
                                "type": "mrkdwn",
                                "text": f"*Query:* {query}"
                            }
                        ]
                    }
                ]
            }
        
        # Build blocks for rich formatting
        blocks = [
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"💡 *Answer:*\n{response['answer']}"
                }
            }
        ]
        
        # Add confidence score if available
        if response.get("confidence_score"):
            confidence = response["confidence_score"]
            confidence_emoji = "🟢" if confidence > 0.8 else "🟡" if confidence > 0.6 else "🔴"
            blocks.append({
                "type": "context",
                "elements": [
                    {
                        "type": "mrkdwn",
                        "text": f"{confidence_emoji} *Confidence:* {confidence:.1%}"
                    }
                ]
            })
        
        # Add sources
        if response.get("sources"):
            sources_text = "\n".join([
                f"• <{source.get('url', '#')}|{source['title']}> ({source.get('category', 'Document')})"
                for source in response["sources"][:5]  # Limit to 5 sources
            ])
            
            blocks.append({
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"📚 *Sources:*\n{sources_text}"
                }
            })
        
        # Add feedback buttons
        blocks.append({
            "type": "actions",
            "elements": [
                {
                    "type": "button",
                    "text": {
                        "type": "plain_text",
                        "text": "👍 Helpful"
                    },
                    "style": "primary",
                    "action_id": "knowledge_feedback",
                    "value": f"helpful:{response.get('query_id', '')}"
                },
                {
                    "type": "button",
                    "text": {
                        "type": "plain_text",
                        "text": "👎 Not Helpful"
                    },
                    "action_id": "knowledge_feedback",
                    "value": f"not_helpful:{response.get('query_id', '')}"
                }
            ]
        })
        
        return {
            "text": f"Answer: {response['answer'][:100]}...",  # Fallback text
            "blocks": blocks
        }
    
    async def handle_user_feedback(self, body: Dict[str, Any], client: AsyncWebClient):
        """
        Handle user feedback on responses.
        
        Args:
            body: Slack interaction body
            client: Slack web client
        """
        try:
            action = body["actions"][0]
            feedback_value = action["value"]
            user_id = body["user"]["id"]
            
            feedback_type, query_id = feedback_value.split(":", 1)
            
            # Send feedback to knowledge base
            await self.kb_client.submit_feedback(
                query_id=query_id,
                feedback_type=feedback_type,
                user_id=user_id,
                platform="slack"
            )
            
            # Update message to show feedback received
            feedback_text = "👍 Thanks for your feedback!" if feedback_type == "helpful" else "👎 Thanks for your feedback. We'll work to improve our responses."
            
            await client.chat_update(
                channel=body["channel"]["id"],
                ts=body["message"]["ts"],
                text=body["message"]["text"],
                blocks=body["message"]["blocks"][:-1] + [{  # Replace action block
                    "type": "context",
                    "elements": [
                        {
                            "type": "mrkdwn",
                            "text": feedback_text
                        }
                    ]
                }]
            )
            
        except Exception as e:
            logger.error(f"Error handling feedback: {e}")
    
    async def update_app_home(self, user_id: str, client: AsyncWebClient):
        """
        Update the app home tab for a user.
        
        Args:
            user_id: Slack user ID
            client: Slack web client
        """
        try:
            home_view = {
                "type": "home",
                "blocks": [
                    {
                        "type": "header",
                        "text": {
                            "type": "plain_text",
                            "text": "🤖 KnowledgeBot"
                        }
                    },
                    {
                        "type": "section",
                        "text": {
                            "type": "mrkdwn",
                            "text": "*Welcome to KnowledgeBot!*\n\nI can help you find information from your company's knowledge base. Here's how to use me:"
                        }
                    },
                    {
                        "type": "section",
                        "text": {
                            "type": "mrkdwn",
                            "text": "• *Mention me* in any channel: `@KnowledgeBot What is our vacation policy?`\n• *Send me a DM* with your question\n• *Use the slash command*: `/kb What are the office hours?`"
                        }
                    },
                    {
                        "type": "divider"
                    },
                    {
                        "type": "section",
                        "text": {
                            "type": "mrkdwn",
                            "text": "*Recent Popular Queries:*"
                        }
                    }
                ]
            }
            
            # Get popular queries
            try:
                popular_queries = await self.kb_client.get_popular_queries(limit=5)
                for query in popular_queries:
                    home_view["blocks"].append({
                        "type": "section",
                        "text": {
                            "type": "mrkdwn",
                            "text": f"• {query['query']} _{query['count']} times_"
                        }
                    })
            except Exception:
                home_view["blocks"].append({
                    "type": "section",
                    "text": {
                        "type": "mrkdwn",
                        "text": "• No recent queries available"
                    }
                })
            
            await client.views_publish(
                user_id=user_id,
                view=home_view
            )
            
        except Exception as e:
            logger.error(f"Error updating app home: {e}")
    
    async def get_user_info(self, user_id: str, client: AsyncWebClient) -> Optional[Dict[str, Any]]:
        """
        Get user information from Slack.
        
        Args:
            user_id: Slack user ID
            client: Slack web client
            
        Returns:
            Optional[Dict[str, Any]]: User information or None if error
        """
        try:
            response = await client.users_info(user=user_id)
            user = response["user"]
            
            return {
                "id": user["id"],
                "name": user["name"],
                "real_name": user.get("real_name", ""),
                "email": user["profile"].get("email", ""),
                "is_admin": user.get("is_admin", False),
                "is_owner": user.get("is_owner", False)
            }
        except SlackApiError as e:
            logger.error(f"Error getting user info: {e}")
            return None
    
    def extract_query_from_text(self, text: str) -> str:
        """
        Extract query from Slack message text, removing bot mentions.
        
        Args:
            text: Raw message text
            
        Returns:
            str: Cleaned query text
        """
        # Remove bot mentions
        import re
        text = re.sub(r'<@[A-Z0-9]+>', '', text).strip()
        
        # Sanitize input
        return sanitize_input(text)


# Initialize bot
slack_bot = SlackKnowledgeBot(app, kb_client)


async def main():
    """Main function to start the Slack bot."""
    handler = AsyncSocketModeHandler(app, os.environ["SLACK_APP_TOKEN"])
    await handler.start_async()


if __name__ == "__main__":
    asyncio.run(main())
