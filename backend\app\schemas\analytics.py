"""
KnowledgeBot - Corporate Memory System
Analytics Pydantic Schemas

This module defines Pydantic models for analytics and reporting API requests
and responses with comprehensive validation and serialization.
"""

from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from uuid import UUID
from pydantic import BaseModel, Field, validator

from app.schemas.base import BaseResponse


class AnalyticsMetric(BaseModel):
    """Schema for individual analytics metrics."""
    name: str
    value: Union[int, float, str]
    change_percent: Optional[float] = None
    trend: Optional[str] = Field(None, regex="^(up|down|stable)$")
    metadata: Optional[Dict[str, Any]] = None


class DashboardMetricsResponse(BaseModel):
    """Schema for dashboard metrics responses."""
    metrics: List[AnalyticsMetric]
    period_days: int
    generated_at: str


class UsageReportResponse(BaseModel):
    """Schema for usage report responses."""
    period: str
    total_queries: int
    total_searches: int
    total_documents: int
    active_users: int
    top_queries: List[Dict[str, Any]]
    top_documents: List[Dict[str, Any]]
    department_usage: List[Dict[str, Any]]
    performance_metrics: Dict[str, Any]
    generated_at: str


class PopularQueriesResponse(BaseModel):
    """Schema for popular queries responses."""
    queries: List[Dict[str, Any]]
    period_days: int
    generated_at: str


class UserActivityResponse(BaseModel):
    """Schema for user activity responses."""
    user_id: str
    period_days: int
    activity_by_type: Dict[str, int]
    daily_activity: Dict[str, Dict[str, int]]
    top_documents: List[Dict[str, Any]]
    total_activities: int
    generated_at: str


class DepartmentAnalyticsResponse(BaseModel):
    """Schema for department analytics responses."""
    department_id: str
    period_days: int
    total_activities: int
    activity_breakdown: Dict[str, Dict[str, Any]]
    popular_queries: List[Dict[str, Any]]
    total_users: int
    generated_at: str


class QueryTrendData(BaseModel):
    """Schema for query trend data points."""
    period: str
    count: int
    unique_users: Optional[int] = None
    average_response_time: Optional[float] = None


class QueryTrendsResponse(BaseModel):
    """Schema for query trends responses."""
    trends: List[QueryTrendData]
    interval: str
    period_days: int
    generated_at: str


class ResponseTimeStats(BaseModel):
    """Schema for response time statistics."""
    average_ms: float
    minimum_ms: float
    maximum_ms: float
    median_ms: float
    p95_ms: float
    total_requests: int


class ResponseTimeAnalyticsResponse(BaseModel):
    """Schema for response time analytics responses."""
    statistics: ResponseTimeStats
    period_days: int
    generated_at: str


class DocumentAccessStats(BaseModel):
    """Schema for document access statistics."""
    document_id: UUID
    title: str
    category: Optional[str] = None
    access_count: int
    unique_users: int
    last_accessed: datetime
    average_rating: Optional[float] = None


class DocumentAnalyticsResponse(BaseModel):
    """Schema for document analytics responses."""
    most_accessed: List[DocumentAccessStats]
    least_accessed: List[DocumentAccessStats]
    by_category: Dict[str, int]
    by_department: Dict[str, int]
    by_type: Dict[str, int]
    period_days: int
    generated_at: str


class SearchAnalyticsRequest(BaseModel):
    """Schema for search analytics requests."""
    period: str = Field("30d", regex="^(1d|7d|30d|90d|1y)$")
    group_by: str = Field("day", regex="^(hour|day|week|month)$")
    include_failed: bool = Field(False, description="Include failed searches")
    department_filter: Optional[List[UUID]] = None


class SearchAnalyticsResponse(BaseModel):
    """Schema for search analytics responses."""
    total_searches: int
    successful_searches: int
    failed_searches: int
    average_results_per_search: float
    most_common_terms: List[Dict[str, Any]]
    search_trends: List[QueryTrendData]
    no_results_queries: List[str]
    period: str
    generated_at: str


class UserEngagementMetrics(BaseModel):
    """Schema for user engagement metrics."""
    daily_active_users: int
    weekly_active_users: int
    monthly_active_users: int
    average_session_duration: float
    queries_per_user: float
    documents_per_user: float
    retention_rate: float


class UserEngagementResponse(BaseModel):
    """Schema for user engagement responses."""
    metrics: UserEngagementMetrics
    engagement_trends: List[Dict[str, Any]]
    user_segments: Dict[str, int]
    period_days: int
    generated_at: str


class ContentAnalyticsRequest(BaseModel):
    """Schema for content analytics requests."""
    content_type: Optional[str] = Field(None, regex="^(document|query|search)$")
    period: str = Field("30d", regex="^(1d|7d|30d|90d|1y)$")
    include_metadata: bool = Field(True, description="Include content metadata")
    department_filter: Optional[List[UUID]] = None


class ContentPerformanceMetrics(BaseModel):
    """Schema for content performance metrics."""
    total_content_items: int
    most_viewed_content: List[Dict[str, Any]]
    content_by_category: Dict[str, int]
    content_by_type: Dict[str, int]
    average_engagement_score: float
    content_freshness_score: float


class ContentAnalyticsResponse(BaseModel):
    """Schema for content analytics responses."""
    performance: ContentPerformanceMetrics
    trends: List[Dict[str, Any]]
    recommendations: List[str]
    period: str
    generated_at: str


class SystemPerformanceMetrics(BaseModel):
    """Schema for system performance metrics."""
    average_response_time: float
    error_rate: float
    throughput: float
    uptime_percentage: float
    memory_usage: float
    cpu_usage: float
    disk_usage: float
    active_connections: int


class SystemPerformanceResponse(BaseModel):
    """Schema for system performance responses."""
    current_metrics: SystemPerformanceMetrics
    historical_trends: List[Dict[str, Any]]
    alerts: List[Dict[str, Any]]
    recommendations: List[str]
    generated_at: str


class ExportAnalyticsRequest(BaseModel):
    """Schema for analytics export requests."""
    report_type: str = Field(..., regex="^(usage|queries|users|documents|performance)$")
    format: str = Field("csv", regex="^(csv|xlsx|json|pdf)$")
    period: str = Field("30d", regex="^(1d|7d|30d|90d|1y)$")
    include_charts: bool = Field(False, description="Include charts in export")
    filters: Optional[Dict[str, Any]] = None


class ExportAnalyticsResponse(BaseModel):
    """Schema for analytics export responses."""
    export_id: UUID
    report_type: str
    format: str
    status: str
    download_url: Optional[str] = None
    file_size: Optional[int] = None
    expires_at: Optional[datetime] = None
    created_at: datetime


class CustomReportRequest(BaseModel):
    """Schema for custom report requests."""
    name: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=1000)
    metrics: List[str] = Field(..., min_items=1)
    filters: Optional[Dict[str, Any]] = None
    grouping: Optional[List[str]] = None
    time_range: str = Field("30d", regex="^(1d|7d|30d|90d|1y)$")
    schedule: Optional[str] = Field(None, regex="^(daily|weekly|monthly)$")
    recipients: Optional[List[str]] = None


class CustomReportResponse(BaseModel):
    """Schema for custom report responses."""
    report_id: UUID
    name: str
    description: Optional[str] = None
    status: str
    last_generated: Optional[datetime] = None
    next_scheduled: Optional[datetime] = None
    created_at: datetime
    created_by: UUID


class ReportScheduleRequest(BaseModel):
    """Schema for report schedule requests."""
    report_id: UUID
    schedule: str = Field(..., regex="^(daily|weekly|monthly)$")
    recipients: List[str] = Field(..., min_items=1)
    format: str = Field("pdf", regex="^(pdf|csv|xlsx)$")
    enabled: bool = True


class ReportScheduleResponse(BaseModel):
    """Schema for report schedule responses."""
    schedule_id: UUID
    report_id: UUID
    schedule: str
    recipients: List[str]
    format: str
    enabled: bool
    last_sent: Optional[datetime] = None
    next_send: Optional[datetime] = None
    created_at: datetime


class AnalyticsInsight(BaseModel):
    """Schema for analytics insights."""
    type: str
    title: str
    description: str
    impact: str = Field(..., regex="^(low|medium|high|critical)$")
    recommendation: Optional[str] = None
    data: Dict[str, Any]
    confidence: float = Field(..., ge=0.0, le=1.0)


class AnalyticsInsightsResponse(BaseModel):
    """Schema for analytics insights responses."""
    insights: List[AnalyticsInsight]
    summary: Dict[str, Any]
    generated_at: str


class ComparisonAnalyticsRequest(BaseModel):
    """Schema for comparison analytics requests."""
    metric: str
    compare_periods: List[str] = Field(..., min_items=2, max_items=4)
    group_by: Optional[str] = Field(None, regex="^(department|user|document_type|category)$")
    filters: Optional[Dict[str, Any]] = None


class ComparisonAnalyticsResponse(BaseModel):
    """Schema for comparison analytics responses."""
    metric: str
    periods: List[str]
    data: List[Dict[str, Any]]
    summary: Dict[str, Any]
    insights: List[str]
    generated_at: str


class RealTimeMetrics(BaseModel):
    """Schema for real-time metrics."""
    active_users: int
    queries_per_minute: float
    searches_per_minute: float
    response_time_ms: float
    error_rate: float
    system_load: float
    timestamp: datetime


class RealTimeMetricsResponse(BaseModel):
    """Schema for real-time metrics responses."""
    current: RealTimeMetrics
    history: List[RealTimeMetrics]
    alerts: List[Dict[str, Any]]
    thresholds: Dict[str, float]
