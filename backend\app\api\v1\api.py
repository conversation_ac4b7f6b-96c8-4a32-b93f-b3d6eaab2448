"""
KnowledgeBot - Corporate Memory System
API Router Configuration

This module configures all API routes for the v1 API endpoints.
"""

from fastapi import APIRouter

from app.api.v1.endpoints import (
    auth,
    users,
    documents,
    knowledge,
    admin,
    health,
    analytics
)

api_router = APIRouter()

# Authentication endpoints
api_router.include_router(
    auth.router,
    prefix="/auth",
    tags=["authentication"]
)

# User management endpoints
api_router.include_router(
    users.router,
    prefix="/users",
    tags=["users"]
)

# Document management endpoints
api_router.include_router(
    documents.router,
    prefix="/documents",
    tags=["documents"]
)

# Knowledge query endpoints
api_router.include_router(
    knowledge.router,
    prefix="/knowledge",
    tags=["knowledge"]
)

# Admin endpoints
api_router.include_router(
    admin.router,
    prefix="/admin",
    tags=["admin"]
)

# Health check endpoints
api_router.include_router(
    health.router,
    prefix="/health",
    tags=["health"]
)

# Analytics endpoints
api_router.include_router(
    analytics.router,
    prefix="/analytics",
    tags=["analytics"]
)
