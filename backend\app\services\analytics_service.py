"""
KnowledgeBot - Corporate Memory System
Analytics Service for Enterprise Reporting

This module provides comprehensive analytics and reporting capabilities
for usage tracking, performance monitoring, and business intelligence.
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
from uuid import UUID
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, desc, text
from sqlalchemy.orm import selectinload
from dataclasses import dataclass
import json

from app.models.document import Document, DocumentAccess
from app.models.user import User, Department
from app.core.config import settings

logger = logging.getLogger(__name__)


@dataclass
class AnalyticsMetric:
    """Analytics metric data structure."""
    name: str
    value: Any
    change_percent: Optional[float] = None
    trend: Optional[str] = None  # "up", "down", "stable"
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class UsageReport:
    """Usage report data structure."""
    period: str
    total_queries: int
    total_searches: int
    total_documents: int
    active_users: int
    top_queries: List[Dict[str, Any]]
    top_documents: List[Dict[str, Any]]
    department_usage: List[Dict[str, Any]]
    performance_metrics: Dict[str, Any]


class AnalyticsService:
    """
    Enterprise analytics service for usage tracking and reporting.
    """
    
    def __init__(self):
        self.cache_ttl = 300  # 5 minutes cache
        self._cache = {}
    
    async def get_dashboard_metrics(
        self,
        db: AsyncSession,
        user_departments: List[UUID],
        days: int = 30
    ) -> List[AnalyticsMetric]:
        """
        Get key metrics for the analytics dashboard.
        
        Args:
            db: Database session
            user_departments: User's accessible departments
            days: Number of days to analyze
            
        Returns:
            List[AnalyticsMetric]: Key performance metrics
        """
        try:
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=days)
            prev_start_date = start_date - timedelta(days=days)
            
            metrics = []
            
            # Total queries metric
            current_queries = await self._get_query_count(db, start_date, end_date, user_departments)
            previous_queries = await self._get_query_count(db, prev_start_date, start_date, user_departments)
            
            query_change = self._calculate_change_percent(current_queries, previous_queries)
            metrics.append(AnalyticsMetric(
                name="Total Queries",
                value=current_queries,
                change_percent=query_change,
                trend=self._get_trend(query_change)
            ))
            
            # Active users metric
            current_users = await self._get_active_users_count(db, start_date, end_date, user_departments)
            previous_users = await self._get_active_users_count(db, prev_start_date, start_date, user_departments)
            
            users_change = self._calculate_change_percent(current_users, previous_users)
            metrics.append(AnalyticsMetric(
                name="Active Users",
                value=current_users,
                change_percent=users_change,
                trend=self._get_trend(users_change)
            ))
            
            # Document count metric
            doc_count = await self._get_document_count(db, user_departments)
            metrics.append(AnalyticsMetric(
                name="Total Documents",
                value=doc_count,
                metadata={"processed": await self._get_processed_document_count(db, user_departments)}
            ))
            
            # Average response time metric
            avg_response_time = await self._get_average_response_time(db, start_date, end_date, user_departments)
            metrics.append(AnalyticsMetric(
                name="Avg Response Time",
                value=f"{avg_response_time:.2f}s",
                metadata={"raw_value": avg_response_time}
            ))
            
            # Success rate metric
            success_rate = await self._get_success_rate(db, start_date, end_date, user_departments)
            metrics.append(AnalyticsMetric(
                name="Success Rate",
                value=f"{success_rate:.1f}%",
                trend="up" if success_rate > 90 else "down" if success_rate < 70 else "stable"
            ))
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error getting dashboard metrics: {e}")
            return []
    
    async def get_usage_report(
        self,
        db: AsyncSession,
        user_departments: List[UUID],
        period: str = "monthly"
    ) -> UsageReport:
        """
        Generate comprehensive usage report.
        
        Args:
            db: Database session
            user_departments: User's accessible departments
            period: Report period (daily, weekly, monthly)
            
        Returns:
            UsageReport: Comprehensive usage report
        """
        try:
            # Calculate date range based on period
            end_date = datetime.utcnow()
            if period == "daily":
                start_date = end_date - timedelta(days=1)
            elif period == "weekly":
                start_date = end_date - timedelta(days=7)
            else:  # monthly
                start_date = end_date - timedelta(days=30)
            
            # Gather report data
            total_queries = await self._get_query_count(db, start_date, end_date, user_departments)
            total_searches = await self._get_search_count(db, start_date, end_date, user_departments)
            total_documents = await self._get_document_count(db, user_departments)
            active_users = await self._get_active_users_count(db, start_date, end_date, user_departments)
            
            top_queries = await self.get_popular_queries(db, 10, 30, user_departments)
            top_documents = await self._get_popular_documents(db, start_date, end_date, user_departments, 10)
            department_usage = await self._get_department_usage(db, start_date, end_date, user_departments)
            performance_metrics = await self._get_performance_metrics(db, start_date, end_date, user_departments)
            
            return UsageReport(
                period=period,
                total_queries=total_queries,
                total_searches=total_searches,
                total_documents=total_documents,
                active_users=active_users,
                top_queries=top_queries,
                top_documents=top_documents,
                department_usage=department_usage,
                performance_metrics=performance_metrics
            )
            
        except Exception as e:
            logger.error(f"Error generating usage report: {e}")
            return UsageReport(
                period=period,
                total_queries=0,
                total_searches=0,
                total_documents=0,
                active_users=0,
                top_queries=[],
                top_documents=[],
                department_usage=[],
                performance_metrics={}
            )
    
    async def get_popular_queries(
        self,
        db: AsyncSession,
        limit: int = 10,
        days: int = 30,
        user_departments: List[UUID] = None
    ) -> List[Dict[str, Any]]:
        """Get most popular queries with usage statistics."""
        try:
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=days)
            
            query = select(
                DocumentAccess.query_text,
                func.count(DocumentAccess.id).label('count'),
                func.count(func.distinct(DocumentAccess.user_id)).label('unique_users'),
                func.avg(DocumentAccess.response_time).label('avg_response_time')
            ).where(
                and_(
                    DocumentAccess.query_text.isnot(None),
                    DocumentAccess.access_type == "query",
                    DocumentAccess.accessed_at >= start_date,
                    DocumentAccess.accessed_at <= end_date
                )
            ).group_by(DocumentAccess.query_text).order_by(desc('count')).limit(limit)
            
            result = await db.execute(query)
            
            popular_queries = []
            for row in result.fetchall():
                popular_queries.append({
                    "query": row[0],
                    "count": row[1],
                    "unique_users": row[2],
                    "avg_response_time": float(row[3]) if row[3] else 0.0
                })
            
            return popular_queries
            
        except Exception as e:
            logger.error(f"Error getting popular queries: {e}")
            return []
    
    async def update_query_analytics(
        self,
        query: str,
        result_count: int,
        confidence_score: float
    ):
        """Update analytics for a knowledge query (background task)."""
        try:
            # This would typically update a separate analytics table
            # or send data to an analytics service like Google Analytics
            analytics_data = {
                "query": query,
                "result_count": result_count,
                "confidence_score": confidence_score,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            # Log for now - in production, this would go to analytics service
            logger.info(f"Query analytics: {json.dumps(analytics_data)}")
            
        except Exception as e:
            logger.error(f"Error updating query analytics: {e}")
    
    async def get_user_activity_report(
        self,
        db: AsyncSession,
        user_id: UUID,
        days: int = 30
    ) -> Dict[str, Any]:
        """Get activity report for a specific user."""
        try:
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=days)
            
            # Get user's activity
            activity_query = select(
                DocumentAccess.access_type,
                func.count(DocumentAccess.id).label('count'),
                func.date(DocumentAccess.accessed_at).label('date')
            ).where(
                and_(
                    DocumentAccess.user_id == user_id,
                    DocumentAccess.accessed_at >= start_date,
                    DocumentAccess.accessed_at <= end_date
                )
            ).group_by(DocumentAccess.access_type, func.date(DocumentAccess.accessed_at))
            
            result = await db.execute(activity_query)
            
            # Process activity data
            activity_by_type = {}
            daily_activity = {}
            
            for row in result.fetchall():
                access_type, count, date = row
                
                if access_type not in activity_by_type:
                    activity_by_type[access_type] = 0
                activity_by_type[access_type] += count
                
                date_str = date.isoformat()
                if date_str not in daily_activity:
                    daily_activity[date_str] = {}
                daily_activity[date_str][access_type] = count
            
            # Get user's most accessed documents
            doc_query = select(
                Document.title,
                func.count(DocumentAccess.id).label('access_count')
            ).join(DocumentAccess).where(
                and_(
                    DocumentAccess.user_id == user_id,
                    DocumentAccess.accessed_at >= start_date
                )
            ).group_by(Document.id, Document.title).order_by(desc('access_count')).limit(10)
            
            doc_result = await db.execute(doc_query)
            top_documents = [
                {"title": row[0], "access_count": row[1]}
                for row in doc_result.fetchall()
            ]
            
            return {
                "user_id": str(user_id),
                "period_days": days,
                "activity_by_type": activity_by_type,
                "daily_activity": daily_activity,
                "top_documents": top_documents,
                "total_activities": sum(activity_by_type.values())
            }
            
        except Exception as e:
            logger.error(f"Error getting user activity report: {e}")
            return {}
    
    async def get_department_analytics(
        self,
        db: AsyncSession,
        department_id: UUID,
        days: int = 30
    ) -> Dict[str, Any]:
        """Get analytics for a specific department."""
        try:
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=days)
            
            # Get department users
            dept_users_query = select(User.id).join(User.departments).where(
                Department.id == department_id
            )
            dept_users_result = await db.execute(dept_users_query)
            dept_user_ids = [row[0] for row in dept_users_result.fetchall()]
            
            if not dept_user_ids:
                return {"error": "No users found in department"}
            
            # Get department activity
            activity_query = select(
                DocumentAccess.access_type,
                func.count(DocumentAccess.id).label('count'),
                func.count(func.distinct(DocumentAccess.user_id)).label('unique_users')
            ).where(
                and_(
                    DocumentAccess.user_id.in_(dept_user_ids),
                    DocumentAccess.accessed_at >= start_date,
                    DocumentAccess.accessed_at <= end_date
                )
            ).group_by(DocumentAccess.access_type)
            
            activity_result = await db.execute(activity_query)
            
            department_activity = {}
            total_activities = 0
            active_users = set()
            
            for row in activity_result.fetchall():
                access_type, count, unique_users = row
                department_activity[access_type] = {
                    "count": count,
                    "unique_users": unique_users
                }
                total_activities += count
            
            # Get department's most popular queries
            popular_queries_query = select(
                DocumentAccess.query_text,
                func.count(DocumentAccess.id).label('count')
            ).where(
                and_(
                    DocumentAccess.user_id.in_(dept_user_ids),
                    DocumentAccess.query_text.isnot(None),
                    DocumentAccess.accessed_at >= start_date
                )
            ).group_by(DocumentAccess.query_text).order_by(desc('count')).limit(10)
            
            queries_result = await db.execute(popular_queries_query)
            popular_queries = [
                {"query": row[0], "count": row[1]}
                for row in queries_result.fetchall()
            ]
            
            return {
                "department_id": str(department_id),
                "period_days": days,
                "total_activities": total_activities,
                "activity_breakdown": department_activity,
                "popular_queries": popular_queries,
                "total_users": len(dept_user_ids)
            }
            
        except Exception as e:
            logger.error(f"Error getting department analytics: {e}")
            return {}
    
    # Helper methods
    async def _get_query_count(
        self,
        db: AsyncSession,
        start_date: datetime,
        end_date: datetime,
        user_departments: List[UUID]
    ) -> int:
        """Get total query count for period."""
        query = select(func.count(DocumentAccess.id)).where(
            and_(
                DocumentAccess.access_type == "query",
                DocumentAccess.accessed_at >= start_date,
                DocumentAccess.accessed_at <= end_date
            )
        )
        
        result = await db.execute(query)
        return result.scalar() or 0
    
    async def _get_search_count(
        self,
        db: AsyncSession,
        start_date: datetime,
        end_date: datetime,
        user_departments: List[UUID]
    ) -> int:
        """Get total search count for period."""
        query = select(func.count(DocumentAccess.id)).where(
            and_(
                DocumentAccess.access_type == "search",
                DocumentAccess.accessed_at >= start_date,
                DocumentAccess.accessed_at <= end_date
            )
        )
        
        result = await db.execute(query)
        return result.scalar() or 0
    
    async def _get_active_users_count(
        self,
        db: AsyncSession,
        start_date: datetime,
        end_date: datetime,
        user_departments: List[UUID]
    ) -> int:
        """Get active users count for period."""
        query = select(func.count(func.distinct(DocumentAccess.user_id))).where(
            and_(
                DocumentAccess.accessed_at >= start_date,
                DocumentAccess.accessed_at <= end_date
            )
        )
        
        result = await db.execute(query)
        return result.scalar() or 0
    
    async def _get_document_count(self, db: AsyncSession, user_departments: List[UUID]) -> int:
        """Get total document count."""
        query = select(func.count(Document.id)).where(Document.status == "active")
        result = await db.execute(query)
        return result.scalar() or 0
    
    async def _get_processed_document_count(self, db: AsyncSession, user_departments: List[UUID]) -> int:
        """Get processed document count."""
        query = select(func.count(Document.id)).where(
            and_(
                Document.status == "active",
                Document.processing_status == "completed"
            )
        )
        result = await db.execute(query)
        return result.scalar() or 0
    
    async def _get_average_response_time(
        self,
        db: AsyncSession,
        start_date: datetime,
        end_date: datetime,
        user_departments: List[UUID]
    ) -> float:
        """Get average response time."""
        query = select(func.avg(DocumentAccess.response_time)).where(
            and_(
                DocumentAccess.response_time.isnot(None),
                DocumentAccess.accessed_at >= start_date,
                DocumentAccess.accessed_at <= end_date
            )
        )
        
        result = await db.execute(query)
        return float(result.scalar() or 0.0)
    
    async def _get_success_rate(
        self,
        db: AsyncSession,
        start_date: datetime,
        end_date: datetime,
        user_departments: List[UUID]
    ) -> float:
        """Calculate success rate based on response times and query results."""
        # This is a simplified calculation - in practice, you'd track actual success/failure
        total_query = select(func.count(DocumentAccess.id)).where(
            and_(
                DocumentAccess.access_type == "query",
                DocumentAccess.accessed_at >= start_date,
                DocumentAccess.accessed_at <= end_date
            )
        )
        
        total_result = await db.execute(total_query)
        total = total_result.scalar() or 0
        
        if total == 0:
            return 100.0
        
        # Assume queries with reasonable response times are successful
        successful_query = select(func.count(DocumentAccess.id)).where(
            and_(
                DocumentAccess.access_type == "query",
                DocumentAccess.accessed_at >= start_date,
                DocumentAccess.accessed_at <= end_date,
                DocumentAccess.response_time < 10.0  # Less than 10 seconds
            )
        )
        
        successful_result = await db.execute(successful_query)
        successful = successful_result.scalar() or 0
        
        return (successful / total) * 100.0
    
    async def _get_popular_documents(
        self,
        db: AsyncSession,
        start_date: datetime,
        end_date: datetime,
        user_departments: List[UUID],
        limit: int
    ) -> List[Dict[str, Any]]:
        """Get most popular documents."""
        query = select(
            Document.title,
            Document.category,
            func.count(DocumentAccess.id).label('access_count')
        ).join(DocumentAccess).where(
            DocumentAccess.accessed_at >= start_date
        ).group_by(Document.id, Document.title, Document.category).order_by(desc('access_count')).limit(limit)
        
        result = await db.execute(query)
        return [
            {
                "title": row[0],
                "category": row[1],
                "access_count": row[2]
            }
            for row in result.fetchall()
        ]
    
    async def _get_department_usage(
        self,
        db: AsyncSession,
        start_date: datetime,
        end_date: datetime,
        user_departments: List[UUID]
    ) -> List[Dict[str, Any]]:
        """Get usage by department."""
        # This would require joining with user departments
        # Simplified implementation for now
        return []
    
    async def _get_performance_metrics(
        self,
        db: AsyncSession,
        start_date: datetime,
        end_date: datetime,
        user_departments: List[UUID]
    ) -> Dict[str, Any]:
        """Get performance metrics."""
        avg_response_time = await self._get_average_response_time(db, start_date, end_date, user_departments)
        success_rate = await self._get_success_rate(db, start_date, end_date, user_departments)
        
        return {
            "average_response_time": avg_response_time,
            "success_rate": success_rate,
            "uptime": 99.9  # This would come from monitoring system
        }
    
    def _calculate_change_percent(self, current: int, previous: int) -> Optional[float]:
        """Calculate percentage change."""
        if previous == 0:
            return None if current == 0 else 100.0
        
        return ((current - previous) / previous) * 100.0
    
    def _get_trend(self, change_percent: Optional[float]) -> str:
        """Get trend direction."""
        if change_percent is None:
            return "stable"
        elif change_percent > 5:
            return "up"
        elif change_percent < -5:
            return "down"
        else:
            return "stable"
