"""
KnowledgeBot - Corporate Memory System
User Pydantic Schemas

This module defines Pydantic models for user-related API requests and responses
with comprehensive validation and serialization.
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from uuid import UUID
from pydantic import BaseModel, EmailStr, Field, validator

from app.schemas.base import BaseResponse


class UserBase(BaseModel):
    """Base user schema with common fields."""
    email: EmailStr
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    display_name: Optional[str] = None
    is_active: bool = True
    is_verified: bool = False


class UserCreate(UserBase):
    """Schema for creating a new user."""
    password: Optional[str] = Field(None, min_length=8, max_length=128)
    must_change_password: bool = False
    role_ids: Optional[List[UUID]] = None
    department_ids: Optional[List[UUID]] = None
    
    @validator('password')
    def validate_password(cls, v):
        if v is not None:
            if len(v) < 8:
                raise ValueError('Password must be at least 8 characters long')
            if not any(c.isupper() for c in v):
                raise ValueError('Password must contain at least one uppercase letter')
            if not any(c.islower() for c in v):
                raise ValueError('Password must contain at least one lowercase letter')
            if not any(c.isdigit() for c in v):
                raise ValueError('Password must contain at least one digit')
        return v


class UserUpdate(BaseModel):
    """Schema for updating user information."""
    email: Optional[EmailStr] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    display_name: Optional[str] = None
    is_active: Optional[bool] = None
    is_verified: Optional[bool] = None
    password: Optional[str] = Field(None, min_length=8, max_length=128)
    must_change_password: Optional[bool] = None
    
    @validator('password')
    def validate_password(cls, v):
        if v is not None:
            if len(v) < 8:
                raise ValueError('Password must be at least 8 characters long')
            if not any(c.isupper() for c in v):
                raise ValueError('Password must contain at least one uppercase letter')
            if not any(c.islower() for c in v):
                raise ValueError('Password must contain at least one lowercase letter')
            if not any(c.isdigit() for c in v):
                raise ValueError('Password must contain at least one digit')
        return v


class UserRoleUpdate(BaseModel):
    """Schema for updating user roles."""
    role_ids: List[UUID]


class UserDepartmentUpdate(BaseModel):
    """Schema for updating user departments."""
    department_ids: List[UUID]


class RoleResponse(BaseModel):
    """Schema for role information in responses."""
    id: UUID
    name: str
    display_name: str
    description: Optional[str] = None
    
    class Config:
        from_attributes = True


class DepartmentResponse(BaseModel):
    """Schema for department information in responses."""
    id: UUID
    name: str
    display_name: str
    description: Optional[str] = None
    is_active: bool
    
    class Config:
        from_attributes = True


class UserResponse(UserBase):
    """Schema for user information in responses."""
    id: UUID
    is_superuser: bool
    is_locked: bool
    must_change_password: bool
    failed_login_attempts: int
    last_login: Optional[datetime] = None
    last_password_change: Optional[datetime] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    roles: List[RoleResponse] = []
    departments: List[DepartmentResponse] = []
    sso_provider: Optional[str] = None
    
    class Config:
        from_attributes = True


class UserListResponse(BaseResponse):
    """Schema for paginated user list responses."""
    users: List[UserResponse]
    total: int
    skip: int
    limit: int
    has_more: bool


class UserProfileUpdate(BaseModel):
    """Schema for user profile updates."""
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    display_name: Optional[str] = None


class PasswordChangeRequest(BaseModel):
    """Schema for password change requests."""
    old_password: str
    new_password: str = Field(..., min_length=8, max_length=128)
    
    @validator('new_password')
    def validate_new_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not any(c.isupper() for c in v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not any(c.islower() for c in v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not any(c.isdigit() for c in v):
            raise ValueError('Password must contain at least one digit')
        return v


class PasswordResetRequest(BaseModel):
    """Schema for password reset requests."""
    email: EmailStr


class PasswordResetConfirm(BaseModel):
    """Schema for password reset confirmation."""
    token: str
    new_password: str = Field(..., min_length=8, max_length=128)
    
    @validator('new_password')
    def validate_new_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not any(c.isupper() for c in v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not any(c.islower() for c in v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not any(c.isdigit() for c in v):
            raise ValueError('Password must contain at least one digit')
        return v


class EmailVerificationRequest(BaseModel):
    """Schema for email verification requests."""
    token: str


class UserSessionResponse(BaseModel):
    """Schema for user session information."""
    id: UUID
    session_token: str
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    created_at: datetime
    expires_at: datetime
    last_activity: Optional[datetime] = None
    is_active: bool
    
    class Config:
        from_attributes = True


class UserSessionListResponse(BaseResponse):
    """Schema for user session list responses."""
    sessions: List[UserSessionResponse]
    total: int


class UserPreferences(BaseModel):
    """Schema for user preferences."""
    theme: str = Field("light", regex="^(light|dark|auto)$")
    language: str = Field("en", regex="^[a-z]{2}$")
    timezone: str = "UTC"
    notifications_enabled: bool = True
    email_notifications: bool = True
    
    class Config:
        extra = "allow"  # Allow additional preference fields


class UserPreferencesUpdate(BaseModel):
    """Schema for updating user preferences."""
    theme: Optional[str] = Field(None, regex="^(light|dark|auto)$")
    language: Optional[str] = Field(None, regex="^[a-z]{2}$")
    timezone: Optional[str] = None
    notifications_enabled: Optional[bool] = None
    email_notifications: Optional[bool] = None
    
    class Config:
        extra = "allow"  # Allow additional preference fields


class UserStatsResponse(BaseModel):
    """Schema for user statistics."""
    total_queries: int
    total_searches: int
    documents_uploaded: int
    documents_accessed: int
    last_activity: Optional[datetime] = None
    account_age_days: int
    
    class Config:
        from_attributes = True


class UserActivityResponse(BaseModel):
    """Schema for user activity information."""
    user_id: UUID
    period_days: int
    activity_by_type: Dict[str, int]
    daily_activity: Dict[str, Dict[str, int]]
    top_documents: List[Dict[str, Any]]
    total_activities: int
    generated_at: str


class APIKeyResponse(BaseModel):
    """Schema for API key information."""
    id: UUID
    name: str
    key_prefix: str
    created_at: datetime
    last_used: Optional[datetime] = None
    expires_at: Optional[datetime] = None
    is_active: bool
    
    class Config:
        from_attributes = True


class APIKeyCreate(BaseModel):
    """Schema for creating API keys."""
    name: str = Field(..., min_length=1, max_length=100)
    expires_days: Optional[int] = Field(None, ge=1, le=365)


class APIKeyListResponse(BaseResponse):
    """Schema for API key list responses."""
    api_keys: List[APIKeyResponse]
    total: int
