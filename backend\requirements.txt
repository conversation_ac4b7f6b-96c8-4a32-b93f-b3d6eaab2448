# KnowledgeBot - Corporate Memory System
# Backend Python Dependencies

# FastAPI and ASGI
fastapi==0.104.1
uvicorn[standard]==0.24.0
gunicorn==21.2.0

# Database
sqlalchemy==2.0.23
alembic==1.12.1
asyncpg==0.29.0
psycopg2-binary==2.9.9

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
cryptography==41.0.8

# HTTP Client
httpx==0.25.2
aiohttp==3.9.1

# Data Validation
pydantic==2.5.0
pydantic-settings==2.1.0
email-validator==2.1.0

# Redis & Caching
redis==5.0.1
aioredis==2.0.1

# Background Tasks
celery==5.3.4
flower==2.0.1

# File Processing
python-magic==0.4.27
PyPDF2==3.0.1
python-docx==1.1.0
python-pptx==0.6.23
openpyxl==3.1.2
beautifulsoup4==4.12.2
lxml==4.9.3
Pillow==10.1.0

# OCR and Text Extraction
pytesseract==0.3.10
pdf2image==1.16.3

# Vector Database Clients
pinecone-client==2.2.4
weaviate-client==3.25.3
qdrant-client==1.7.0

# Machine Learning & AI
numpy==1.25.2
scikit-learn==1.3.2
sentence-transformers==2.2.2
transformers==4.36.0
torch==2.1.1

# Search
elasticsearch==8.11.0

# Cloud Storage
boto3==1.34.0
minio==7.2.0

# Monitoring & Logging
prometheus-client==0.19.0
structlog==23.2.0
sentry-sdk[fastapi]==1.38.0

# Email
emails==0.6
jinja2==3.1.2

# Utilities
python-dateutil==2.8.2
pytz==2023.3
click==8.1.7
rich==13.7.0
typer==0.9.0

# Development Tools (included in base for simplicity)
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1
pre-commit==3.6.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.12.0
httpx==0.25.2
factory-boy==3.3.0

# SAML SSO
python3-saml==1.15.0

# Rate Limiting
slowapi==0.1.9

# Configuration
python-dotenv==1.0.0

# Validation & Serialization
marshmallow==3.20.1

# Time & Date
arrow==1.3.0

# Async utilities
asyncio-mqtt==0.16.1

# Health checks
psutil==5.9.6

# File type detection
filetype==1.2.0

# Text processing
nltk==3.8.1
spacy==3.7.2

# API documentation
swagger-ui-bundle==0.0.9

# WebSocket support
websockets==12.0

# Metrics and monitoring
statsd==4.0.1

# Job scheduling
apscheduler==3.10.4

# XML processing
xmltodict==0.13.0

# CSV processing
pandas==2.1.4

# Environment detection
python-decouple==3.8
