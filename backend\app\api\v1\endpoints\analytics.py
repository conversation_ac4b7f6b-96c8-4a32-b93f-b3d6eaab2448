"""
KnowledgeBot - Corporate Memory System
Analytics API Endpoints

This module provides analytics and reporting endpoints for usage tracking,
performance monitoring, and business intelligence.
"""

from typing import List, Dict, Any, Optional
from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import get_current_user, get_db, require_permissions
from app.models.user import User
from app.schemas.analytics import (
    DashboardMetricsResponse,
    UsageReportResponse,
    UserActivityResponse,
    DepartmentAnalyticsResponse,
    PopularQueriesResponse
)
from app.services.analytics_service import AnalyticsService

router = APIRouter()
analytics_service = AnalyticsService()


@router.get("/dashboard", response_model=DashboardMetricsResponse)
async def get_dashboard_metrics(
    days: int = Query(30, ge=1, le=365, description="Number of days to analyze"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    _: None = Depends(require_permissions(["analytics.read"]))
) -> Dict[str, Any]:
    """
    Get dashboard metrics for analytics overview.
    
    Args:
        days: Number of days to analyze
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Dict[str, Any]: Dashboard metrics
    """
    try:
        user_departments = [dept.id for dept in current_user.departments]
        metrics = await analytics_service.get_dashboard_metrics(db, user_departments, days)
        
        return {
            "metrics": [
                {
                    "name": metric.name,
                    "value": metric.value,
                    "change_percent": metric.change_percent,
                    "trend": metric.trend,
                    "metadata": metric.metadata
                }
                for metric in metrics
            ],
            "period_days": days,
            "generated_at": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get dashboard metrics: {str(e)}"
        )


@router.get("/usage-report", response_model=UsageReportResponse)
async def get_usage_report(
    period: str = Query("monthly", regex="^(daily|weekly|monthly)$", description="Report period"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    _: None = Depends(require_permissions(["analytics.read"]))
) -> Dict[str, Any]:
    """
    Get comprehensive usage report.
    
    Args:
        period: Report period (daily, weekly, monthly)
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Dict[str, Any]: Usage report
    """
    try:
        user_departments = [dept.id for dept in current_user.departments]
        report = await analytics_service.get_usage_report(db, user_departments, period)
        
        return {
            "period": report.period,
            "total_queries": report.total_queries,
            "total_searches": report.total_searches,
            "total_documents": report.total_documents,
            "active_users": report.active_users,
            "top_queries": report.top_queries,
            "top_documents": report.top_documents,
            "department_usage": report.department_usage,
            "performance_metrics": report.performance_metrics,
            "generated_at": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get usage report: {str(e)}"
        )


@router.get("/popular-queries", response_model=PopularQueriesResponse)
async def get_popular_queries(
    limit: int = Query(10, ge=1, le=50, description="Maximum number of queries to return"),
    days: int = Query(30, ge=1, le=365, description="Number of days to analyze"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    _: None = Depends(require_permissions(["analytics.read"]))
) -> Dict[str, Any]:
    """
    Get most popular queries with usage statistics.
    
    Args:
        limit: Maximum number of queries to return
        days: Number of days to analyze
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Dict[str, Any]: Popular queries with statistics
    """
    try:
        user_departments = [dept.id for dept in current_user.departments]
        queries = await analytics_service.get_popular_queries(db, limit, days, user_departments)
        
        return {
            "queries": queries,
            "period_days": days,
            "generated_at": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get popular queries: {str(e)}"
        )


@router.get("/user-activity/{user_id}", response_model=UserActivityResponse)
async def get_user_activity(
    user_id: UUID,
    days: int = Query(30, ge=1, le=365, description="Number of days to analyze"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    _: None = Depends(require_permissions(["analytics.users.read"]))
) -> Dict[str, Any]:
    """
    Get activity report for a specific user.
    
    Args:
        user_id: User UUID
        days: Number of days to analyze
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Dict[str, Any]: User activity report
    """
    try:
        # Check if current user can view this user's activity
        if not current_user.is_superuser and user_id != current_user.id:
            # Check if user is in same department
            user_departments = {dept.id for dept in current_user.departments}
            
            # Get target user's departments
            from sqlalchemy import select
            from sqlalchemy.orm import selectinload
            stmt = select(User).options(selectinload(User.departments)).where(User.id == user_id)
            result = await db.execute(stmt)
            target_user = result.scalar_one_or_none()
            
            if not target_user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="User not found"
                )
            
            target_departments = {dept.id for dept in target_user.departments}
            
            if not user_departments.intersection(target_departments):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Access denied to user activity"
                )
        
        activity_report = await analytics_service.get_user_activity_report(db, user_id, days)
        
        return {
            **activity_report,
            "generated_at": datetime.utcnow().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get user activity: {str(e)}"
        )


@router.get("/department/{department_id}", response_model=DepartmentAnalyticsResponse)
async def get_department_analytics(
    department_id: UUID,
    days: int = Query(30, ge=1, le=365, description="Number of days to analyze"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    _: None = Depends(require_permissions(["analytics.departments.read"]))
) -> Dict[str, Any]:
    """
    Get analytics for a specific department.
    
    Args:
        department_id: Department UUID
        days: Number of days to analyze
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Dict[str, Any]: Department analytics
    """
    try:
        # Check if current user has access to this department
        if not current_user.is_superuser:
            user_departments = {dept.id for dept in current_user.departments}
            if department_id not in user_departments:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Access denied to department analytics"
                )
        
        analytics = await analytics_service.get_department_analytics(db, department_id, days)
        
        return {
            **analytics,
            "generated_at": datetime.utcnow().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get department analytics: {str(e)}"
        )


@router.get("/trends/queries")
async def get_query_trends(
    days: int = Query(30, ge=7, le=365, description="Number of days to analyze"),
    interval: str = Query("daily", regex="^(hourly|daily|weekly)$", description="Trend interval"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    _: None = Depends(require_permissions(["analytics.read"]))
) -> Dict[str, Any]:
    """
    Get query volume trends over time.
    
    Args:
        days: Number of days to analyze
        interval: Trend interval (hourly, daily, weekly)
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Dict[str, Any]: Query trends data
    """
    try:
        from datetime import datetime, timedelta
        from sqlalchemy import select, func, text
        from app.models.document import DocumentAccess
        
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)
        
        # Determine date truncation based on interval
        if interval == "hourly":
            date_trunc = "hour"
        elif interval == "weekly":
            date_trunc = "week"
        else:  # daily
            date_trunc = "day"
        
        # Query for trends
        stmt = select(
            func.date_trunc(date_trunc, DocumentAccess.accessed_at).label('period'),
            func.count(DocumentAccess.id).label('count')
        ).where(
            and_(
                DocumentAccess.access_type == "query",
                DocumentAccess.accessed_at >= start_date,
                DocumentAccess.accessed_at <= end_date
            )
        ).group_by(
            func.date_trunc(date_trunc, DocumentAccess.accessed_at)
        ).order_by('period')
        
        result = await db.execute(stmt)
        trends = [
            {
                "period": row[0].isoformat(),
                "count": row[1]
            }
            for row in result.fetchall()
        ]
        
        return {
            "trends": trends,
            "interval": interval,
            "period_days": days,
            "generated_at": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get query trends: {str(e)}"
        )


@router.get("/performance/response-times")
async def get_response_time_analytics(
    days: int = Query(30, ge=1, le=365, description="Number of days to analyze"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    _: None = Depends(require_permissions(["analytics.read"]))
) -> Dict[str, Any]:
    """
    Get response time analytics.
    
    Args:
        days: Number of days to analyze
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Dict[str, Any]: Response time analytics
    """
    try:
        from datetime import datetime, timedelta
        from sqlalchemy import select, func
        from app.models.document import DocumentAccess
        
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)
        
        # Get response time statistics
        stmt = select(
            func.avg(DocumentAccess.response_time).label('avg_response_time'),
            func.min(DocumentAccess.response_time).label('min_response_time'),
            func.max(DocumentAccess.response_time).label('max_response_time'),
            func.percentile_cont(0.5).within_group(DocumentAccess.response_time).label('median_response_time'),
            func.percentile_cont(0.95).within_group(DocumentAccess.response_time).label('p95_response_time'),
            func.count(DocumentAccess.id).label('total_requests')
        ).where(
            and_(
                DocumentAccess.response_time.isnot(None),
                DocumentAccess.accessed_at >= start_date,
                DocumentAccess.accessed_at <= end_date
            )
        )
        
        result = await db.execute(stmt)
        stats = result.fetchone()
        
        if not stats or stats[5] == 0:  # total_requests
            return {
                "message": "No response time data available for the specified period",
                "period_days": days
            }
        
        return {
            "statistics": {
                "average_ms": round(float(stats[0]) * 1000, 2) if stats[0] else 0,
                "minimum_ms": round(float(stats[1]) * 1000, 2) if stats[1] else 0,
                "maximum_ms": round(float(stats[2]) * 1000, 2) if stats[2] else 0,
                "median_ms": round(float(stats[3]) * 1000, 2) if stats[3] else 0,
                "p95_ms": round(float(stats[4]) * 1000, 2) if stats[4] else 0,
                "total_requests": stats[5]
            },
            "period_days": days,
            "generated_at": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get response time analytics: {str(e)}"
        )


@router.get("/export/csv")
async def export_analytics_csv(
    report_type: str = Query("usage", regex="^(usage|queries|users|departments)$"),
    days: int = Query(30, ge=1, le=365, description="Number of days to analyze"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    _: None = Depends(require_permissions(["analytics.export"]))
):
    """
    Export analytics data as CSV.
    
    Args:
        report_type: Type of report to export
        days: Number of days to analyze
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        CSV file response
    """
    try:
        from fastapi.responses import StreamingResponse
        import csv
        import io
        
        # Generate CSV data based on report type
        if report_type == "queries":
            user_departments = [dept.id for dept in current_user.departments]
            queries = await analytics_service.get_popular_queries(db, 100, days, user_departments)
            
            output = io.StringIO()
            writer = csv.writer(output)
            writer.writerow(["Query", "Count", "Unique Users", "Avg Response Time"])
            
            for query in queries:
                writer.writerow([
                    query["query"],
                    query["count"],
                    query["unique_users"],
                    query["avg_response_time"]
                ])
            
            output.seek(0)
            
            return StreamingResponse(
                io.BytesIO(output.getvalue().encode()),
                media_type="text/csv",
                headers={"Content-Disposition": f"attachment; filename=queries_{days}days.csv"}
            )
        
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Export type '{report_type}' not implemented"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to export analytics: {str(e)}"
        )
