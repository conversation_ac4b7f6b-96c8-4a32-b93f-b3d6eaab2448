# KnowledgeBot Frontend Docker Compose Configuration
# For development and testing purposes

version: '3.8'

services:
  frontend:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - VITE_API_BASE_URL=${VITE_API_BASE_URL:-http://localhost:8000/api/v1}
        - VITE_APP_VERSION=${VITE_APP_VERSION:-1.0.0}
        - VITE_APP_ENVIRONMENT=${VITE_APP_ENVIRONMENT:-development}
    ports:
      - "3000:3000"
    environment:
      - VITE_API_BASE_URL=${VITE_API_BASE_URL:-http://localhost:8000/api/v1}
      - VITE_APP_VERSION=${VITE_APP_VERSION:-1.0.0}
      - VITE_APP_ENVIRONMENT=${VITE_APP_ENVIRONMENT:-development}
    volumes:
      # Mount source code for development (comment out for production)
      - .:/app
      - /app/node_modules
    networks:
      - knowledgebot-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Development server (alternative to built version)
  frontend-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "3001:3000"
    environment:
      - VITE_API_BASE_URL=${VITE_API_BASE_URL:-http://localhost:8000/api/v1}
      - VITE_APP_ENVIRONMENT=development
    volumes:
      - .:/app
      - /app/node_modules
    networks:
      - knowledgebot-network
    profiles:
      - dev
    command: npm run dev

networks:
  knowledgebot-network:
    external: true
