/**
 * KnowledgeBot - Corporate Memory System
 * Dashboard Page Component
 * 
 * Main dashboard with overview metrics, recent activity,
 * quick actions, and personalized content for users.
 */

import React from 'react'
import { Link } from 'react-router-dom'
import { Helmet } from 'react-helmet-async'
import {
  DocumentTextIcon,
  MagnifyingGlassIcon,
  ChartBarIcon,
  CloudArrowUpIcon,
  LightBulbIcon,
  UserGroupIcon,
} from '@heroicons/react/24/outline'

import { useAuth } from '@/hooks/useAuth'
import LoadingSpinner from '@/components/common/LoadingSpinner'

// Mock data - replace with real API calls
const mockStats = {
  totalDocuments: 1247,
  totalQueries: 3891,
  activeUsers: 156,
  avgResponseTime: 0.8,
}

const mockRecentActivity = [
  {
    id: 1,
    type: 'query',
    title: 'How to implement OAuth authentication?',
    user: '<PERSON>',
    timestamp: '2 minutes ago',
  },
  {
    id: 2,
    type: 'document',
    title: 'API Security Best Practices.pdf',
    user: '<PERSON>',
    timestamp: '15 minutes ago',
  },
  {
    id: 3,
    type: 'query',
    title: 'Database migration procedures',
    user: '<PERSON>',
    timestamp: '1 hour ago',
  },
]

const quickActions = [
  {
    name: 'Ask Knowledge',
    description: 'Get instant answers from your knowledge base',
    href: '/knowledge',
    icon: LightBulbIcon,
    color: 'bg-blue-500',
  },
  {
    name: 'Search Documents',
    description: 'Find specific documents and information',
    href: '/search',
    icon: MagnifyingGlassIcon,
    color: 'bg-green-500',
  },
  {
    name: 'Upload Document',
    description: 'Add new documents to the knowledge base',
    href: '/documents/upload',
    icon: CloudArrowUpIcon,
    color: 'bg-purple-500',
  },
  {
    name: 'View Analytics',
    description: 'Analyze usage patterns and insights',
    href: '/analytics',
    icon: ChartBarIcon,
    color: 'bg-orange-500',
  },
]

export default function DashboardPage() {
  const { user, hasPermission } = useAuth()

  if (!user) {
    return <LoadingSpinner size="lg" className="min-h-screen" />
  }

  return (
    <>
      <Helmet>
        <title>Dashboard - KnowledgeBot</title>
        <meta name="description" content="KnowledgeBot dashboard with overview and quick actions" />
      </Helmet>

      <div className="space-y-8">
        {/* Welcome Header */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            Welcome back, {user.first_name || user.display_name || 'User'}!
          </h1>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Here's what's happening with your knowledge base today.
          </p>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          <div className="card">
            <div className="card-body">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <DocumentTextIcon className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                      Total Documents
                    </dt>
                    <dd className="text-lg font-medium text-gray-900 dark:text-gray-100">
                      {mockStats.totalDocuments.toLocaleString()}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-body">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <LightBulbIcon className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                      Knowledge Queries
                    </dt>
                    <dd className="text-lg font-medium text-gray-900 dark:text-gray-100">
                      {mockStats.totalQueries.toLocaleString()}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-body">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <UserGroupIcon className="h-6 w-6 text-purple-600" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                      Active Users
                    </dt>
                    <dd className="text-lg font-medium text-gray-900 dark:text-gray-100">
                      {mockStats.activeUsers}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-body">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ChartBarIcon className="h-6 w-6 text-orange-600" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                      Avg Response Time
                    </dt>
                    <dd className="text-lg font-medium text-gray-900 dark:text-gray-100">
                      {mockStats.avgResponseTime}s
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div>
          <h2 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
            Quick Actions
          </h2>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
            {quickActions.map((action) => {
              // Check permissions for certain actions
              if (action.href === '/analytics' && !hasPermission('analytics.read')) {
                return null
              }
              if (action.href === '/documents/upload' && !hasPermission('documents.upload')) {
                return null
              }

              return (
                <Link
                  key={action.name}
                  to={action.href}
                  className="card hover:shadow-md transition-shadow duration-200 group"
                >
                  <div className="card-body">
                    <div className="flex items-center">
                      <div className={`flex-shrink-0 w-10 h-10 ${action.color} rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-200`}>
                        <action.icon className="h-5 w-5 text-white" />
                      </div>
                      <div className="ml-4">
                        <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {action.name}
                        </h3>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          {action.description}
                        </p>
                      </div>
                    </div>
                  </div>
                </Link>
              )
            })}
          </div>
        </div>

        {/* Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h2 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
              Recent Activity
            </h2>
            <div className="card">
              <div className="card-body">
                <div className="flow-root">
                  <ul className="-mb-8">
                    {mockRecentActivity.map((activity, activityIdx) => (
                      <li key={activity.id}>
                        <div className="relative pb-8">
                          {activityIdx !== mockRecentActivity.length - 1 ? (
                            <span
                              className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200 dark:bg-gray-700"
                              aria-hidden="true"
                            />
                          ) : null}
                          <div className="relative flex space-x-3">
                            <div>
                              <span className={`h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white dark:ring-gray-800 ${
                                activity.type === 'query' ? 'bg-blue-500' : 'bg-green-500'
                              }`}>
                                {activity.type === 'query' ? (
                                  <LightBulbIcon className="h-4 w-4 text-white" />
                                ) : (
                                  <DocumentTextIcon className="h-4 w-4 text-white" />
                                )}
                              </span>
                            </div>
                            <div className="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                              <div>
                                <p className="text-sm text-gray-900 dark:text-gray-100">
                                  {activity.title}
                                </p>
                                <p className="text-xs text-gray-500 dark:text-gray-400">
                                  by {activity.user}
                                </p>
                              </div>
                              <div className="text-right text-xs whitespace-nowrap text-gray-500 dark:text-gray-400">
                                {activity.timestamp}
                              </div>
                            </div>
                          </div>
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* System Status */}
          <div>
            <h2 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
              System Status
            </h2>
            <div className="card">
              <div className="card-body space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">API Status</span>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">
                    Operational
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Database</span>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">
                    Healthy
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Search Engine</span>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">
                    Online
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">AI Processing</span>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">
                    Ready
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
