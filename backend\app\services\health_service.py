"""
KnowledgeBot - Corporate Memory System
Health Service for System Monitoring

This module provides comprehensive health checks for all system components
including database, cache, storage, and external services.
"""

import asyncio
import logging
import time
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import psutil
import redis.asyncio as redis
from sqlalchemy import text

from app.core.database import db_manager
from app.core.config import settings
from app.services.storage_service import StorageService
from app.services.memvid_service import MemVidService

logger = logging.getLogger(__name__)


class HealthService:
    """
    Comprehensive health monitoring service for enterprise deployment.
    """
    
    def __init__(self):
        self.storage_service = StorageService()
        self.memvid_service = MemVidService()
        self._redis_client = None
        self._health_cache = {}
        self._cache_ttl = 30  # 30 seconds cache
    
    async def get_basic_health(self) -> Dict[str, Any]:
        """
        Get basic health status for quick checks.
        
        Returns:
            Dict[str, Any]: Basic health information
        """
        return {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "service": "knowledgebot-api",
            "version": settings.VERSION,
            "environment": settings.ENVIRONMENT
        }
    
    async def get_detailed_health(self) -> Dict[str, Any]:
        """
        Get comprehensive health status for all system components.
        
        Returns:
            Dict[str, Any]: Detailed health information
        """
        try:
            # Check cache first
            cache_key = "detailed_health"
            if cache_key in self._health_cache:
                cached_data, cached_time = self._health_cache[cache_key]
                if time.time() - cached_time < self._cache_ttl:
                    return cached_data
            
            start_time = time.time()
            
            # Run all health checks concurrently
            health_checks = await asyncio.gather(
                self._check_database_health(),
                self._check_redis_health(),
                self._check_storage_health(),
                self._check_memvid_health(),
                self._check_system_resources(),
                return_exceptions=True
            )
            
            # Process results
            database_health, redis_health, storage_health, memvid_health, system_health = health_checks
            
            # Determine overall status
            all_checks = [database_health, redis_health, storage_health, memvid_health, system_health]
            overall_status = "healthy"
            
            for check in all_checks:
                if isinstance(check, Exception):
                    overall_status = "unhealthy"
                    break
                elif check.get("status") != "healthy":
                    if check.get("status") == "degraded":
                        overall_status = "degraded" if overall_status == "healthy" else overall_status
                    else:
                        overall_status = "unhealthy"
                        break
            
            response_time = time.time() - start_time
            
            health_data = {
                "status": overall_status,
                "timestamp": datetime.utcnow().isoformat(),
                "response_time": round(response_time, 3),
                "service": "knowledgebot-api",
                "version": settings.VERSION,
                "environment": settings.ENVIRONMENT,
                "checks": {
                    "database": database_health if not isinstance(database_health, Exception) else {"status": "error", "error": str(database_health)},
                    "redis": redis_health if not isinstance(redis_health, Exception) else {"status": "error", "error": str(redis_health)},
                    "storage": storage_health if not isinstance(storage_health, Exception) else {"status": "error", "error": str(storage_health)},
                    "memvid": memvid_health if not isinstance(memvid_health, Exception) else {"status": "error", "error": str(memvid_health)},
                    "system": system_health if not isinstance(system_health, Exception) else {"status": "error", "error": str(system_health)}
                }
            }
            
            # Cache the result
            self._health_cache[cache_key] = (health_data, time.time())
            
            return health_data
            
        except Exception as e:
            logger.error(f"Error getting detailed health: {e}")
            return {
                "status": "error",
                "timestamp": datetime.utcnow().isoformat(),
                "error": str(e),
                "service": "knowledgebot-api"
            }
    
    async def _check_database_health(self) -> Dict[str, Any]:
        """Check PostgreSQL database health."""
        try:
            start_time = time.time()
            
            # Test basic connectivity
            is_healthy = await db_manager.health_check()
            
            if not is_healthy:
                return {
                    "status": "unhealthy",
                    "error": "Database connection failed"
                }
            
            # Test query performance
            async with db_manager.get_async_session() as session:
                await session.execute(text("SELECT 1"))
            
            response_time = time.time() - start_time
            
            # Check response time thresholds
            if response_time > 5.0:
                status = "unhealthy"
            elif response_time > 1.0:
                status = "degraded"
            else:
                status = "healthy"
            
            return {
                "status": status,
                "response_time": round(response_time, 3),
                "connection_pool": {
                    "size": settings.DATABASE_POOL_SIZE,
                    "max_overflow": settings.DATABASE_MAX_OVERFLOW
                }
            }
            
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return {
                "status": "unhealthy",
                "error": str(e)
            }
    
    async def _check_redis_health(self) -> Dict[str, Any]:
        """Check Redis cache health."""
        try:
            start_time = time.time()
            
            # Create Redis client if not exists
            if not self._redis_client:
                self._redis_client = redis.from_url(settings.REDIS_URL)
            
            # Test basic connectivity
            await self._redis_client.ping()
            
            # Test set/get operations
            test_key = "health_check_test"
            test_value = str(time.time())
            
            await self._redis_client.set(test_key, test_value, ex=60)
            retrieved_value = await self._redis_client.get(test_key)
            
            if retrieved_value.decode() != test_value:
                return {
                    "status": "unhealthy",
                    "error": "Redis set/get test failed"
                }
            
            # Clean up test key
            await self._redis_client.delete(test_key)
            
            response_time = time.time() - start_time
            
            # Check response time thresholds
            if response_time > 2.0:
                status = "unhealthy"
            elif response_time > 0.5:
                status = "degraded"
            else:
                status = "healthy"
            
            # Get Redis info
            info = await self._redis_client.info()
            
            return {
                "status": status,
                "response_time": round(response_time, 3),
                "info": {
                    "version": info.get("redis_version"),
                    "connected_clients": info.get("connected_clients"),
                    "used_memory": info.get("used_memory_human"),
                    "uptime": info.get("uptime_in_seconds")
                }
            }
            
        except Exception as e:
            logger.error(f"Redis health check failed: {e}")
            return {
                "status": "unhealthy",
                "error": str(e)
            }
    
    async def _check_storage_health(self) -> Dict[str, Any]:
        """Check file storage health."""
        try:
            start_time = time.time()
            
            # Test storage operations
            test_content = b"health_check_test_content"
            test_filename = "health_check_test.txt"
            
            # Test file storage
            file_key = await self.storage_service.store_file(
                test_content,
                test_filename,
                "text/plain",
                {"test": "health_check"}
            )
            
            # Test file retrieval
            retrieved_content = await self.storage_service.retrieve_file(file_key)
            
            if retrieved_content != test_content:
                return {
                    "status": "unhealthy",
                    "error": "Storage read/write test failed"
                }
            
            # Test file deletion
            await self.storage_service.delete_file(file_key)
            
            response_time = time.time() - start_time
            
            # Check response time thresholds
            if response_time > 10.0:
                status = "unhealthy"
            elif response_time > 3.0:
                status = "degraded"
            else:
                status = "healthy"
            
            storage_info = self.storage_service.get_storage_info()
            
            return {
                "status": status,
                "response_time": round(response_time, 3),
                "storage_type": storage_info["storage_type"],
                "encryption_enabled": storage_info["encryption_enabled"]
            }
            
        except Exception as e:
            logger.error(f"Storage health check failed: {e}")
            return {
                "status": "unhealthy",
                "error": str(e)
            }
    
    async def _check_memvid_health(self) -> Dict[str, Any]:
        """Check MemVid service health."""
        try:
            start_time = time.time()
            
            # Test embedding generation
            test_text = "This is a health check test for MemVid service."
            embedding = await self.memvid_service.generate_embedding(test_text)
            
            if not embedding or len(embedding) == 0:
                return {
                    "status": "unhealthy",
                    "error": "MemVid embedding generation failed"
                }
            
            response_time = time.time() - start_time
            
            # Check response time thresholds
            if response_time > 30.0:
                status = "unhealthy"
            elif response_time > 10.0:
                status = "degraded"
            else:
                status = "healthy"
            
            model_info = self.memvid_service.get_model_info()
            
            return {
                "status": status,
                "response_time": round(response_time, 3),
                "model_info": model_info
            }
            
        except Exception as e:
            logger.error(f"MemVid health check failed: {e}")
            return {
                "status": "unhealthy",
                "error": str(e)
            }
    
    async def _check_system_resources(self) -> Dict[str, Any]:
        """Check system resource usage."""
        try:
            # Get CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # Get memory usage
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # Get disk usage
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            
            # Determine status based on resource usage
            status = "healthy"
            warnings = []
            
            if cpu_percent > 90:
                status = "unhealthy"
                warnings.append("High CPU usage")
            elif cpu_percent > 70:
                status = "degraded" if status == "healthy" else status
                warnings.append("Elevated CPU usage")
            
            if memory_percent > 90:
                status = "unhealthy"
                warnings.append("High memory usage")
            elif memory_percent > 80:
                status = "degraded" if status == "healthy" else status
                warnings.append("Elevated memory usage")
            
            if disk_percent > 95:
                status = "unhealthy"
                warnings.append("Disk space critical")
            elif disk_percent > 85:
                status = "degraded" if status == "healthy" else status
                warnings.append("Low disk space")
            
            return {
                "status": status,
                "warnings": warnings,
                "resources": {
                    "cpu_percent": round(cpu_percent, 1),
                    "memory_percent": round(memory_percent, 1),
                    "memory_available": f"{memory.available // (1024**3)}GB",
                    "disk_percent": round(disk_percent, 1),
                    "disk_free": f"{disk.free // (1024**3)}GB"
                }
            }
            
        except Exception as e:
            logger.error(f"System resource check failed: {e}")
            return {
                "status": "error",
                "error": str(e)
            }
    
    async def get_metrics(self) -> Dict[str, Any]:
        """
        Get system metrics for monitoring.
        
        Returns:
            Dict[str, Any]: System metrics
        """
        try:
            # Get basic metrics
            cpu_percent = psutil.cpu_percent()
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # Get process info
            process = psutil.Process()
            process_memory = process.memory_info()
            
            return {
                "timestamp": datetime.utcnow().isoformat(),
                "system": {
                    "cpu_percent": cpu_percent,
                    "memory_percent": memory.percent,
                    "memory_used_gb": memory.used // (1024**3),
                    "memory_total_gb": memory.total // (1024**3),
                    "disk_percent": (disk.used / disk.total) * 100,
                    "disk_used_gb": disk.used // (1024**3),
                    "disk_total_gb": disk.total // (1024**3)
                },
                "process": {
                    "memory_rss_mb": process_memory.rss // (1024**2),
                    "memory_vms_mb": process_memory.vms // (1024**2),
                    "cpu_percent": process.cpu_percent(),
                    "num_threads": process.num_threads(),
                    "create_time": datetime.fromtimestamp(process.create_time()).isoformat()
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting metrics: {e}")
            return {"error": str(e)}
    
    async def cleanup(self):
        """Clean up resources."""
        try:
            if self._redis_client:
                await self._redis_client.close()
        except Exception as e:
            logger.error(f"Error cleaning up health service: {e}")
    
    def clear_cache(self):
        """Clear health check cache."""
        self._health_cache.clear()
