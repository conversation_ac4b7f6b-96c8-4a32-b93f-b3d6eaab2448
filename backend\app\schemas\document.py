"""
KnowledgeBot - Corporate Memory System
Document Pydantic Schemas

This module defines Pydantic models for document-related API requests and responses
with comprehensive validation and serialization.
"""

from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from uuid import UUID
from pydantic import BaseModel, Field, validator, HttpUrl

from app.schemas.base import BaseResponse
from app.schemas.user import UserResponse, DepartmentResponse


class DocumentBase(BaseModel):
    """Base document schema with common fields."""
    title: str = Field(..., min_length=1, max_length=500)
    description: Optional[str] = Field(None, max_length=2000)
    category: Optional[str] = Field(None, max_length=100)
    document_type: Optional[str] = Field(None, max_length=50)
    classification_level: str = Field("public", regex="^(public|internal|confidential|restricted)$")
    is_public: bool = False
    metadata: Optional[Dict[str, Any]] = None


class DocumentCreate(DocumentBase):
    """Schema for creating a new document."""
    department_ids: Optional[List[UUID]] = None
    tag_names: Optional[List[str]] = None
    
    @validator('tag_names')
    def validate_tag_names(cls, v):
        if v:
            # Remove duplicates and empty strings
            v = list(set(tag.strip() for tag in v if tag.strip()))
            # Limit number of tags
            if len(v) > 20:
                raise ValueError('Maximum 20 tags allowed')
            # Validate tag length
            for tag in v:
                if len(tag) > 50:
                    raise ValueError('Tag names must be 50 characters or less')
        return v


class DocumentUpdate(BaseModel):
    """Schema for updating document information."""
    title: Optional[str] = Field(None, min_length=1, max_length=500)
    description: Optional[str] = Field(None, max_length=2000)
    category: Optional[str] = Field(None, max_length=100)
    document_type: Optional[str] = Field(None, max_length=50)
    classification_level: Optional[str] = Field(None, regex="^(public|internal|confidential|restricted)$")
    is_public: Optional[bool] = None
    metadata: Optional[Dict[str, Any]] = None
    department_ids: Optional[List[UUID]] = None
    tag_names: Optional[List[str]] = None
    
    @validator('tag_names')
    def validate_tag_names(cls, v):
        if v:
            # Remove duplicates and empty strings
            v = list(set(tag.strip() for tag in v if tag.strip()))
            # Limit number of tags
            if len(v) > 20:
                raise ValueError('Maximum 20 tags allowed')
            # Validate tag length
            for tag in v:
                if len(tag) > 50:
                    raise ValueError('Tag names must be 50 characters or less')
        return v


class TagResponse(BaseModel):
    """Schema for tag information in responses."""
    id: UUID
    name: str
    display_name: str
    tag_type: str
    usage_count: int
    last_used: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class DocumentChunkResponse(BaseModel):
    """Schema for document chunk information."""
    id: UUID
    content: str
    chunk_index: int
    memvid_embedding: Optional[List[float]] = None
    similarity_score: Optional[float] = None
    
    class Config:
        from_attributes = True


class DocumentResponse(DocumentBase):
    """Schema for document information in responses."""
    id: UUID
    filename: str
    file_size: int
    mime_type: str
    file_hash: str
    storage_path: str
    processing_status: str
    processing_error: Optional[str] = None
    text_content: Optional[str] = None
    summary: Optional[str] = None
    key_points: Optional[List[str]] = None
    status: str
    created_at: datetime
    updated_at: Optional[datetime] = None
    uploader: Optional[UserResponse] = None
    departments: List[DepartmentResponse] = []
    tags: List[TagResponse] = []
    chunks: Optional[List[DocumentChunkResponse]] = None
    
    class Config:
        from_attributes = True


class DocumentListResponse(BaseResponse):
    """Schema for paginated document list responses."""
    documents: List[DocumentResponse]
    total: int
    skip: int
    limit: int
    has_more: bool
    facets: Optional[Dict[str, Any]] = None


class DocumentUploadResponse(BaseModel):
    """Schema for document upload responses."""
    document_id: UUID
    filename: str
    file_size: int
    mime_type: str
    processing_status: str
    message: str
    
    class Config:
        from_attributes = True


class DocumentSearchRequest(BaseModel):
    """Schema for document search requests."""
    query: str = Field(..., min_length=1, max_length=1000)
    filters: Optional[Dict[str, Any]] = None
    sort_by: str = Field("relevance", regex="^(relevance|date|title)$")
    skip: int = Field(0, ge=0)
    limit: int = Field(20, ge=1, le=100)


class DocumentSearchResult(BaseModel):
    """Schema for individual search results."""
    document_id: UUID
    title: str
    content_excerpt: str
    relevance_score: float
    document_type: str
    category: str
    created_at: datetime
    url: str
    metadata: Dict[str, Any]


class DocumentSearchResponse(BaseModel):
    """Schema for document search responses."""
    results: List[DocumentSearchResult]
    total: int
    search_time: float
    suggestions: List[str]
    facets: Dict[str, Any]
    query: str


class DocumentProcessingStatus(BaseModel):
    """Schema for document processing status."""
    document_id: UUID
    status: str
    progress: Optional[int] = Field(None, ge=0, le=100)
    message: Optional[str] = None
    error: Optional[str] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class DocumentSummaryRequest(BaseModel):
    """Schema for document summarization requests."""
    max_length: int = Field(300, ge=50, le=1000)
    include_key_points: bool = True


class DocumentSummaryResponse(BaseModel):
    """Schema for document summary responses."""
    document_id: UUID
    summary: str
    key_points: List[str]
    confidence_score: float
    generated_at: datetime


class DocumentAccessRequest(BaseModel):
    """Schema for document access requests."""
    access_type: str = Field(..., regex="^(view|download|search|query)$")
    query_text: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class DocumentAccessResponse(BaseModel):
    """Schema for document access responses."""
    access_id: UUID
    document_id: UUID
    access_type: str
    granted: bool
    reason: Optional[str] = None
    expires_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class DocumentStatsResponse(BaseModel):
    """Schema for document statistics."""
    total_documents: int
    processed_documents: int
    processing_documents: int
    failed_documents: int
    processing_rate: float
    documents_by_type: Dict[str, int]
    documents_by_category: Optional[Dict[str, int]] = None
    documents_by_department: Optional[Dict[str, int]] = None


class DocumentBulkOperation(BaseModel):
    """Schema for bulk document operations."""
    document_ids: List[UUID] = Field(..., min_items=1, max_items=100)
    operation: str = Field(..., regex="^(delete|archive|reprocess|update_tags|update_departments)$")
    parameters: Optional[Dict[str, Any]] = None


class DocumentBulkOperationResponse(BaseModel):
    """Schema for bulk operation responses."""
    operation: str
    total_documents: int
    successful: int
    failed: int
    errors: List[Dict[str, str]]
    operation_id: UUID


class DocumentVersionResponse(BaseModel):
    """Schema for document version information."""
    id: UUID
    document_id: UUID
    version_number: int
    filename: str
    file_size: int
    file_hash: str
    created_at: datetime
    created_by: Optional[UserResponse] = None
    is_current: bool
    
    class Config:
        from_attributes = True


class DocumentVersionListResponse(BaseResponse):
    """Schema for document version list responses."""
    versions: List[DocumentVersionResponse]
    total: int
    current_version: int


class DocumentShareRequest(BaseModel):
    """Schema for document sharing requests."""
    share_type: str = Field(..., regex="^(link|email|department)$")
    recipients: Optional[List[str]] = None
    expires_in_days: Optional[int] = Field(None, ge=1, le=365)
    permissions: List[str] = Field(default=["view"], regex="^(view|download|comment)$")
    message: Optional[str] = Field(None, max_length=500)


class DocumentShareResponse(BaseModel):
    """Schema for document sharing responses."""
    share_id: UUID
    document_id: UUID
    share_type: str
    share_url: Optional[HttpUrl] = None
    expires_at: Optional[datetime] = None
    permissions: List[str]
    created_at: datetime
    
    class Config:
        from_attributes = True


class DocumentCommentRequest(BaseModel):
    """Schema for document comment requests."""
    content: str = Field(..., min_length=1, max_length=2000)
    parent_comment_id: Optional[UUID] = None


class DocumentCommentResponse(BaseModel):
    """Schema for document comment responses."""
    id: UUID
    document_id: UUID
    content: str
    author: UserResponse
    parent_comment_id: Optional[UUID] = None
    replies: Optional[List['DocumentCommentResponse']] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


# Enable forward references for recursive models
DocumentCommentResponse.model_rebuild()
