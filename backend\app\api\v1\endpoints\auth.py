"""
KnowledgeBot - Corporate Memory System
Authentication API Endpoints

This module handles user authentication, including login, logout,
token refresh, and SSO integration.
"""

from datetime import datetime, timedelta
from typing import Any, Dict, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Request, Response
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.api.deps import get_current_user, get_db
from app.core.config import settings
from app.core.security import (
    create_access_token,
    create_refresh_token,
    verify_password,
    get_password_hash,
    verify_token,
    rate_limiter
)
from app.models.user import User, UserSession
from app.schemas.auth import (
    Token,
    TokenRefresh,
    UserLogin,
    UserRegister,
    PasswordReset,
    PasswordResetRequest,
    SSORResponse
)
from app.schemas.user import UserResponse
from app.services.auth_service import AuthService
from app.services.email_service import EmailService

router = APIRouter()
security = HTTPBearer()
auth_service = AuthService()
email_service = EmailService()


@router.post("/login", response_model=Token)
async def login(
    user_credentials: UserLogin,
    request: Request,
    response: Response,
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    User login with email/password authentication.
    
    Args:
        user_credentials: User login credentials
        request: HTTP request object
        response: HTTP response object
        db: Database session
        
    Returns:
        Dict[str, Any]: Access token and user information
        
    Raises:
        HTTPException: If authentication fails
    """
    # Rate limiting
    client_ip = request.client.host
    if not rate_limiter.is_allowed(
        f"login:{client_ip}",
        limit=settings.RATE_LIMIT_REQUESTS,
        window=settings.RATE_LIMIT_WINDOW
    ):
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Too many login attempts. Please try again later."
        )
    
    # Authenticate user
    user = await auth_service.authenticate_user(
        db, user_credentials.email, user_credentials.password
    )
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user account"
        )
    
    if user.is_locked:
        raise HTTPException(
            status_code=status.HTTP_423_LOCKED,
            detail="Account is temporarily locked due to failed login attempts"
        )
    
    # Create tokens
    access_token = create_access_token(
        subject=user.id,
        additional_claims={
            "email": user.email,
            "roles": [role.name for role in user.roles],
            "departments": user.get_department_names()
        }
    )
    refresh_token = create_refresh_token(subject=user.id)
    
    # Create user session
    session = UserSession(
        user_id=user.id,
        session_token=refresh_token,
        ip_address=client_ip,
        user_agent=request.headers.get("user-agent"),
        expires_at=datetime.utcnow() + timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)
    )
    db.add(session)
    
    # Update user login info
    user.last_login = datetime.utcnow()
    user.failed_login_attempts = 0
    user.locked_until = None
    
    await db.commit()
    
    # Set secure cookie for refresh token
    response.set_cookie(
        key="refresh_token",
        value=refresh_token,
        max_age=settings.REFRESH_TOKEN_EXPIRE_DAYS * 24 * 60 * 60,
        httponly=True,
        secure=settings.ENVIRONMENT == "production",
        samesite="lax"
    )
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        "refresh_token": refresh_token,
        "user": UserResponse.from_orm(user)
    }


@router.post("/logout")
async def logout(
    request: Request,
    response: Response,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, str]:
    """
    User logout - invalidate session and clear cookies.
    
    Args:
        request: HTTP request object
        response: HTTP response object
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Dict[str, str]: Logout confirmation message
    """
    # Get refresh token from cookie
    refresh_token = request.cookies.get("refresh_token")
    
    if refresh_token:
        # Invalidate session
        stmt = select(UserSession).where(
            UserSession.user_id == current_user.id,
            UserSession.session_token == refresh_token,
            UserSession.is_active == True
        )
        result = await db.execute(stmt)
        session = result.scalar_one_or_none()
        
        if session:
            session.is_active = False
            session.logout_reason = "manual"
            await db.commit()
    
    # Clear cookies
    response.delete_cookie(key="refresh_token")
    
    return {"message": "Successfully logged out"}


@router.post("/refresh", response_model=Token)
async def refresh_token(
    request: Request,
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    Refresh access token using refresh token.
    
    Args:
        request: HTTP request object
        db: Database session
        
    Returns:
        Dict[str, Any]: New access token
        
    Raises:
        HTTPException: If refresh token is invalid
    """
    refresh_token = request.cookies.get("refresh_token")
    
    if not refresh_token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Refresh token not found"
        )
    
    # Verify refresh token
    payload = verify_token(refresh_token)
    if not payload or payload.get("type") != "refresh":
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token"
        )
    
    user_id = payload.get("sub")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token payload"
        )
    
    # Verify session exists and is active
    stmt = select(UserSession).where(
        UserSession.session_token == refresh_token,
        UserSession.is_active == True,
        UserSession.expires_at > datetime.utcnow()
    )
    result = await db.execute(stmt)
    session = result.scalar_one_or_none()
    
    if not session:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Session expired or invalid"
        )
    
    # Get user
    user_stmt = select(User).where(User.id == user_id, User.is_active == True)
    user_result = await db.execute(user_stmt)
    user = user_result.scalar_one_or_none()
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found or inactive"
        )
    
    # Create new access token
    access_token = create_access_token(
        subject=user.id,
        additional_claims={
            "email": user.email,
            "roles": [role.name for role in user.roles],
            "departments": user.get_department_names()
        }
    )
    
    # Update session activity
    session.last_activity = datetime.utcnow()
    await db.commit()
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
    }


@router.post("/register", response_model=UserResponse)
async def register(
    user_data: UserRegister,
    db: AsyncSession = Depends(get_db)
) -> User:
    """
    User registration (if enabled).
    
    Args:
        user_data: User registration data
        db: Database session
        
    Returns:
        User: Created user object
        
    Raises:
        HTTPException: If registration fails or is disabled
    """
    if not settings.ALLOW_USER_REGISTRATION:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="User registration is disabled"
        )
    
    # Check if user already exists
    stmt = select(User).where(User.email == user_data.email)
    result = await db.execute(stmt)
    existing_user = result.scalar_one_or_none()
    
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User with this email already exists"
        )
    
    # Create new user
    user = User(
        email=user_data.email,
        hashed_password=get_password_hash(user_data.password),
        first_name=user_data.first_name,
        last_name=user_data.last_name,
        is_verified=False
    )
    
    db.add(user)
    await db.commit()
    await db.refresh(user)
    
    # Send verification email
    if settings.ENABLE_EMAIL_VERIFICATION:
        await email_service.send_verification_email(user.email, user.id)
    
    return user


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    Get current user information.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        User: Current user object
    """
    return current_user


@router.post("/sso/saml")
async def saml_sso_callback(
    request: Request,
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    SAML SSO callback endpoint.
    
    Args:
        request: HTTP request with SAML response
        db: Database session
        
    Returns:
        Dict[str, Any]: Authentication result
    """
    # This would integrate with a SAML library like python3-saml
    # Implementation depends on specific SAML provider
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="SAML SSO not yet implemented"
    )


@router.post("/password-reset-request")
async def request_password_reset(
    reset_request: PasswordResetRequest,
    db: AsyncSession = Depends(get_db)
) -> Dict[str, str]:
    """
    Request password reset email.
    
    Args:
        reset_request: Password reset request data
        db: Database session
        
    Returns:
        Dict[str, str]: Confirmation message
    """
    # Always return success to prevent email enumeration
    await email_service.send_password_reset_email(reset_request.email)
    
    return {"message": "If the email exists, a password reset link has been sent"}


@router.post("/password-reset")
async def reset_password(
    reset_data: PasswordReset,
    db: AsyncSession = Depends(get_db)
) -> Dict[str, str]:
    """
    Reset password using reset token.
    
    Args:
        reset_data: Password reset data
        db: Database session
        
    Returns:
        Dict[str, str]: Success message
        
    Raises:
        HTTPException: If reset token is invalid
    """
    email = verify_password_reset_token(reset_data.token)
    if not email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired reset token"
        )
    
    # Get user by email
    stmt = select(User).where(User.email == email, User.is_active == True)
    result = await db.execute(stmt)
    user = result.scalar_one_or_none()
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    # Update password
    user.hashed_password = get_password_hash(reset_data.new_password)
    user.last_password_change = datetime.utcnow()
    user.must_change_password = False
    
    await db.commit()
    
    return {"message": "Password successfully reset"}
