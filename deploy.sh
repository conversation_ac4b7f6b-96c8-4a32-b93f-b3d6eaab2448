#!/bin/bash

# KnowledgeBot - Corporate Memory System
# Production Deployment Script
# 
# This script handles the complete deployment process for KnowledgeBot
# including environment setup, SSL certificates, and service orchestration.

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ENV_FILE="${SCRIPT_DIR}/.env.prod"
COMPOSE_FILE="${SCRIPT_DIR}/docker-compose.prod.yml"
BACKUP_DIR="${SCRIPT_DIR}/backups"
LOG_FILE="${SCRIPT_DIR}/deploy.log"

# Functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}" | tee -a "$LOG_FILE"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}" | tee -a "$LOG_FILE"
    exit 1
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}" | tee -a "$LOG_FILE"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if running as root or with sudo
    if [[ $EUID -eq 0 ]]; then
        warn "Running as root. Consider using a non-root user with sudo privileges."
    fi
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed. Please install Docker first."
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed. Please install Docker Compose first."
    fi
    
    # Check available disk space (minimum 10GB)
    available_space=$(df / | awk 'NR==2 {print $4}')
    if [[ $available_space -lt 10485760 ]]; then
        warn "Less than 10GB disk space available. Consider freeing up space."
    fi
    
    # Check available memory (minimum 4GB)
    available_memory=$(free -m | awk 'NR==2{printf "%.0f", $7}')
    if [[ $available_memory -lt 4096 ]]; then
        warn "Less than 4GB memory available. Performance may be affected."
    fi
    
    log "Prerequisites check completed."
}

# Setup environment
setup_environment() {
    log "Setting up environment..."
    
    # Create directories
    mkdir -p "$BACKUP_DIR"
    mkdir -p "${SCRIPT_DIR}/nginx/ssl"
    mkdir -p "${SCRIPT_DIR}/nginx/logs"
    mkdir -p "${SCRIPT_DIR}/postgres"
    mkdir -p "${SCRIPT_DIR}/redis"
    mkdir -p "${SCRIPT_DIR}/minio/certs"
    
    # Check if .env.prod exists
    if [[ ! -f "$ENV_FILE" ]]; then
        warn ".env.prod file not found. Creating from template..."
        
        # Create .env.prod template
        cat > "$ENV_FILE" << EOF
# KnowledgeBot Production Environment Configuration
# Generated on $(date)

# Domain Configuration
DOMAIN=your-domain.com
APP_VERSION=1.0.0

# Database Configuration
POSTGRES_PASSWORD=$(openssl rand -base64 32)

# Redis Configuration  
REDIS_PASSWORD=$(openssl rand -base64 32)

# MinIO Configuration
MINIO_ROOT_USER=admin
MINIO_ROOT_PASSWORD=$(openssl rand -base64 32)

# JWT Configuration
JWT_SECRET_KEY=$(openssl rand -base64 64)

# MemVid Configuration (cpu or cuda)
MEMVID_DEVICE=cpu

# SSL Configuration
SSL_EMAIL=<EMAIL>
EOF
        
        error "Please edit $ENV_FILE with your configuration and run the script again."
    fi
    
    # Source environment variables
    source "$ENV_FILE"
    
    # Validate required variables
    required_vars=("DOMAIN" "POSTGRES_PASSWORD" "REDIS_PASSWORD" "MINIO_ROOT_PASSWORD" "JWT_SECRET_KEY")
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var}" ]]; then
            error "Required environment variable $var is not set in $ENV_FILE"
        fi
    done
    
    log "Environment setup completed."
}

# Setup SSL certificates
setup_ssl() {
    log "Setting up SSL certificates..."
    
    if [[ "$DOMAIN" == "your-domain.com" ]]; then
        warn "Domain not configured. Skipping SSL setup."
        return
    fi
    
    # Check if certificates already exist
    if [[ -f "${SCRIPT_DIR}/nginx/ssl/${DOMAIN}.crt" ]]; then
        log "SSL certificates already exist for $DOMAIN"
        return
    fi
    
    # Install certbot if not present
    if ! command -v certbot &> /dev/null; then
        info "Installing certbot..."
        if command -v apt-get &> /dev/null; then
            sudo apt-get update
            sudo apt-get install -y certbot
        elif command -v yum &> /dev/null; then
            sudo yum install -y certbot
        else
            warn "Could not install certbot automatically. Please install manually."
            return
        fi
    fi
    
    # Generate SSL certificate
    info "Generating SSL certificate for $DOMAIN..."
    sudo certbot certonly --standalone \
        --email "$SSL_EMAIL" \
        --agree-tos \
        --no-eff-email \
        -d "$DOMAIN" \
        --non-interactive
    
    # Copy certificates to nginx directory
    sudo cp "/etc/letsencrypt/live/$DOMAIN/fullchain.pem" "${SCRIPT_DIR}/nginx/ssl/${DOMAIN}.crt"
    sudo cp "/etc/letsencrypt/live/$DOMAIN/privkey.pem" "${SCRIPT_DIR}/nginx/ssl/${DOMAIN}.key"
    sudo chown $(whoami):$(whoami) "${SCRIPT_DIR}/nginx/ssl/${DOMAIN}."*
    
    log "SSL certificates setup completed."
}

# Build and deploy services
deploy_services() {
    log "Building and deploying services..."
    
    # Pull latest images
    info "Pulling latest base images..."
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" pull
    
    # Build custom images
    info "Building application images..."
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" build --no-cache
    
    # Start services
    info "Starting services..."
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d
    
    # Wait for services to be healthy
    info "Waiting for services to be healthy..."
    max_attempts=30
    attempt=0
    
    while [[ $attempt -lt $max_attempts ]]; do
        if docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" ps | grep -q "unhealthy"; then
            info "Waiting for services to become healthy... (attempt $((attempt + 1))/$max_attempts)"
            sleep 10
            ((attempt++))
        else
            break
        fi
    done
    
    if [[ $attempt -eq $max_attempts ]]; then
        error "Services failed to become healthy within expected time."
    fi
    
    log "Services deployed successfully."
}

# Initialize database
initialize_database() {
    log "Initializing database..."
    
    # Wait for database to be ready
    info "Waiting for database to be ready..."
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" exec -T postgres \
        bash -c 'until pg_isready -U knowledgebot -d knowledgebot; do sleep 1; done'
    
    # Run database migrations
    info "Running database migrations..."
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" exec -T backend \
        alembic upgrade head
    
    # Create initial admin user
    info "Creating initial admin user..."
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" exec -T backend \
        python -c "
from app.core.database import get_db
from app.services.user_service import create_initial_admin
import asyncio

async def main():
    async for db in get_db():
        await create_initial_admin(db)
        break

asyncio.run(main())
"
    
    log "Database initialization completed."
}

# Create backup
create_backup() {
    log "Creating backup..."
    
    timestamp=$(date +%Y%m%d_%H%M%S)
    backup_file="${BACKUP_DIR}/knowledgebot_backup_${timestamp}.sql"
    
    # Backup database
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" exec -T postgres \
        pg_dump -U knowledgebot knowledgebot > "$backup_file"
    
    # Compress backup
    gzip "$backup_file"
    
    log "Backup created: ${backup_file}.gz"
}

# Health check
health_check() {
    log "Performing health check..."
    
    # Check backend health
    if curl -f "http://localhost:8000/api/v1/health" &> /dev/null; then
        log "Backend health check: PASSED"
    else
        error "Backend health check: FAILED"
    fi
    
    # Check frontend health
    if curl -f "http://localhost:3000/health" &> /dev/null; then
        log "Frontend health check: PASSED"
    else
        error "Frontend health check: FAILED"
    fi
    
    # Check database connection
    if docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" exec -T postgres \
        pg_isready -U knowledgebot -d knowledgebot &> /dev/null; then
        log "Database health check: PASSED"
    else
        error "Database health check: FAILED"
    fi
    
    log "All health checks passed."
}

# Show deployment info
show_deployment_info() {
    log "Deployment completed successfully!"
    
    echo ""
    echo "=== KnowledgeBot Deployment Information ==="
    echo ""
    echo "Frontend URL: https://$DOMAIN"
    echo "Backend API: https://$DOMAIN/api/v1"
    echo "API Documentation: https://$DOMAIN/docs"
    echo ""
    echo "Default Admin Credentials:"
    echo "Email: <EMAIL>"
    echo "Password: Admin123!"
    echo ""
    echo "Services Status:"
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" ps
    echo ""
    echo "To view logs: docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE logs -f"
    echo "To stop services: docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE down"
    echo ""
}

# Main deployment function
main() {
    log "Starting KnowledgeBot deployment..."
    
    check_prerequisites
    setup_environment
    setup_ssl
    create_backup
    deploy_services
    initialize_database
    health_check
    show_deployment_info
    
    log "Deployment completed successfully!"
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "backup")
        setup_environment
        create_backup
        ;;
    "health")
        setup_environment
        health_check
        ;;
    "logs")
        setup_environment
        docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" logs -f "${2:-}"
        ;;
    "stop")
        setup_environment
        docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" down
        ;;
    "restart")
        setup_environment
        docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" restart "${2:-}"
        ;;
    *)
        echo "Usage: $0 {deploy|backup|health|logs|stop|restart}"
        echo ""
        echo "Commands:"
        echo "  deploy  - Full deployment (default)"
        echo "  backup  - Create database backup"
        echo "  health  - Run health checks"
        echo "  logs    - View service logs"
        echo "  stop    - Stop all services"
        echo "  restart - Restart services"
        exit 1
        ;;
esac
