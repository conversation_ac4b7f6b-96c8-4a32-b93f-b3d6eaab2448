"""Seed initial data - Create default roles, permissions, and admin user

Revision ID: 002_seed_initial_data
Revises: 001_initial_migration
Create Date: 2024-01-01 00:01:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.sql import table, column
from sqlalchemy.dialects import postgresql
import uuid
from datetime import datetime

# revision identifiers, used by Alembic.
revision = '002_seed_initial_data'
down_revision = '001_initial_migration'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Seed initial data."""
    
    # Define table structures for data insertion
    permissions_table = table('permissions',
        column('id', postgresql.UUID),
        column('name', sa.String),
        column('display_name', sa.String),
        column('description', sa.String),
        column('category', sa.String),
        column('is_system_permission', sa.Bo<PERSON>an),
        column('created_at', sa.DateTime)
    )
    
    roles_table = table('roles',
        column('id', postgresql.UUID),
        column('name', sa.String),
        column('display_name', sa.String),
        column('description', sa.String),
        column('is_system_role', sa.Bo<PERSON>an),
        column('created_at', sa.DateTime)
    )
    
    role_permissions_table = table('role_permissions',
        column('role_id', postgresql.UUID),
        column('permission_id', postgresql.UUID)
    )
    
    departments_table = table('departments',
        column('id', postgresql.UUID),
        column('name', sa.String),
        column('display_name', sa.String),
        column('description', sa.String),
        column('is_active', sa.Boolean),
        column('created_at', sa.DateTime)
    )
    
    users_table = table('users',
        column('id', postgresql.UUID),
        column('email', sa.String),
        column('hashed_password', sa.String),
        column('first_name', sa.String),
        column('last_name', sa.String),
        column('display_name', sa.String),
        column('is_active', sa.Boolean),
        column('is_superuser', sa.Boolean),
        column('is_verified', sa.Boolean),
        column('created_at', sa.DateTime)
    )
    
    user_roles_table = table('user_roles',
        column('user_id', postgresql.UUID),
        column('role_id', postgresql.UUID)
    )
    
    user_departments_table = table('user_departments',
        column('user_id', postgresql.UUID),
        column('department_id', postgresql.UUID)
    )
    
    # Generate UUIDs for entities
    now = datetime.utcnow()
    
    # Permission IDs
    perm_ids = {
        'users.read': uuid.uuid4(),
        'users.create': uuid.uuid4(),
        'users.update': uuid.uuid4(),
        'users.delete': uuid.uuid4(),
        'users.roles.update': uuid.uuid4(),
        'users.departments.update': uuid.uuid4(),
        'users.unlock': uuid.uuid4(),
        'users.password.reset': uuid.uuid4(),
        'documents.read': uuid.uuid4(),
        'documents.create': uuid.uuid4(),
        'documents.update': uuid.uuid4(),
        'documents.delete': uuid.uuid4(),
        'documents.upload': uuid.uuid4(),
        'documents.download': uuid.uuid4(),
        'knowledge.query': uuid.uuid4(),
        'knowledge.search': uuid.uuid4(),
        'analytics.read': uuid.uuid4(),
        'analytics.users.read': uuid.uuid4(),
        'analytics.departments.read': uuid.uuid4(),
        'analytics.export': uuid.uuid4(),
        'admin.roles.read': uuid.uuid4(),
        'admin.roles.create': uuid.uuid4(),
        'admin.roles.update': uuid.uuid4(),
        'admin.roles.delete': uuid.uuid4(),
        'admin.permissions.read': uuid.uuid4(),
        'admin.departments.read': uuid.uuid4(),
        'admin.departments.create': uuid.uuid4(),
        'admin.departments.update': uuid.uuid4(),
        'admin.departments.delete': uuid.uuid4(),
        'admin.stats.read': uuid.uuid4(),
        'admin.maintenance': uuid.uuid4(),
        'system.health.read': uuid.uuid4(),
        'system.metrics.read': uuid.uuid4(),
        'system.admin': uuid.uuid4(),
    }
    
    # Role IDs
    role_ids = {
        'superuser': uuid.uuid4(),
        'admin': uuid.uuid4(),
        'manager': uuid.uuid4(),
        'user': uuid.uuid4(),
        'guest': uuid.uuid4(),
    }
    
    # Department IDs
    dept_ids = {
        'general': uuid.uuid4(),
        'it': uuid.uuid4(),
        'hr': uuid.uuid4(),
        'finance': uuid.uuid4(),
        'marketing': uuid.uuid4(),
    }
    
    # Admin user ID
    admin_user_id = uuid.uuid4()
    
    # Insert permissions
    permissions_data = [
        # User management permissions
        (perm_ids['users.read'], 'users.read', 'Read Users', 'View user information', 'users', True),
        (perm_ids['users.create'], 'users.create', 'Create Users', 'Create new users', 'users', True),
        (perm_ids['users.update'], 'users.update', 'Update Users', 'Update user information', 'users', True),
        (perm_ids['users.delete'], 'users.delete', 'Delete Users', 'Delete users', 'users', True),
        (perm_ids['users.roles.update'], 'users.roles.update', 'Update User Roles', 'Assign roles to users', 'users', True),
        (perm_ids['users.departments.update'], 'users.departments.update', 'Update User Departments', 'Assign departments to users', 'users', True),
        (perm_ids['users.unlock'], 'users.unlock', 'Unlock Users', 'Unlock locked user accounts', 'users', True),
        (perm_ids['users.password.reset'], 'users.password.reset', 'Reset User Passwords', 'Reset user passwords', 'users', True),
        
        # Document management permissions
        (perm_ids['documents.read'], 'documents.read', 'Read Documents', 'View documents', 'documents', True),
        (perm_ids['documents.create'], 'documents.create', 'Create Documents', 'Upload new documents', 'documents', True),
        (perm_ids['documents.update'], 'documents.update', 'Update Documents', 'Update document information', 'documents', True),
        (perm_ids['documents.delete'], 'documents.delete', 'Delete Documents', 'Delete documents', 'documents', True),
        (perm_ids['documents.upload'], 'documents.upload', 'Upload Documents', 'Upload document files', 'documents', True),
        (perm_ids['documents.download'], 'documents.download', 'Download Documents', 'Download document files', 'documents', True),
        
        # Knowledge management permissions
        (perm_ids['knowledge.query'], 'knowledge.query', 'Knowledge Queries', 'Ask knowledge questions', 'knowledge', True),
        (perm_ids['knowledge.search'], 'knowledge.search', 'Knowledge Search', 'Search knowledge base', 'knowledge', True),
        
        # Analytics permissions
        (perm_ids['analytics.read'], 'analytics.read', 'Read Analytics', 'View analytics and reports', 'analytics', True),
        (perm_ids['analytics.users.read'], 'analytics.users.read', 'Read User Analytics', 'View user activity analytics', 'analytics', True),
        (perm_ids['analytics.departments.read'], 'analytics.departments.read', 'Read Department Analytics', 'View department analytics', 'analytics', True),
        (perm_ids['analytics.export'], 'analytics.export', 'Export Analytics', 'Export analytics data', 'analytics', True),
        
        # Admin permissions
        (perm_ids['admin.roles.read'], 'admin.roles.read', 'Read Roles', 'View roles', 'admin', True),
        (perm_ids['admin.roles.create'], 'admin.roles.create', 'Create Roles', 'Create new roles', 'admin', True),
        (perm_ids['admin.roles.update'], 'admin.roles.update', 'Update Roles', 'Update roles', 'admin', True),
        (perm_ids['admin.roles.delete'], 'admin.roles.delete', 'Delete Roles', 'Delete roles', 'admin', True),
        (perm_ids['admin.permissions.read'], 'admin.permissions.read', 'Read Permissions', 'View permissions', 'admin', True),
        (perm_ids['admin.departments.read'], 'admin.departments.read', 'Read Departments', 'View departments', 'admin', True),
        (perm_ids['admin.departments.create'], 'admin.departments.create', 'Create Departments', 'Create new departments', 'admin', True),
        (perm_ids['admin.departments.update'], 'admin.departments.update', 'Update Departments', 'Update departments', 'admin', True),
        (perm_ids['admin.departments.delete'], 'admin.departments.delete', 'Delete Departments', 'Delete departments', 'admin', True),
        (perm_ids['admin.stats.read'], 'admin.stats.read', 'Read System Stats', 'View system statistics', 'admin', True),
        (perm_ids['admin.maintenance'], 'admin.maintenance', 'System Maintenance', 'Perform system maintenance', 'admin', True),
        
        # System permissions
        (perm_ids['system.health.read'], 'system.health.read', 'Read System Health', 'View system health status', 'system', True),
        (perm_ids['system.metrics.read'], 'system.metrics.read', 'Read System Metrics', 'View system metrics', 'system', True),
        (perm_ids['system.admin'], 'system.admin', 'System Administration', 'Full system administration', 'system', True),
    ]
    
    op.bulk_insert(permissions_table, [
        {
            'id': perm_id,
            'name': name,
            'display_name': display_name,
            'description': description,
            'category': category,
            'is_system_permission': is_system,
            'created_at': now
        }
        for perm_id, name, display_name, description, category, is_system in permissions_data
    ])
    
    # Insert roles
    roles_data = [
        (role_ids['superuser'], 'superuser', 'Superuser', 'Full system access', True),
        (role_ids['admin'], 'admin', 'Administrator', 'Administrative access', True),
        (role_ids['manager'], 'manager', 'Manager', 'Department management access', True),
        (role_ids['user'], 'user', 'User', 'Standard user access', True),
        (role_ids['guest'], 'guest', 'Guest', 'Limited read-only access', True),
    ]
    
    op.bulk_insert(roles_table, [
        {
            'id': role_id,
            'name': name,
            'display_name': display_name,
            'description': description,
            'is_system_role': is_system,
            'created_at': now
        }
        for role_id, name, display_name, description, is_system in roles_data
    ])
    
    # Assign permissions to roles
    role_permission_assignments = [
        # Superuser gets all permissions
        *[(role_ids['superuser'], perm_id) for perm_id in perm_ids.values()],
        
        # Admin gets most permissions except system admin
        (role_ids['admin'], perm_ids['users.read']),
        (role_ids['admin'], perm_ids['users.create']),
        (role_ids['admin'], perm_ids['users.update']),
        (role_ids['admin'], perm_ids['users.delete']),
        (role_ids['admin'], perm_ids['users.roles.update']),
        (role_ids['admin'], perm_ids['users.departments.update']),
        (role_ids['admin'], perm_ids['users.unlock']),
        (role_ids['admin'], perm_ids['users.password.reset']),
        (role_ids['admin'], perm_ids['documents.read']),
        (role_ids['admin'], perm_ids['documents.create']),
        (role_ids['admin'], perm_ids['documents.update']),
        (role_ids['admin'], perm_ids['documents.delete']),
        (role_ids['admin'], perm_ids['documents.upload']),
        (role_ids['admin'], perm_ids['documents.download']),
        (role_ids['admin'], perm_ids['knowledge.query']),
        (role_ids['admin'], perm_ids['knowledge.search']),
        (role_ids['admin'], perm_ids['analytics.read']),
        (role_ids['admin'], perm_ids['analytics.users.read']),
        (role_ids['admin'], perm_ids['analytics.departments.read']),
        (role_ids['admin'], perm_ids['analytics.export']),
        *[(role_ids['admin'], perm_id) for perm_name, perm_id in perm_ids.items() if perm_name.startswith('admin.')],
        (role_ids['admin'], perm_ids['system.health.read']),
        (role_ids['admin'], perm_ids['system.metrics.read']),
        
        # Manager gets user and document management for their department
        (role_ids['manager'], perm_ids['users.read']),
        (role_ids['manager'], perm_ids['documents.read']),
        (role_ids['manager'], perm_ids['documents.create']),
        (role_ids['manager'], perm_ids['documents.update']),
        (role_ids['manager'], perm_ids['documents.upload']),
        (role_ids['manager'], perm_ids['documents.download']),
        (role_ids['manager'], perm_ids['knowledge.query']),
        (role_ids['manager'], perm_ids['knowledge.search']),
        (role_ids['manager'], perm_ids['analytics.read']),
        (role_ids['manager'], perm_ids['analytics.departments.read']),
        
        # User gets basic document and knowledge access
        (role_ids['user'], perm_ids['documents.read']),
        (role_ids['user'], perm_ids['documents.create']),
        (role_ids['user'], perm_ids['documents.upload']),
        (role_ids['user'], perm_ids['documents.download']),
        (role_ids['user'], perm_ids['knowledge.query']),
        (role_ids['user'], perm_ids['knowledge.search']),
        
        # Guest gets read-only access
        (role_ids['guest'], perm_ids['documents.read']),
        (role_ids['guest'], perm_ids['knowledge.query']),
        (role_ids['guest'], perm_ids['knowledge.search']),
    ]
    
    op.bulk_insert(role_permissions_table, [
        {'role_id': role_id, 'permission_id': perm_id}
        for role_id, perm_id in role_permission_assignments
    ])
    
    # Insert default departments
    departments_data = [
        (dept_ids['general'], 'general', 'General', 'General department for all users'),
        (dept_ids['it'], 'it', 'Information Technology', 'IT department'),
        (dept_ids['hr'], 'hr', 'Human Resources', 'HR department'),
        (dept_ids['finance'], 'finance', 'Finance', 'Finance department'),
        (dept_ids['marketing'], 'marketing', 'Marketing', 'Marketing department'),
    ]
    
    op.bulk_insert(departments_table, [
        {
            'id': dept_id,
            'name': name,
            'display_name': display_name,
            'description': description,
            'is_active': True,
            'created_at': now
        }
        for dept_id, name, display_name, description in departments_data
    ])
    
    # Create default admin user
    # Password: Admin123! (hashed with bcrypt)
    admin_password_hash = '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3L6W5wJlRK'
    
    op.bulk_insert(users_table, [
        {
            'id': admin_user_id,
            'email': '<EMAIL>',
            'hashed_password': admin_password_hash,
            'first_name': 'System',
            'last_name': 'Administrator',
            'display_name': 'System Administrator',
            'is_active': True,
            'is_superuser': True,
            'is_verified': True,
            'created_at': now
        }
    ])
    
    # Assign superuser role to admin
    op.bulk_insert(user_roles_table, [
        {'user_id': admin_user_id, 'role_id': role_ids['superuser']}
    ])
    
    # Assign admin to general department
    op.bulk_insert(user_departments_table, [
        {'user_id': admin_user_id, 'department_id': dept_ids['general']}
    ])


def downgrade() -> None:
    """Remove seeded data."""
    # Delete in reverse order due to foreign key constraints
    op.execute("DELETE FROM user_departments")
    op.execute("DELETE FROM user_roles")
    op.execute("DELETE FROM users")
    op.execute("DELETE FROM departments")
    op.execute("DELETE FROM role_permissions")
    op.execute("DELETE FROM roles")
    op.execute("DELETE FROM permissions")
