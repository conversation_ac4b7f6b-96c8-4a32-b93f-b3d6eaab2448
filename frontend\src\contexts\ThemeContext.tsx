/**
 * KnowledgeBot - Corporate Memory System
 * Theme Context
 * 
 * Provides theme management for light/dark mode and user preferences
 * with persistent storage and system preference detection.
 */

import React, { createContext, useContext, useReducer, useEffect } from 'react'
import { ThemeMode, ThemeConfig } from '@/types'

// Theme state interface
interface ThemeState {
  theme: ThemeMode
  primaryColor: string
  borderRadius: number
  compactMode: boolean
  systemPreference: 'light' | 'dark'
  effectiveTheme: 'light' | 'dark'
}

// Theme actions
type ThemeAction =
  | { type: 'SET_THEME'; payload: ThemeMode }
  | { type: 'SET_PRIMARY_COLOR'; payload: string }
  | { type: 'SET_BORDER_RADIUS'; payload: number }
  | { type: 'SET_COMPACT_MODE'; payload: boolean }
  | { type: 'SET_SYSTEM_PREFERENCE'; payload: 'light' | 'dark' }
  | { type: 'LOAD_PREFERENCES'; payload: Partial<ThemeState> }

// Default theme configuration
const defaultTheme: ThemeState = {
  theme: 'auto',
  primaryColor: '#3b82f6',
  borderRadius: 8,
  compactMode: false,
  systemPreference: 'light',
  effectiveTheme: 'light',
}

// Theme reducer
function themeReducer(state: ThemeState, action: ThemeAction): ThemeState {
  switch (action.type) {
    case 'SET_THEME':
      return {
        ...state,
        theme: action.payload,
        effectiveTheme: action.payload === 'auto' ? state.systemPreference : action.payload,
      }
    case 'SET_PRIMARY_COLOR':
      return {
        ...state,
        primaryColor: action.payload,
      }
    case 'SET_BORDER_RADIUS':
      return {
        ...state,
        borderRadius: action.payload,
      }
    case 'SET_COMPACT_MODE':
      return {
        ...state,
        compactMode: action.payload,
      }
    case 'SET_SYSTEM_PREFERENCE':
      return {
        ...state,
        systemPreference: action.payload,
        effectiveTheme: state.theme === 'auto' ? action.payload : state.effectiveTheme,
      }
    case 'LOAD_PREFERENCES':
      return {
        ...state,
        ...action.payload,
        effectiveTheme: action.payload.theme === 'auto' 
          ? (action.payload.systemPreference || state.systemPreference)
          : (action.payload.theme as 'light' | 'dark') || state.effectiveTheme,
      }
    default:
      return state
  }
}

// Theme context interface
interface ThemeContextType extends ThemeState {
  setTheme: (theme: ThemeMode) => void
  setPrimaryColor: (color: string) => void
  setBorderRadius: (radius: number) => void
  setCompactMode: (compact: boolean) => void
  toggleTheme: () => void
  resetTheme: () => void
  exportTheme: () => ThemeConfig
  importTheme: (config: ThemeConfig) => void
}

// Create context
const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

// Storage key for theme preferences
const THEME_STORAGE_KEY = 'knowledgebot-theme-preferences'

// Theme provider component
export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(themeReducer, defaultTheme)

  // Load theme preferences from localStorage on mount
  useEffect(() => {
    try {
      const stored = localStorage.getItem(THEME_STORAGE_KEY)
      if (stored) {
        const preferences = JSON.parse(stored)
        dispatch({ type: 'LOAD_PREFERENCES', payload: preferences })
      }
    } catch (error) {
      console.warn('Failed to load theme preferences:', error)
    }
  }, [])

  // Save theme preferences to localStorage when state changes
  useEffect(() => {
    try {
      const preferences = {
        theme: state.theme,
        primaryColor: state.primaryColor,
        borderRadius: state.borderRadius,
        compactMode: state.compactMode,
      }
      localStorage.setItem(THEME_STORAGE_KEY, JSON.stringify(preferences))
    } catch (error) {
      console.warn('Failed to save theme preferences:', error)
    }
  }, [state.theme, state.primaryColor, state.borderRadius, state.compactMode])

  // Listen for system theme changes
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    
    const handleChange = (e: MediaQueryListEvent) => {
      dispatch({ 
        type: 'SET_SYSTEM_PREFERENCE', 
        payload: e.matches ? 'dark' : 'light' 
      })
    }

    // Set initial system preference
    dispatch({ 
      type: 'SET_SYSTEM_PREFERENCE', 
      payload: mediaQuery.matches ? 'dark' : 'light' 
    })

    // Listen for changes
    mediaQuery.addEventListener('change', handleChange)
    
    return () => mediaQuery.removeEventListener('change', handleChange)
  }, [])

  // Apply CSS custom properties when theme changes
  useEffect(() => {
    const root = document.documentElement
    
    // Set CSS custom properties
    root.style.setProperty('--primary-color', state.primaryColor)
    root.style.setProperty('--border-radius', `${state.borderRadius}px`)
    
    // Apply theme class
    root.classList.remove('light', 'dark')
    root.classList.add(state.effectiveTheme)
    
    // Apply compact mode
    if (state.compactMode) {
      root.classList.add('compact')
    } else {
      root.classList.remove('compact')
    }
  }, [state.effectiveTheme, state.primaryColor, state.borderRadius, state.compactMode])

  // Theme management functions
  const setTheme = (theme: ThemeMode) => {
    dispatch({ type: 'SET_THEME', payload: theme })
  }

  const setPrimaryColor = (color: string) => {
    dispatch({ type: 'SET_PRIMARY_COLOR', payload: color })
  }

  const setBorderRadius = (radius: number) => {
    dispatch({ type: 'SET_BORDER_RADIUS', payload: radius })
  }

  const setCompactMode = (compact: boolean) => {
    dispatch({ type: 'SET_COMPACT_MODE', payload: compact })
  }

  const toggleTheme = () => {
    const newTheme = state.effectiveTheme === 'light' ? 'dark' : 'light'
    setTheme(newTheme)
  }

  const resetTheme = () => {
    dispatch({ type: 'LOAD_PREFERENCES', payload: defaultTheme })
  }

  const exportTheme = (): ThemeConfig => {
    return {
      theme: state.theme,
      primaryColor: state.primaryColor,
      borderRadius: state.borderRadius,
      compactMode: state.compactMode,
    }
  }

  const importTheme = (config: ThemeConfig) => {
    dispatch({ type: 'LOAD_PREFERENCES', payload: config })
  }

  // Context value
  const value: ThemeContextType = {
    ...state,
    setTheme,
    setPrimaryColor,
    setBorderRadius,
    setCompactMode,
    toggleTheme,
    resetTheme,
    exportTheme,
    importTheme,
  }

  return <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>
}

// Hook to use theme context
export function useTheme() {
  const context = useContext(ThemeContext)
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  return context
}

// Export context for testing
export { ThemeContext }
