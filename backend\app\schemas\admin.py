"""
KnowledgeBot - Corporate Memory System
Admin Pydantic Schemas

This module defines Pydantic models for administrative API requests and responses
including roles, permissions, departments, and system management.
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from uuid import UUID
from pydantic import BaseModel, Field, validator

from app.schemas.base import BaseResponse


class PermissionResponse(BaseModel):
    """Schema for permission information in responses."""
    id: UUID
    name: str
    display_name: str
    description: Optional[str] = None
    category: str
    is_system_permission: bool
    
    class Config:
        from_attributes = True


class RoleBase(BaseModel):
    """Base role schema with common fields."""
    name: str = Field(..., min_length=1, max_length=100)
    display_name: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=500)
    
    @validator('name')
    def validate_name(cls, v):
        # Role names should be lowercase with underscores
        if not v.replace('_', '').replace('-', '').isalnum():
            raise ValueError('Role name can only contain letters, numbers, underscores, and hyphens')
        return v.lower()


class RoleCreate(RoleBase):
    """Schema for creating a new role."""
    permission_ids: Optional[List[UUID]] = None


class RoleUpdate(BaseModel):
    """Schema for updating role information."""
    display_name: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=500)
    permission_ids: Optional[List[UUID]] = None


class RoleResponse(RoleBase):
    """Schema for role information in responses."""
    id: UUID
    is_system_role: bool
    created_at: datetime
    updated_at: Optional[datetime] = None
    permissions: List[PermissionResponse] = []
    user_count: Optional[int] = None
    
    class Config:
        from_attributes = True


class DepartmentBase(BaseModel):
    """Base department schema with common fields."""
    name: str = Field(..., min_length=1, max_length=100)
    display_name: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=500)
    is_active: bool = True
    
    @validator('name')
    def validate_name(cls, v):
        # Department names should be lowercase with underscores
        if not v.replace('_', '').replace('-', '').isalnum():
            raise ValueError('Department name can only contain letters, numbers, underscores, and hyphens')
        return v.lower()


class DepartmentCreate(DepartmentBase):
    """Schema for creating a new department."""
    parent_department_id: Optional[UUID] = None
    manager_user_ids: Optional[List[UUID]] = None


class DepartmentUpdate(BaseModel):
    """Schema for updating department information."""
    display_name: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=500)
    is_active: Optional[bool] = None
    parent_department_id: Optional[UUID] = None
    manager_user_ids: Optional[List[UUID]] = None


class DepartmentResponse(DepartmentBase):
    """Schema for department information in responses."""
    id: UUID
    parent_department_id: Optional[UUID] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    user_count: Optional[int] = None
    document_count: Optional[int] = None
    subdepartments: Optional[List['DepartmentResponse']] = None
    
    class Config:
        from_attributes = True


class SystemStatsResponse(BaseModel):
    """Schema for system statistics responses."""
    users: Dict[str, int]
    documents: Dict[str, Any]
    metrics: Dict[str, Any]
    generated_at: str


class UserManagementStats(BaseModel):
    """Schema for user management statistics."""
    total_users: int
    active_users: int
    inactive_users: int
    locked_users: int
    sso_users: int
    local_users: int
    users_by_role: Dict[str, int]
    users_by_department: Dict[str, int]
    recent_logins: int
    failed_logins: int


class DocumentManagementStats(BaseModel):
    """Schema for document management statistics."""
    total_documents: int
    processed_documents: int
    processing_documents: int
    failed_documents: int
    documents_by_type: Dict[str, int]
    documents_by_category: Dict[str, int]
    documents_by_department: Dict[str, int]
    storage_used_gb: float
    processing_queue_size: int


class SystemHealthResponse(BaseModel):
    """Schema for system health responses."""
    overall_status: str
    components: Dict[str, Dict[str, Any]]
    performance_metrics: Dict[str, float]
    resource_usage: Dict[str, Any]
    alerts: List[Dict[str, Any]]
    last_updated: datetime


class AuditLogRequest(BaseModel):
    """Schema for audit log requests."""
    user_id: Optional[UUID] = None
    action: Optional[str] = None
    resource_type: Optional[str] = None
    resource_id: Optional[UUID] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    limit: int = Field(50, ge=1, le=1000)
    skip: int = Field(0, ge=0)


class AuditLogResponse(BaseModel):
    """Schema for audit log responses."""
    id: UUID
    user_id: Optional[UUID] = None
    user_email: Optional[str] = None
    action: str
    resource_type: str
    resource_id: Optional[UUID] = None
    details: Dict[str, Any]
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    timestamp: datetime
    
    class Config:
        from_attributes = True


class AuditLogListResponse(BaseResponse):
    """Schema for audit log list responses."""
    logs: List[AuditLogResponse]
    total: int
    skip: int
    limit: int
    has_more: bool


class SystemConfigurationRequest(BaseModel):
    """Schema for system configuration requests."""
    category: Optional[str] = None
    include_sensitive: bool = False


class SystemConfigurationItem(BaseModel):
    """Schema for system configuration items."""
    key: str
    value: Any
    description: Optional[str] = None
    category: str
    data_type: str
    is_sensitive: bool
    is_readonly: bool
    validation_rules: Optional[Dict[str, Any]] = None
    updated_at: datetime
    updated_by: Optional[UUID] = None


class SystemConfigurationResponse(BaseResponse):
    """Schema for system configuration responses."""
    configurations: List[SystemConfigurationItem]
    categories: List[str]


class SystemConfigurationUpdate(BaseModel):
    """Schema for system configuration updates."""
    configurations: Dict[str, Any] = Field(..., min_items=1)
    
    @validator('configurations')
    def validate_configurations(cls, v):
        # Validate that keys are valid configuration keys
        for key in v.keys():
            if not key.replace('_', '').replace('.', '').isalnum():
                raise ValueError(f'Invalid configuration key: {key}')
        return v


class MaintenanceModeRequest(BaseModel):
    """Schema for maintenance mode requests."""
    enabled: bool
    message: Optional[str] = Field(None, max_length=500)
    scheduled_start: Optional[datetime] = None
    scheduled_end: Optional[datetime] = None
    allowed_ips: List[str] = []
    
    @validator('allowed_ips')
    def validate_ips(cls, v):
        import ipaddress
        for ip in v:
            try:
                ipaddress.ip_address(ip)
            except ValueError:
                raise ValueError(f'Invalid IP address: {ip}')
        return v


class MaintenanceModeResponse(BaseModel):
    """Schema for maintenance mode responses."""
    enabled: bool
    message: Optional[str] = None
    scheduled_start: Optional[datetime] = None
    scheduled_end: Optional[datetime] = None
    allowed_ips: List[str] = []
    updated_at: datetime
    updated_by: UUID


class BackupRequest(BaseModel):
    """Schema for backup requests."""
    backup_type: str = Field(..., regex="^(full|incremental|differential)$")
    include_documents: bool = True
    include_user_data: bool = True
    include_system_config: bool = True
    compression: bool = True
    encryption: bool = True


class BackupResponse(BaseModel):
    """Schema for backup responses."""
    backup_id: UUID
    backup_type: str
    status: str
    file_size: Optional[int] = None
    download_url: Optional[str] = None
    created_at: datetime
    completed_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None
    metadata: Dict[str, Any]


class RestoreRequest(BaseModel):
    """Schema for restore requests."""
    backup_id: UUID
    restore_type: str = Field(..., regex="^(full|selective)$")
    components: Optional[List[str]] = None
    overwrite_existing: bool = False
    
    @validator('components')
    def validate_components(cls, v, values):
        if values.get('restore_type') == 'selective' and not v:
            raise ValueError('Components must be specified for selective restore')
        return v


class RestoreResponse(BaseModel):
    """Schema for restore responses."""
    restore_id: UUID
    backup_id: UUID
    restore_type: str
    status: str
    progress: Optional[int] = Field(None, ge=0, le=100)
    started_at: datetime
    completed_at: Optional[datetime] = None
    error: Optional[str] = None
    metadata: Dict[str, Any]


class SystemAlertRequest(BaseModel):
    """Schema for system alert requests."""
    alert_type: str = Field(..., regex="^(info|warning|error|critical)$")
    title: str = Field(..., min_length=1, max_length=200)
    message: str = Field(..., min_length=1, max_length=1000)
    expires_at: Optional[datetime] = None
    target_users: Optional[List[UUID]] = None
    target_roles: Optional[List[UUID]] = None
    target_departments: Optional[List[UUID]] = None


class SystemAlertResponse(BaseModel):
    """Schema for system alert responses."""
    id: UUID
    alert_type: str
    title: str
    message: str
    is_active: bool
    created_at: datetime
    expires_at: Optional[datetime] = None
    created_by: UUID
    target_count: int
    acknowledged_count: int


class SystemMetricsRequest(BaseModel):
    """Schema for system metrics requests."""
    metric_types: Optional[List[str]] = None
    time_range: str = Field("1h", regex="^(5m|15m|1h|6h|24h|7d|30d)$")
    aggregation: str = Field("avg", regex="^(avg|min|max|sum|count)$")


class SystemMetricsResponse(BaseModel):
    """Schema for system metrics responses."""
    metrics: Dict[str, List[Dict[str, Any]]]
    time_range: str
    aggregation: str
    generated_at: datetime


# Enable forward references for recursive models
DepartmentResponse.model_rebuild()
