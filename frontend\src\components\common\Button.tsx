/**
 * KnowledgeBot - Corporate Memory System
 * Button Component
 * 
 * Reusable button component with multiple variants, sizes, and states.
 * Supports loading states, icons, and accessibility features.
 */

import React, { forwardRef } from 'react'
import { clsx } from 'clsx'
import { LoadingSpinner } from './LoadingSpinner'

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'ghost' | 'danger' | 'success' | 'warning'
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  loading?: boolean
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
  fullWidth?: boolean
  rounded?: boolean
}

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      children,
      className,
      variant = 'primary',
      size = 'md',
      loading = false,
      leftIcon,
      rightIcon,
      fullWidth = false,
      rounded = false,
      disabled,
      type = 'button',
      ...props
    },
    ref
  ) => {
    const baseClasses = [
      'inline-flex items-center justify-center font-medium transition-colors duration-200',
      'focus:outline-none focus:ring-2 focus:ring-offset-2',
      'disabled:opacity-50 disabled:cursor-not-allowed',
      'border border-transparent',
    ]

    const variantClasses = {
      primary: [
        'bg-blue-600 text-white hover:bg-blue-700',
        'focus:ring-blue-500',
        'dark:bg-blue-500 dark:hover:bg-blue-600',
      ],
      secondary: [
        'bg-gray-100 text-gray-900 hover:bg-gray-200',
        'focus:ring-gray-500',
        'dark:bg-gray-700 dark:text-gray-100 dark:hover:bg-gray-600',
      ],
      ghost: [
        'bg-transparent text-gray-700 hover:bg-gray-100',
        'focus:ring-gray-500',
        'dark:text-gray-300 dark:hover:bg-gray-800',
      ],
      danger: [
        'bg-red-600 text-white hover:bg-red-700',
        'focus:ring-red-500',
        'dark:bg-red-500 dark:hover:bg-red-600',
      ],
      success: [
        'bg-green-600 text-white hover:bg-green-700',
        'focus:ring-green-500',
        'dark:bg-green-500 dark:hover:bg-green-600',
      ],
      warning: [
        'bg-yellow-600 text-white hover:bg-yellow-700',
        'focus:ring-yellow-500',
        'dark:bg-yellow-500 dark:hover:bg-yellow-600',
      ],
    }

    const sizeClasses = {
      xs: 'px-2 py-1 text-xs',
      sm: 'px-3 py-1.5 text-sm',
      md: 'px-4 py-2 text-sm',
      lg: 'px-6 py-3 text-base',
      xl: 'px-8 py-4 text-lg',
    }

    const roundedClasses = {
      xs: rounded ? 'rounded-full' : 'rounded',
      sm: rounded ? 'rounded-full' : 'rounded-md',
      md: rounded ? 'rounded-full' : 'rounded-md',
      lg: rounded ? 'rounded-full' : 'rounded-lg',
      xl: rounded ? 'rounded-full' : 'rounded-lg',
    }

    const iconSizeClasses = {
      xs: 'w-3 h-3',
      sm: 'w-4 h-4',
      md: 'w-4 h-4',
      lg: 'w-5 h-5',
      xl: 'w-6 h-6',
    }

    const classes = clsx(
      baseClasses,
      variantClasses[variant],
      sizeClasses[size],
      roundedClasses[size],
      {
        'w-full': fullWidth,
        'cursor-not-allowed': loading,
      },
      className
    )

    const iconClasses = clsx(iconSizeClasses[size])

    return (
      <button
        ref={ref}
        type={type}
        className={classes}
        disabled={disabled || loading}
        {...props}
      >
        {loading && (
          <LoadingSpinner
            size={size === 'xs' || size === 'sm' ? 'sm' : 'md'}
            className={clsx('mr-2', {
              'text-white': variant === 'primary' || variant === 'danger' || variant === 'success' || variant === 'warning',
              'text-gray-600': variant === 'secondary' || variant === 'ghost',
            })}
          />
        )}
        
        {!loading && leftIcon && (
          <span className={clsx(iconClasses, 'mr-2')}>
            {leftIcon}
          </span>
        )}
        
        {children}
        
        {!loading && rightIcon && (
          <span className={clsx(iconClasses, 'ml-2')}>
            {rightIcon}
          </span>
        )}
      </button>
    )
  }
)

Button.displayName = 'Button'

export { Button }

// Icon Button variant
export interface IconButtonProps extends Omit<ButtonProps, 'leftIcon' | 'rightIcon' | 'children'> {
  icon: React.ReactNode
  'aria-label': string
}

export const IconButton = forwardRef<HTMLButtonElement, IconButtonProps>(
  ({ icon, className, size = 'md', rounded = true, ...props }, ref) => {
    const iconSizeClasses = {
      xs: 'w-3 h-3',
      sm: 'w-4 h-4',
      md: 'w-5 h-5',
      lg: 'w-6 h-6',
      xl: 'w-7 h-7',
    }

    const paddingClasses = {
      xs: 'p-1',
      sm: 'p-1.5',
      md: 'p-2',
      lg: 'p-3',
      xl: 'p-4',
    }

    return (
      <Button
        ref={ref}
        size={size}
        rounded={rounded}
        className={clsx(paddingClasses[size], className)}
        {...props}
      >
        <span className={iconSizeClasses[size]}>
          {icon}
        </span>
      </Button>
    )
  }
)

IconButton.displayName = 'IconButton'

// Button Group component
export interface ButtonGroupProps {
  children: React.ReactNode
  className?: string
  orientation?: 'horizontal' | 'vertical'
  size?: ButtonProps['size']
  variant?: ButtonProps['variant']
}

export const ButtonGroup: React.FC<ButtonGroupProps> = ({
  children,
  className,
  orientation = 'horizontal',
  size,
  variant,
}) => {
  const groupClasses = clsx(
    'inline-flex',
    {
      'flex-row': orientation === 'horizontal',
      'flex-col': orientation === 'vertical',
    },
    className
  )

  return (
    <div className={groupClasses} role="group">
      {React.Children.map(children, (child, index) => {
        if (React.isValidElement(child) && child.type === Button) {
          const isFirst = index === 0
          const isLast = index === React.Children.count(children) - 1
          
          return React.cloneElement(child, {
            size: size || child.props.size,
            variant: variant || child.props.variant,
            className: clsx(
              child.props.className,
              {
                // Horizontal orientation
                'rounded-r-none border-r-0': orientation === 'horizontal' && !isLast,
                'rounded-l-none': orientation === 'horizontal' && !isFirst,
                // Vertical orientation
                'rounded-b-none border-b-0': orientation === 'vertical' && !isLast,
                'rounded-t-none': orientation === 'vertical' && !isFirst,
              }
            ),
          })
        }
        return child
      })}
    </div>
  )
}
