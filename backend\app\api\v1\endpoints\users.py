"""
KnowledgeBot - Corporate Memory System
User Management API Endpoints

This module handles user management operations including CRUD operations,
role assignments, and user administration with enterprise security.
"""

from typing import List, Dict, Any, Optional
from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, desc
from sqlalchemy.orm import selectinload

from app.api.deps import get_current_user, get_db, require_permissions, get_common_params, CommonQueryParams
from app.models.user import User, Role, Department
from app.schemas.user import (
    UserResponse,
    UserCreate,
    UserUpdate,
    UserListResponse,
    UserRoleUpdate,
    UserDepartmentUpdate
)
from app.services.auth_service import AuthService
from app.services.email_service import EmailService
from app.core.security import get_password_hash

router = APIRouter()
auth_service = AuthService()
email_service = EmailService()


@router.get("/", response_model=UserListResponse)
async def list_users(
    common: CommonQueryParams = Depends(get_common_params),
    search: Optional[str] = Query(None, description="Search users by name or email"),
    role: Optional[str] = Query(None, description="Filter by role"),
    department: Optional[str] = Query(None, description="Filter by department"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    _: None = Depends(require_permissions(["users.read"]))
) -> Dict[str, Any]:
    """
    List users with filtering and pagination.
    
    Args:
        common: Common query parameters
        search: Search term for name/email
        role: Filter by role name
        department: Filter by department name
        is_active: Filter by active status
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Dict[str, Any]: Paginated user list
    """
    try:
        # Build query
        query = select(User).options(
            selectinload(User.roles),
            selectinload(User.departments)
        )
        
        # Apply filters
        filters = []
        
        if search:
            search_filter = or_(
                User.first_name.ilike(f"%{search}%"),
                User.last_name.ilike(f"%{search}%"),
                User.email.ilike(f"%{search}%"),
                User.display_name.ilike(f"%{search}%")
            )
            filters.append(search_filter)
        
        if role:
            filters.append(User.roles.any(Role.name == role))
        
        if department:
            filters.append(User.departments.any(Department.name == department))
        
        if is_active is not None:
            filters.append(User.is_active == is_active)
        
        if filters:
            query = query.where(and_(*filters))
        
        # Apply sorting
        if common.sort_by == "name":
            sort_field = User.first_name if common.sort_order == "asc" else desc(User.first_name)
        elif common.sort_by == "email":
            sort_field = User.email if common.sort_order == "asc" else desc(User.email)
        elif common.sort_by == "last_login":
            sort_field = User.last_login if common.sort_order == "asc" else desc(User.last_login)
        else:  # created_at
            sort_field = User.created_at if common.sort_order == "asc" else desc(User.created_at)
        
        query = query.order_by(sort_field)
        
        # Get total count
        count_query = select(func.count()).select_from(query.subquery())
        total_result = await db.execute(count_query)
        total = total_result.scalar()
        
        # Apply pagination
        query = query.offset(common.skip).limit(common.limit)
        result = await db.execute(query)
        users = result.scalars().all()
        
        return {
            "users": users,
            "total": total,
            "skip": common.skip,
            "limit": common.limit,
            "has_more": common.skip + common.limit < total
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list users: {str(e)}"
        )


@router.get("/{user_id}", response_model=UserResponse)
async def get_user(
    user_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    _: None = Depends(require_permissions(["users.read"]))
) -> User:
    """
    Get user by ID.
    
    Args:
        user_id: User UUID
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        User: User object
        
    Raises:
        HTTPException: If user not found
    """
    try:
        stmt = select(User).options(
            selectinload(User.roles),
            selectinload(User.departments)
        ).where(User.id == user_id)
        
        result = await db.execute(stmt)
        user = result.scalar_one_or_none()
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        return user
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get user: {str(e)}"
        )


@router.post("/", response_model=UserResponse)
async def create_user(
    user_data: UserCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    _: None = Depends(require_permissions(["users.create"]))
) -> User:
    """
    Create new user.
    
    Args:
        user_data: User creation data
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        User: Created user object
        
    Raises:
        HTTPException: If user creation fails
    """
    try:
        # Check if user already exists
        stmt = select(User).where(User.email == user_data.email.lower())
        result = await db.execute(stmt)
        existing_user = result.scalar_one_or_none()
        
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User with this email already exists"
            )
        
        # Create new user
        user = User(
            email=user_data.email.lower(),
            hashed_password=get_password_hash(user_data.password) if user_data.password else None,
            first_name=user_data.first_name,
            last_name=user_data.last_name,
            display_name=user_data.display_name,
            is_active=user_data.is_active,
            is_verified=user_data.is_verified,
            must_change_password=user_data.must_change_password,
            created_by=current_user.id
        )
        
        db.add(user)
        await db.flush()  # Get user ID
        
        # Assign roles if provided
        if user_data.role_ids:
            roles_stmt = select(Role).where(Role.id.in_(user_data.role_ids))
            roles_result = await db.execute(roles_stmt)
            roles = roles_result.scalars().all()
            user.roles.extend(roles)
        
        # Assign departments if provided
        if user_data.department_ids:
            dept_stmt = select(Department).where(Department.id.in_(user_data.department_ids))
            dept_result = await db.execute(dept_stmt)
            departments = dept_result.scalars().all()
            user.departments.extend(departments)
        
        await db.commit()
        await db.refresh(user)
        
        # Send welcome email if user is active
        if user.is_active and user.email:
            await email_service.send_welcome_email(
                user.email,
                user.display_name or user.first_name or user.email
            )
        
        return user
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create user: {str(e)}"
        )


@router.put("/{user_id}", response_model=UserResponse)
async def update_user(
    user_id: UUID,
    user_data: UserUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    _: None = Depends(require_permissions(["users.update"]))
) -> User:
    """
    Update user information.
    
    Args:
        user_id: User UUID
        user_data: User update data
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        User: Updated user object
        
    Raises:
        HTTPException: If user not found or update fails
    """
    try:
        stmt = select(User).where(User.id == user_id)
        result = await db.execute(stmt)
        user = result.scalar_one_or_none()
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Update fields
        update_data = user_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            if field == "password" and value:
                user.hashed_password = get_password_hash(value)
                user.must_change_password = False
            elif field == "email" and value:
                # Check if email is already taken
                email_stmt = select(User).where(
                    and_(User.email == value.lower(), User.id != user_id)
                )
                email_result = await db.execute(email_stmt)
                if email_result.scalar_one_or_none():
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Email already taken"
                    )
                user.email = value.lower()
            else:
                setattr(user, field, value)
        
        user.updated_by = current_user.id
        
        await db.commit()
        await db.refresh(user)
        
        return user
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update user: {str(e)}"
        )


@router.delete("/{user_id}")
async def delete_user(
    user_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    _: None = Depends(require_permissions(["users.delete"]))
) -> Dict[str, str]:
    """
    Soft delete user.
    
    Args:
        user_id: User UUID
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Dict[str, str]: Deletion confirmation
        
    Raises:
        HTTPException: If user not found or deletion fails
    """
    try:
        if user_id == current_user.id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot delete your own account"
            )
        
        stmt = select(User).where(User.id == user_id)
        result = await db.execute(stmt)
        user = result.scalar_one_or_none()
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Soft delete
        user.is_active = False
        user.deleted_at = datetime.utcnow()
        user.deleted_by = current_user.id
        
        # Invalidate all user sessions
        await auth_service.invalidate_user_sessions(db, user_id)
        
        await db.commit()
        
        return {"message": "User successfully deleted"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete user: {str(e)}"
        )


@router.put("/{user_id}/roles", response_model=UserResponse)
async def update_user_roles(
    user_id: UUID,
    role_data: UserRoleUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    _: None = Depends(require_permissions(["users.roles.update"]))
) -> User:
    """
    Update user roles.
    
    Args:
        user_id: User UUID
        role_data: Role update data
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        User: Updated user object
    """
    try:
        stmt = select(User).options(selectinload(User.roles)).where(User.id == user_id)
        result = await db.execute(stmt)
        user = result.scalar_one_or_none()
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Get new roles
        roles_stmt = select(Role).where(Role.id.in_(role_data.role_ids))
        roles_result = await db.execute(roles_stmt)
        new_roles = roles_result.scalars().all()
        
        # Update user roles
        user.roles.clear()
        user.roles.extend(new_roles)
        user.updated_by = current_user.id
        
        await db.commit()
        await db.refresh(user)
        
        return user
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update user roles: {str(e)}"
        )


@router.put("/{user_id}/departments", response_model=UserResponse)
async def update_user_departments(
    user_id: UUID,
    dept_data: UserDepartmentUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    _: None = Depends(require_permissions(["users.departments.update"]))
) -> User:
    """
    Update user departments.
    
    Args:
        user_id: User UUID
        dept_data: Department update data
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        User: Updated user object
    """
    try:
        stmt = select(User).options(selectinload(User.departments)).where(User.id == user_id)
        result = await db.execute(stmt)
        user = result.scalar_one_or_none()
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Get new departments
        dept_stmt = select(Department).where(Department.id.in_(dept_data.department_ids))
        dept_result = await db.execute(dept_stmt)
        new_departments = dept_result.scalars().all()
        
        # Update user departments
        user.departments.clear()
        user.departments.extend(new_departments)
        user.updated_by = current_user.id
        
        await db.commit()
        await db.refresh(user)
        
        return user
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update user departments: {str(e)}"
        )


@router.post("/{user_id}/unlock")
async def unlock_user(
    user_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    _: None = Depends(require_permissions(["users.unlock"]))
) -> Dict[str, str]:
    """
    Unlock user account.
    
    Args:
        user_id: User UUID
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Dict[str, str]: Success message
    """
    try:
        stmt = select(User).where(User.id == user_id)
        result = await db.execute(stmt)
        user = result.scalar_one_or_none()
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        success = await auth_service.unlock_user_account(db, user.email)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to unlock user account"
            )
        
        return {"message": "User account unlocked successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to unlock user: {str(e)}"
        )


@router.post("/{user_id}/reset-password")
async def reset_user_password(
    user_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    _: None = Depends(require_permissions(["users.password.reset"]))
) -> Dict[str, str]:
    """
    Reset user password (generates temporary password).
    
    Args:
        user_id: User UUID
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Dict[str, str]: Success message with temporary password
    """
    try:
        stmt = select(User).where(User.id == user_id)
        result = await db.execute(stmt)
        user = result.scalar_one_or_none()
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Generate temporary password
        import secrets
        import string
        temp_password = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(12))
        
        success = await auth_service.reset_password(db, user.email, temp_password)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to reset user password"
            )
        
        return {
            "message": "Password reset successfully",
            "temporary_password": temp_password,
            "note": "User must change password on next login"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to reset password: {str(e)}"
        )
