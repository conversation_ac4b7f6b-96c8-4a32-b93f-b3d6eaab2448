"""
KnowledgeBot Backend - Search Service Tests
Unit tests for search service functionality including rate limiting.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from uuid import uuid4

from app.services.search_service import SearchService, RateLimitTier
from app.models.user import User, Role


class TestSearchService:
    """Test cases for SearchService."""
    
    @pytest.fixture
    def search_service(self):
        """Create SearchService instance."""
        with patch('app.services.search_service.MemVidService') as mock_memvid:
            mock_memvid.return_value = AsyncMock()
            return SearchService()
    
    @pytest.mark.asyncio
    async def test_vector_similarity_search_success(self, search_service, db_session):
        """Test successful vector similarity search."""
        query_embedding = [0.1] * 384
        user_departments = [uuid4()]
        
        with patch.object(search_service, '_execute_vector_search') as mock_search:
            mock_chunks = [
                MagicMock(
                    id=uuid4(),
                    content="Test content 1",
                    document=MagicMock(title="Test Doc 1"),
                    similarity_score=0.9
                ),
                MagicMock(
                    id=uuid4(),
                    content="Test content 2", 
                    document=MagicMock(title="Test Doc 2"),
                    similarity_score=0.8
                )
            ]
            mock_search.return_value = mock_chunks
            
            results = await search_service.vector_similarity_search(
                db_session, query_embedding, user_departments
            )
            
            assert isinstance(results, list)
            assert len(results) == 2
            assert all(hasattr(chunk, 'similarity_score') for chunk in results)
    
    @pytest.mark.asyncio
    async def test_vector_similarity_search_with_filters(self, search_service, db_session):
        """Test vector similarity search with filters."""
        query_embedding = [0.1] * 384
        user_departments = [uuid4()]
        filters = {
            "document_type": "pdf",
            "classification_level": "public",
            "date_from": "2023-01-01",
            "date_to": "2023-12-31"
        }
        
        with patch.object(search_service, '_execute_vector_search') as mock_search:
            mock_search.return_value = []
            
            results = await search_service.vector_similarity_search(
                db_session, query_embedding, user_departments, filters=filters
            )
            
            mock_search.assert_called_once()
            call_args = mock_search.call_args
            assert filters in call_args[0] or any(filters == arg for arg in call_args[1].values())
    
    @pytest.mark.asyncio
    async def test_hybrid_search_success(self, search_service, db_session):
        """Test successful hybrid search combining vector and text search."""
        query = "artificial intelligence machine learning"
        user_departments = [uuid4()]
        
        with patch.object(search_service, 'vector_similarity_search') as mock_vector:
            with patch.object(search_service, 'full_text_search') as mock_text:
                # Mock vector search results
                mock_vector.return_value = [
                    MagicMock(id=uuid4(), similarity_score=0.9),
                    MagicMock(id=uuid4(), similarity_score=0.8)
                ]
                
                # Mock text search results
                mock_text.return_value = [
                    MagicMock(id=uuid4(), relevance_score=0.85),
                    MagicMock(id=uuid4(), relevance_score=0.75)
                ]
                
                results = await search_service.hybrid_search(
                    db_session, query, user_departments
                )
                
                assert isinstance(results, list)
                mock_vector.assert_called_once()
                mock_text.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_full_text_search_success(self, search_service, db_session):
        """Test successful full-text search."""
        query = "machine learning algorithms"
        user_departments = [uuid4()]
        
        with patch.object(search_service, '_execute_text_search') as mock_search:
            mock_chunks = [
                MagicMock(
                    id=uuid4(),
                    content="Machine learning algorithms are powerful",
                    document=MagicMock(title="ML Guide"),
                    relevance_score=0.9
                )
            ]
            mock_search.return_value = mock_chunks
            
            results = await search_service.full_text_search(
                db_session, query, user_departments
            )
            
            assert isinstance(results, list)
            assert len(results) == 1
    
    @pytest.mark.asyncio
    async def test_get_related_queries_success(self, search_service):
        """Test getting related queries."""
        query = "machine learning"
        
        with patch.object(search_service, '_generate_related_queries') as mock_related:
            mock_related.return_value = [
                "deep learning",
                "neural networks", 
                "artificial intelligence"
            ]
            
            related = await search_service.get_related_queries(query)
            
            assert isinstance(related, list)
            assert len(related) == 3
            assert all(isinstance(q, str) for q in related)
    
    @pytest.mark.asyncio
    async def test_search_suggestions_success(self, search_service, db_session):
        """Test search suggestions generation."""
        partial_query = "mach"
        user_departments = [uuid4()]
        
        with patch.object(search_service, '_get_search_suggestions') as mock_suggestions:
            mock_suggestions.return_value = [
                {"suggestion": "machine learning", "frequency": 10},
                {"suggestion": "machine vision", "frequency": 5}
            ]
            
            suggestions = await search_service.get_search_suggestions(
                db_session, partial_query, user_departments
            )
            
            assert isinstance(suggestions, list)
            assert len(suggestions) == 2
            assert all("suggestion" in s for s in suggestions)


class TestRateLimitTier:
    """Test cases for RateLimitTier functionality."""
    
    def test_get_user_tier_superuser(self):
        """Test tier determination for superuser."""
        user = MagicMock(spec=User)
        user.is_superuser = True
        user.roles = []
        
        tier = RateLimitTier.get_user_tier(user)
        
        assert tier == "enterprise"
    
    def test_get_user_tier_admin(self):
        """Test tier determination for admin user."""
        admin_role = MagicMock()
        admin_role.name = "admin"
        
        user = MagicMock(spec=User)
        user.is_superuser = False
        user.roles = [admin_role]
        
        tier = RateLimitTier.get_user_tier(user)
        
        assert tier == "admin"
    
    def test_get_user_tier_premium(self):
        """Test tier determination for premium user."""
        premium_role = MagicMock()
        premium_role.name = "premium"
        
        user = MagicMock(spec=User)
        user.is_superuser = False
        user.roles = [premium_role]
        
        tier = RateLimitTier.get_user_tier(user)
        
        assert tier == "premium"
    
    def test_get_user_tier_regular_user(self):
        """Test tier determination for regular user."""
        user_role = MagicMock()
        user_role.name = "user"
        
        user = MagicMock(spec=User)
        user.is_superuser = False
        user.roles = [user_role]
        
        tier = RateLimitTier.get_user_tier(user)
        
        assert tier == "user"
    
    def test_get_user_tier_guest(self):
        """Test tier determination for guest user."""
        user = MagicMock(spec=User)
        user.is_superuser = False
        user.roles = []
        
        tier = RateLimitTier.get_user_tier(user)
        
        assert tier == "guest"
    
    def test_check_rate_limit_allowed(self):
        """Test rate limit check when allowed."""
        user = MagicMock(spec=User)
        user.is_superuser = False
        user.roles = []
        user.id = uuid4()
        
        with patch('app.services.search_service.rate_limiter') as mock_limiter:
            mock_limiter.is_allowed.return_value = True
            
            result = RateLimitTier.check_rate_limit(user, "queries")
            
            assert result is True
            mock_limiter.is_allowed.assert_called_once()
    
    def test_check_rate_limit_exceeded(self):
        """Test rate limit check when exceeded."""
        user = MagicMock(spec=User)
        user.is_superuser = False
        user.roles = []
        user.id = uuid4()
        
        with patch('app.services.search_service.rate_limiter') as mock_limiter:
            mock_limiter.is_allowed.return_value = False
            
            result = RateLimitTier.check_rate_limit(user, "queries")
            
            assert result is False
    
    def test_rate_limit_tiers_configuration(self):
        """Test rate limit tiers are properly configured."""
        tiers = RateLimitTier.RATE_LIMITS
        
        assert "guest" in tiers
        assert "user" in tiers
        assert "premium" in tiers
        assert "admin" in tiers
        assert "enterprise" in tiers
        
        # Check that higher tiers have higher limits
        assert tiers["enterprise"]["queries"] > tiers["admin"]["queries"]
        assert tiers["admin"]["queries"] > tiers["premium"]["queries"]
        assert tiers["premium"]["queries"] > tiers["user"]["queries"]
        assert tiers["user"]["queries"] > tiers["guest"]["queries"]
    
    @pytest.mark.asyncio
    async def test_search_with_rate_limiting(self, search_service, db_session):
        """Test search functionality with rate limiting."""
        user = MagicMock(spec=User)
        user.is_superuser = False
        user.roles = []
        user.id = uuid4()
        
        query = "test query"
        user_departments = [uuid4()]
        
        with patch.object(RateLimitTier, 'check_rate_limit') as mock_rate_check:
            mock_rate_check.return_value = True
            
            with patch.object(search_service, 'hybrid_search') as mock_search:
                mock_search.return_value = []
                
                # This would be called from the endpoint with rate limiting
                rate_allowed = RateLimitTier.check_rate_limit(user, "searches")
                
                if rate_allowed:
                    results = await search_service.hybrid_search(
                        db_session, query, user_departments
                    )
                    assert isinstance(results, list)
                
                mock_rate_check.assert_called_once_with(user, "searches")
    
    @pytest.mark.asyncio
    async def test_search_rate_limit_exceeded(self, search_service):
        """Test search when rate limit is exceeded."""
        user = MagicMock(spec=User)
        user.is_superuser = False
        user.roles = []
        user.id = uuid4()
        
        with patch.object(RateLimitTier, 'check_rate_limit') as mock_rate_check:
            mock_rate_check.return_value = False
            
            rate_allowed = RateLimitTier.check_rate_limit(user, "searches")
            
            assert rate_allowed is False
            # In the actual endpoint, this would raise an HTTPException
