{"version": 3, "file": "index.js", "sources": ["../src/constants.js", "../src/utils.js", "../src/server.js", "../src/HelmetData.js", "../src/Provider.js", "../src/client.js", "../src/Dispatcher.js", "../src/index.js"], "sourcesContent": ["export const TAG_PROPERTIES = {\n  CHARSET: 'charset',\n  CSS_TEXT: 'cssText',\n  HREF: 'href',\n  HTTPEQUIV: 'http-equiv',\n  INNER_HTML: 'innerHTML',\n  ITEM_PROP: 'itemprop',\n  NAME: 'name',\n  PROPERTY: 'property',\n  REL: 'rel',\n  SRC: 'src',\n};\n\nexport const ATTRIBUTE_NAMES = {\n  BODY: 'bodyAttributes',\n  HTML: 'htmlAttributes',\n  TITLE: 'titleAttributes',\n};\n\nexport const TAG_NAMES = {\n  BASE: 'base',\n  BODY: 'body',\n  HEAD: 'head',\n  HTML: 'html',\n  LINK: 'link',\n  META: 'meta',\n  NOSCRIPT: 'noscript',\n  SCRIPT: 'script',\n  STYLE: 'style',\n  TITLE: 'title',\n  FRAGMENT: 'Symbol(react.fragment)',\n};\n\nexport const SEO_PRIORITY_TAGS = {\n  link: { rel: ['amphtml', 'canonical', 'alternate'] },\n  script: { type: ['application/ld+json'] },\n  meta: {\n    charset: '',\n    name: ['robots', 'description'],\n    property: [\n      'og:type',\n      'og:title',\n      'og:url',\n      'og:image',\n      'og:image:alt',\n      'og:description',\n      'twitter:url',\n      'twitter:title',\n      'twitter:description',\n      'twitter:image',\n      'twitter:image:alt',\n      'twitter:card',\n      'twitter:site',\n    ],\n  },\n};\n\nexport const VALID_TAG_NAMES = Object.keys(TAG_NAMES).map(name => TAG_NAMES[name]);\n\nexport const REACT_TAG_MAP = {\n  accesskey: 'accessKey',\n  charset: 'charSet',\n  class: 'className',\n  contenteditable: 'contentEditable',\n  contextmenu: 'contextMenu',\n  'http-equiv': 'httpEquiv',\n  itemprop: 'itemProp',\n  tabindex: 'tabIndex',\n};\n\nexport const HTML_TAG_MAP = Object.keys(REACT_TAG_MAP).reduce((obj, key) => {\n  obj[REACT_TAG_MAP[key]] = key;\n  return obj;\n}, {});\n\nexport const HELMET_ATTRIBUTE = 'data-rh';\n", "import { TAG_NAMES, TAG_PROPERTIES, ATTRIBUTE_NAMES } from './constants';\n\nconst HELMET_PROPS = {\n  DEFAULT_TITLE: 'defaultTitle',\n  DEFER: 'defer',\n  ENCODE_SPECIAL_CHARACTERS: 'encodeSpecialCharacters',\n  ON_CHANGE_CLIENT_STATE: 'onChangeClientState',\n  TITLE_TEMPLATE: 'titleTemplate',\n  PRIORITIZE_SEO_TAGS: 'prioritizeSeoTags',\n};\n\nconst getInnermostProperty = (propsList, property) => {\n  for (let i = propsList.length - 1; i >= 0; i -= 1) {\n    const props = propsList[i];\n\n    if (Object.prototype.hasOwnProperty.call(props, property)) {\n      return props[property];\n    }\n  }\n\n  return null;\n};\n\nconst getTitleFromPropsList = propsList => {\n  let innermostTitle = getInnermostProperty(propsList, TAG_NAMES.TITLE);\n  const innermostTemplate = getInnermostProperty(propsList, HELMET_PROPS.TITLE_TEMPLATE);\n  if (Array.isArray(innermostTitle)) {\n    innermostTitle = innermostTitle.join('');\n  }\n  if (innermostTemplate && innermostTitle) {\n    // use function arg to avoid need to escape $ characters\n    return innermostTemplate.replace(/%s/g, () => innermostTitle);\n  }\n\n  const innermostDefaultTitle = getInnermostProperty(propsList, HELMET_PROPS.DEFAULT_TITLE);\n\n  return innermostTitle || innermostDefaultTitle || undefined;\n};\n\nconst getOnChangeClientState = propsList =>\n  getInnermostProperty(propsList, HELMET_PROPS.ON_CHANGE_CLIENT_STATE) || (() => {});\n\nconst getAttributesFromPropsList = (tagType, propsList) =>\n  propsList\n    .filter(props => typeof props[tagType] !== 'undefined')\n    .map(props => props[tagType])\n    .reduce((tagAttrs, current) => ({ ...tagAttrs, ...current }), {});\n\nconst getBaseTagFromPropsList = (primaryAttributes, propsList) =>\n  propsList\n    .filter(props => typeof props[TAG_NAMES.BASE] !== 'undefined')\n    .map(props => props[TAG_NAMES.BASE])\n    .reverse()\n    .reduce((innermostBaseTag, tag) => {\n      if (!innermostBaseTag.length) {\n        const keys = Object.keys(tag);\n\n        for (let i = 0; i < keys.length; i += 1) {\n          const attributeKey = keys[i];\n          const lowerCaseAttributeKey = attributeKey.toLowerCase();\n\n          if (\n            primaryAttributes.indexOf(lowerCaseAttributeKey) !== -1 &&\n            tag[lowerCaseAttributeKey]\n          ) {\n            return innermostBaseTag.concat(tag);\n          }\n        }\n      }\n\n      return innermostBaseTag;\n    }, []);\n\n// eslint-disable-next-line no-console\nconst warn = msg => console && typeof console.warn === 'function' && console.warn(msg);\n\nconst getTagsFromPropsList = (tagName, primaryAttributes, propsList) => {\n  // Calculate list of tags, giving priority innermost component (end of the propslist)\n  const approvedSeenTags = {};\n\n  return propsList\n    .filter(props => {\n      if (Array.isArray(props[tagName])) {\n        return true;\n      }\n      if (typeof props[tagName] !== 'undefined') {\n        warn(\n          `Helmet: ${tagName} should be of type \"Array\". Instead found type \"${typeof props[\n            tagName\n          ]}\"`\n        );\n      }\n      return false;\n    })\n    .map(props => props[tagName])\n    .reverse()\n    .reduce((approvedTags, instanceTags) => {\n      const instanceSeenTags = {};\n\n      instanceTags\n        .filter(tag => {\n          let primaryAttributeKey;\n          const keys = Object.keys(tag);\n          for (let i = 0; i < keys.length; i += 1) {\n            const attributeKey = keys[i];\n            const lowerCaseAttributeKey = attributeKey.toLowerCase();\n\n            // Special rule with link tags, since rel and href are both primary tags, rel takes priority\n            if (\n              primaryAttributes.indexOf(lowerCaseAttributeKey) !== -1 &&\n              !(\n                primaryAttributeKey === TAG_PROPERTIES.REL &&\n                tag[primaryAttributeKey].toLowerCase() === 'canonical'\n              ) &&\n              !(\n                lowerCaseAttributeKey === TAG_PROPERTIES.REL &&\n                tag[lowerCaseAttributeKey].toLowerCase() === 'stylesheet'\n              )\n            ) {\n              primaryAttributeKey = lowerCaseAttributeKey;\n            }\n            // Special case for innerHTML which doesn't work lowercased\n            if (\n              primaryAttributes.indexOf(attributeKey) !== -1 &&\n              (attributeKey === TAG_PROPERTIES.INNER_HTML ||\n                attributeKey === TAG_PROPERTIES.CSS_TEXT ||\n                attributeKey === TAG_PROPERTIES.ITEM_PROP)\n            ) {\n              primaryAttributeKey = attributeKey;\n            }\n          }\n\n          if (!primaryAttributeKey || !tag[primaryAttributeKey]) {\n            return false;\n          }\n\n          const value = tag[primaryAttributeKey].toLowerCase();\n\n          if (!approvedSeenTags[primaryAttributeKey]) {\n            approvedSeenTags[primaryAttributeKey] = {};\n          }\n\n          if (!instanceSeenTags[primaryAttributeKey]) {\n            instanceSeenTags[primaryAttributeKey] = {};\n          }\n\n          if (!approvedSeenTags[primaryAttributeKey][value]) {\n            instanceSeenTags[primaryAttributeKey][value] = true;\n            return true;\n          }\n\n          return false;\n        })\n        .reverse()\n        .forEach(tag => approvedTags.push(tag));\n\n      // Update seen tags with tags from this instance\n      const keys = Object.keys(instanceSeenTags);\n      for (let i = 0; i < keys.length; i += 1) {\n        const attributeKey = keys[i];\n        const tagUnion = {\n          ...approvedSeenTags[attributeKey],\n          ...instanceSeenTags[attributeKey],\n        };\n\n        approvedSeenTags[attributeKey] = tagUnion;\n      }\n\n      return approvedTags;\n    }, [])\n    .reverse();\n};\n\nconst getAnyTrueFromPropsList = (propsList, checkedTag) => {\n  if (Array.isArray(propsList) && propsList.length) {\n    for (let index = 0; index < propsList.length; index += 1) {\n      const prop = propsList[index];\n      if (prop[checkedTag]) {\n        return true;\n      }\n    }\n  }\n  return false;\n};\n\nconst reducePropsToState = propsList => ({\n  baseTag: getBaseTagFromPropsList([TAG_PROPERTIES.HREF], propsList),\n  bodyAttributes: getAttributesFromPropsList(ATTRIBUTE_NAMES.BODY, propsList),\n  defer: getInnermostProperty(propsList, HELMET_PROPS.DEFER),\n  encode: getInnermostProperty(propsList, HELMET_PROPS.ENCODE_SPECIAL_CHARACTERS),\n  htmlAttributes: getAttributesFromPropsList(ATTRIBUTE_NAMES.HTML, propsList),\n  linkTags: getTagsFromPropsList(\n    TAG_NAMES.LINK,\n    [TAG_PROPERTIES.REL, TAG_PROPERTIES.HREF],\n    propsList\n  ),\n  metaTags: getTagsFromPropsList(\n    TAG_NAMES.META,\n    [\n      TAG_PROPERTIES.NAME,\n      TAG_PROPERTIES.CHARSET,\n      TAG_PROPERTIES.HTTPEQUIV,\n      TAG_PROPERTIES.PROPERTY,\n      TAG_PROPERTIES.ITEM_PROP,\n    ],\n    propsList\n  ),\n  noscriptTags: getTagsFromPropsList(TAG_NAMES.NOSCRIPT, [TAG_PROPERTIES.INNER_HTML], propsList),\n  onChangeClientState: getOnChangeClientState(propsList),\n  scriptTags: getTagsFromPropsList(\n    TAG_NAMES.SCRIPT,\n    [TAG_PROPERTIES.SRC, TAG_PROPERTIES.INNER_HTML],\n    propsList\n  ),\n  styleTags: getTagsFromPropsList(TAG_NAMES.STYLE, [TAG_PROPERTIES.CSS_TEXT], propsList),\n  title: getTitleFromPropsList(propsList),\n  titleAttributes: getAttributesFromPropsList(ATTRIBUTE_NAMES.TITLE, propsList),\n  prioritizeSeoTags: getAnyTrueFromPropsList(propsList, HELMET_PROPS.PRIORITIZE_SEO_TAGS),\n});\n\nexport const flattenArray = possibleArray =>\n  Array.isArray(possibleArray) ? possibleArray.join('') : possibleArray;\n\nexport { reducePropsToState };\n\nconst checkIfPropsMatch = (props, toMatch) => {\n  const keys = Object.keys(props);\n  for (let i = 0; i < keys.length; i += 1) {\n    // e.g. if rel exists in the list of allowed props [amphtml, alternate, etc]\n    if (toMatch[keys[i]] && toMatch[keys[i]].includes(props[keys[i]])) {\n      return true;\n    }\n  }\n  return false;\n};\n\nexport const prioritizer = (elementsList, propsToMatch) => {\n  if (Array.isArray(elementsList)) {\n    return elementsList.reduce(\n      (acc, elementAttrs) => {\n        if (checkIfPropsMatch(elementAttrs, propsToMatch)) {\n          acc.priority.push(elementAttrs);\n        } else {\n          acc.default.push(elementAttrs);\n        }\n        return acc;\n      },\n      { priority: [], default: [] }\n    );\n  }\n  return { default: elementsList };\n};\n\nexport const without = (obj, key) => {\n  return {\n    ...obj,\n    [key]: undefined,\n  };\n};\n", "import React from 'react';\nimport {\n  HELMET_ATTRIBUTE,\n  TAG_NAMES,\n  REACT_TAG_MAP,\n  TAG_PROPERTIES,\n  ATTRIBUTE_NAMES,\n  SEO_PRIORITY_TAGS,\n} from './constants';\nimport { flattenArray, prioritizer } from './utils';\n\nconst SELF_CLOSING_TAGS = [TAG_NAMES.NOSCRIPT, TAG_NAMES.SCRIPT, TAG_NAMES.STYLE];\n\nconst encodeSpecialCharacters = (str, encode = true) => {\n  if (encode === false) {\n    return String(str);\n  }\n\n  return String(str)\n    .replace(/&/g, '&amp;')\n    .replace(/</g, '&lt;')\n    .replace(/>/g, '&gt;')\n    .replace(/\"/g, '&quot;')\n    .replace(/'/g, '&#x27;');\n};\n\nconst generateElementAttributesAsString = attributes =>\n  Object.keys(attributes).reduce((str, key) => {\n    const attr = typeof attributes[key] !== 'undefined' ? `${key}=\"${attributes[key]}\"` : `${key}`;\n    return str ? `${str} ${attr}` : attr;\n  }, '');\n\nconst generateTitleAsString = (type, title, attributes, encode) => {\n  const attributeString = generateElementAttributesAsString(attributes);\n  const flattenedTitle = flattenArray(title);\n  return attributeString\n    ? `<${type} ${HELMET_ATTRIBUTE}=\"true\" ${attributeString}>${encodeSpecialCharacters(\n        flattenedTitle,\n        encode\n      )}</${type}>`\n    : `<${type} ${HELMET_ATTRIBUTE}=\"true\">${encodeSpecialCharacters(\n        flattenedTitle,\n        encode\n      )}</${type}>`;\n};\n\nconst generateTagsAsString = (type, tags, encode) =>\n  tags.reduce((str, tag) => {\n    const attributeHtml = Object.keys(tag)\n      .filter(\n        attribute =>\n          !(attribute === TAG_PROPERTIES.INNER_HTML || attribute === TAG_PROPERTIES.CSS_TEXT)\n      )\n      .reduce((string, attribute) => {\n        const attr =\n          typeof tag[attribute] === 'undefined'\n            ? attribute\n            : `${attribute}=\"${encodeSpecialCharacters(tag[attribute], encode)}\"`;\n        return string ? `${string} ${attr}` : attr;\n      }, '');\n\n    const tagContent = tag.innerHTML || tag.cssText || '';\n\n    const isSelfClosing = SELF_CLOSING_TAGS.indexOf(type) === -1;\n\n    return `${str}<${type} ${HELMET_ATTRIBUTE}=\"true\" ${attributeHtml}${\n      isSelfClosing ? `/>` : `>${tagContent}</${type}>`\n    }`;\n  }, '');\n\nconst convertElementAttributesToReactProps = (attributes, initProps = {}) =>\n  Object.keys(attributes).reduce((obj, key) => {\n    obj[REACT_TAG_MAP[key] || key] = attributes[key];\n    return obj;\n  }, initProps);\n\nconst generateTitleAsReactComponent = (type, title, attributes) => {\n  // assigning into an array to define toString function on it\n  const initProps = {\n    key: title,\n    [HELMET_ATTRIBUTE]: true,\n  };\n  const props = convertElementAttributesToReactProps(attributes, initProps);\n\n  return [React.createElement(TAG_NAMES.TITLE, props, title)];\n};\n\nconst generateTagsAsReactComponent = (type, tags) =>\n  tags.map((tag, i) => {\n    const mappedTag = {\n      key: i,\n      [HELMET_ATTRIBUTE]: true,\n    };\n\n    Object.keys(tag).forEach(attribute => {\n      const mappedAttribute = REACT_TAG_MAP[attribute] || attribute;\n\n      if (\n        mappedAttribute === TAG_PROPERTIES.INNER_HTML ||\n        mappedAttribute === TAG_PROPERTIES.CSS_TEXT\n      ) {\n        const content = tag.innerHTML || tag.cssText;\n        mappedTag.dangerouslySetInnerHTML = { __html: content };\n      } else {\n        mappedTag[mappedAttribute] = tag[attribute];\n      }\n    });\n\n    return React.createElement(type, mappedTag);\n  });\n\nconst getMethodsForTag = (type, tags, encode) => {\n  switch (type) {\n    case TAG_NAMES.TITLE:\n      return {\n        toComponent: () =>\n          generateTitleAsReactComponent(type, tags.title, tags.titleAttributes, encode),\n        toString: () => generateTitleAsString(type, tags.title, tags.titleAttributes, encode),\n      };\n    case ATTRIBUTE_NAMES.BODY:\n    case ATTRIBUTE_NAMES.HTML:\n      return {\n        toComponent: () => convertElementAttributesToReactProps(tags),\n        toString: () => generateElementAttributesAsString(tags),\n      };\n    default:\n      return {\n        toComponent: () => generateTagsAsReactComponent(type, tags),\n        toString: () => generateTagsAsString(type, tags, encode),\n      };\n  }\n};\n\nconst getPriorityMethods = ({ metaTags, linkTags, scriptTags, encode }) => {\n  const meta = prioritizer(metaTags, SEO_PRIORITY_TAGS.meta);\n  const link = prioritizer(linkTags, SEO_PRIORITY_TAGS.link);\n  const script = prioritizer(scriptTags, SEO_PRIORITY_TAGS.script);\n\n  // need to have toComponent() and toString()\n  const priorityMethods = {\n    toComponent: () => [\n      ...generateTagsAsReactComponent(TAG_NAMES.META, meta.priority),\n      ...generateTagsAsReactComponent(TAG_NAMES.LINK, link.priority),\n      ...generateTagsAsReactComponent(TAG_NAMES.SCRIPT, script.priority),\n    ],\n    toString: () =>\n      // generate all the tags as strings and concatenate them\n      `${getMethodsForTag(TAG_NAMES.META, meta.priority, encode)} ${getMethodsForTag(\n        TAG_NAMES.LINK,\n        link.priority,\n        encode\n      )} ${getMethodsForTag(TAG_NAMES.SCRIPT, script.priority, encode)}`,\n  };\n\n  return {\n    priorityMethods,\n    metaTags: meta.default,\n    linkTags: link.default,\n    scriptTags: script.default,\n  };\n};\n\nconst mapStateOnServer = props => {\n  const {\n    baseTag,\n    bodyAttributes,\n    encode,\n    htmlAttributes,\n    noscriptTags,\n    styleTags,\n    title = '',\n    titleAttributes,\n    prioritizeSeoTags,\n  } = props;\n  let { linkTags, metaTags, scriptTags } = props;\n  let priorityMethods = {\n    toComponent: () => {},\n    toString: () => '',\n  };\n  if (prioritizeSeoTags) {\n    ({ priorityMethods, linkTags, metaTags, scriptTags } = getPriorityMethods(props));\n  }\n  return {\n    priority: priorityMethods,\n    base: getMethodsForTag(TAG_NAMES.BASE, baseTag, encode),\n    bodyAttributes: getMethodsForTag(ATTRIBUTE_NAMES.BODY, bodyAttributes, encode),\n    htmlAttributes: getMethodsForTag(ATTRIBUTE_NAMES.HTML, htmlAttributes, encode),\n    link: getMethodsForTag(TAG_NAMES.LINK, linkTags, encode),\n    meta: getMethodsForTag(TAG_NAMES.META, metaTags, encode),\n    noscript: getMethodsForTag(TAG_NAMES.NOSCRIPT, noscriptTags, encode),\n    script: getMethodsForTag(TAG_NAMES.SCRIPT, scriptTags, encode),\n    style: getMethodsForTag(TAG_NAMES.STYLE, styleTags, encode),\n    title: getMethodsForTag(TAG_NAMES.TITLE, { title, titleAttributes }, encode),\n  };\n};\n\nexport default mapStateOnServer;\n", "import mapStateOnServer from './server';\n\nconst instances = [];\n\nexport function clearInstances() {\n  instances.length = 0;\n}\n\nexport default class HelmetData {\n  instances = [];\n\n  value = {\n    setHelmet: serverState => {\n      this.context.helmet = serverState;\n    },\n    helmetInstances: {\n      get: () => (this.canUseDOM ? instances : this.instances),\n      add: instance => {\n        (this.canUseDOM ? instances : this.instances).push(instance);\n      },\n      remove: instance => {\n        const index = (this.canUseDOM ? instances : this.instances).indexOf(instance);\n        (this.canUseDOM ? instances : this.instances).splice(index, 1);\n      },\n    },\n  };\n\n  constructor(context, canUseDOM = typeof document !== 'undefined') {\n    this.context = context;\n    this.canUseDOM = canUseDOM;\n\n    if (!canUseDOM) {\n      context.helmet = mapStateOnServer({\n        baseTag: [],\n        bodyAttributes: {},\n        encodeSpecialCharacters: true,\n        htmlAttributes: {},\n        linkTags: [],\n        metaTags: [],\n        noscriptTags: [],\n        scriptTags: [],\n        styleTags: [],\n        title: '',\n        titleAttributes: {},\n      });\n    }\n  }\n}\n", "import React, { Component } from 'react';\nimport PropTypes from 'prop-types';\nimport HelmetData from './HelmetData';\n\nconst defaultValue = {};\n\nexport const Context = React.createContext(defaultValue);\n\nexport const providerShape = PropTypes.shape({\n  setHelmet: PropTypes.func,\n  helmetInstances: PropTypes.shape({\n    get: PropTypes.func,\n    add: PropTypes.func,\n    remove: PropTypes.func,\n  }),\n});\n\nconst canUseDOM = typeof document !== 'undefined';\n\nexport default class Provider extends Component {\n  static canUseDOM = canUseDOM;\n\n  static propTypes = {\n    context: PropTypes.shape({\n      helmet: PropTypes.shape(),\n    }),\n    children: PropTypes.node.isRequired,\n  };\n\n  static defaultProps = {\n    context: {},\n  };\n\n  static displayName = 'HelmetProvider';\n\n  constructor(props) {\n    super(props);\n\n    this.helmetData = new HelmetData(this.props.context, Provider.canUseDOM);\n  }\n\n  render() {\n    return <Context.Provider value={this.helmetData.value}>{this.props.children}</Context.Provider>;\n  }\n}\n", "import { HELMET_ATTRIBUTE, TAG_NAMES, TAG_PROPERTIES } from './constants';\nimport { flattenArray } from './utils';\n\nconst updateTags = (type, tags) => {\n  const headElement = document.head || document.querySelector(TAG_NAMES.HEAD);\n  const tagNodes = headElement.querySelectorAll(`${type}[${HELMET_ATTRIBUTE}]`);\n  const oldTags = [].slice.call(tagNodes);\n  const newTags = [];\n  let indexToDelete;\n\n  if (tags && tags.length) {\n    tags.forEach(tag => {\n      const newElement = document.createElement(type);\n\n      // eslint-disable-next-line\n      for (const attribute in tag) {\n        if (Object.prototype.hasOwnProperty.call(tag, attribute)) {\n          if (attribute === TAG_PROPERTIES.INNER_HTML) {\n            newElement.innerHTML = tag.innerHTML;\n          } else if (attribute === TAG_PROPERTIES.CSS_TEXT) {\n            if (newElement.styleSheet) {\n              newElement.styleSheet.cssText = tag.cssText;\n            } else {\n              newElement.appendChild(document.createTextNode(tag.cssText));\n            }\n          } else {\n            const value = typeof tag[attribute] === 'undefined' ? '' : tag[attribute];\n            newElement.setAttribute(attribute, value);\n          }\n        }\n      }\n\n      newElement.setAttribute(HELMET_ATTRIBUTE, 'true');\n\n      // Remove a duplicate tag from domTagstoRemove, so it isn't cleared.\n      if (\n        oldTags.some((existingTag, index) => {\n          indexToDelete = index;\n          return newElement.isEqualNode(existingTag);\n        })\n      ) {\n        oldTags.splice(indexToDelete, 1);\n      } else {\n        newTags.push(newElement);\n      }\n    });\n  }\n\n  oldTags.forEach(tag => tag.parentNode.removeChild(tag));\n  newTags.forEach(tag => headElement.appendChild(tag));\n\n  return {\n    oldTags,\n    newTags,\n  };\n};\n\nconst updateAttributes = (tagName, attributes) => {\n  const elementTag = document.getElementsByTagName(tagName)[0];\n\n  if (!elementTag) {\n    return;\n  }\n\n  const helmetAttributeString = elementTag.getAttribute(HELMET_ATTRIBUTE);\n  const helmetAttributes = helmetAttributeString ? helmetAttributeString.split(',') : [];\n  const attributesToRemove = [].concat(helmetAttributes);\n  const attributeKeys = Object.keys(attributes);\n\n  for (let i = 0; i < attributeKeys.length; i += 1) {\n    const attribute = attributeKeys[i];\n    const value = attributes[attribute] || '';\n\n    if (elementTag.getAttribute(attribute) !== value) {\n      elementTag.setAttribute(attribute, value);\n    }\n\n    if (helmetAttributes.indexOf(attribute) === -1) {\n      helmetAttributes.push(attribute);\n    }\n\n    const indexToSave = attributesToRemove.indexOf(attribute);\n    if (indexToSave !== -1) {\n      attributesToRemove.splice(indexToSave, 1);\n    }\n  }\n\n  for (let i = attributesToRemove.length - 1; i >= 0; i -= 1) {\n    elementTag.removeAttribute(attributesToRemove[i]);\n  }\n\n  if (helmetAttributes.length === attributesToRemove.length) {\n    elementTag.removeAttribute(HELMET_ATTRIBUTE);\n  } else if (elementTag.getAttribute(HELMET_ATTRIBUTE) !== attributeKeys.join(',')) {\n    elementTag.setAttribute(HELMET_ATTRIBUTE, attributeKeys.join(','));\n  }\n};\n\nconst updateTitle = (title, attributes) => {\n  if (typeof title !== 'undefined' && document.title !== title) {\n    document.title = flattenArray(title);\n  }\n\n  updateAttributes(TAG_NAMES.TITLE, attributes);\n};\n\nconst commitTagChanges = (newState, cb) => {\n  const {\n    baseTag,\n    bodyAttributes,\n    htmlAttributes,\n    linkTags,\n    metaTags,\n    noscriptTags,\n    onChangeClientState,\n    scriptTags,\n    styleTags,\n    title,\n    titleAttributes,\n  } = newState;\n  updateAttributes(TAG_NAMES.BODY, bodyAttributes);\n  updateAttributes(TAG_NAMES.HTML, htmlAttributes);\n\n  updateTitle(title, titleAttributes);\n\n  const tagUpdates = {\n    baseTag: updateTags(TAG_NAMES.BASE, baseTag),\n    linkTags: updateTags(TAG_NAMES.LINK, linkTags),\n    metaTags: updateTags(TAG_NAMES.META, metaTags),\n    noscriptTags: updateTags(TAG_NAMES.NOSCRIPT, noscriptTags),\n    scriptTags: updateTags(TAG_NAMES.SCRIPT, scriptTags),\n    styleTags: updateTags(TAG_NAMES.STYLE, styleTags),\n  };\n\n  const addedTags = {};\n  const removedTags = {};\n\n  Object.keys(tagUpdates).forEach(tagType => {\n    const { newTags, oldTags } = tagUpdates[tagType];\n\n    if (newTags.length) {\n      addedTags[tagType] = newTags;\n    }\n    if (oldTags.length) {\n      removedTags[tagType] = tagUpdates[tagType].oldTags;\n    }\n  });\n\n  if (cb) {\n    cb();\n  }\n\n  onChangeClientState(newState, addedTags, removedTags);\n};\n\n// eslint-disable-next-line\nlet _helmetCallback = null;\n\nconst handleStateChangeOnClient = newState => {\n  if (_helmetCallback) {\n    cancelAnimationFrame(_helmetCallback);\n  }\n\n  if (newState.defer) {\n    _helmetCallback = requestAnimationFrame(() => {\n      commitTagChanges(newState, () => {\n        _helmetCallback = null;\n      });\n    });\n  } else {\n    commitTagChanges(newState);\n    _helmetCallback = null;\n  }\n};\n\nexport default handleStateChangeOnClient;\n", "import { Component } from 'react';\nimport shallowEqual from 'shallowequal';\nimport handleStateChangeOnClient from './client';\nimport mapStateOnServer from './server';\nimport { reducePropsToState } from './utils';\nimport Provider, { providerShape } from './Provider';\n\nexport default class Dispatcher extends Component {\n  static propTypes = {\n    context: providerShape.isRequired,\n  };\n\n  static displayName = 'HelmetDispatcher';\n\n  rendered = false;\n\n  shouldComponentUpdate(nextProps) {\n    return !shallowEqual(nextProps, this.props);\n  }\n\n  componentDidUpdate() {\n    this.emitChange();\n  }\n\n  componentWillUnmount() {\n    const { helmetInstances } = this.props.context;\n    helmetInstances.remove(this);\n    this.emitChange();\n  }\n\n  emitChange() {\n    const { helmetInstances, setHelmet } = this.props.context;\n    let serverState = null;\n    const state = reducePropsToState(\n      helmetInstances.get().map(instance => {\n        const props = { ...instance.props };\n        delete props.context;\n        return props;\n      })\n    );\n    if (Provider.canUseDOM) {\n      handleStateChangeOnClient(state);\n    } else if (mapStateOnServer) {\n      serverState = mapStateOnServer(state);\n    }\n    setHelmet(serverState);\n  }\n\n  // componentWillMount will be deprecated\n  // for SSR, initialize on first render\n  // constructor is also unsafe in StrictMode\n  init() {\n    if (this.rendered) {\n      return;\n    }\n\n    this.rendered = true;\n\n    const { helmetInstances } = this.props.context;\n    helmetInstances.add(this);\n    this.emitChange();\n  }\n\n  render() {\n    this.init();\n\n    return null;\n  }\n}\n", "import React, { Component } from 'react';\nimport PropTypes from 'prop-types';\nimport fastCompare from 'react-fast-compare';\nimport invariant from 'invariant';\nimport { Context } from './Provider';\nimport HelmetData from './HelmetData';\nimport Dispatcher from './Dispatcher';\nimport { without } from './utils';\nimport { TAG_NAMES, VALID_TAG_NAMES, HTML_TAG_MAP } from './constants';\n\nexport { default as HelmetData } from './HelmetData';\nexport { default as HelmetProvider } from './Provider';\n\n/* eslint-disable class-methods-use-this */\nexport class Helmet extends Component {\n  /**\n   * @param {Object} base: {\"target\": \"_blank\", \"href\": \"http://mysite.com/\"}\n   * @param {Object} bodyAttributes: {\"className\": \"root\"}\n   * @param {String} defaultTitle: \"Default Title\"\n   * @param {Boolean} defer: true\n   * @param {Boolean} encodeSpecialCharacters: true\n   * @param {Object} htmlAttributes: {\"lang\": \"en\", \"amp\": undefined}\n   * @param {Array} link: [{\"rel\": \"canonical\", \"href\": \"http://mysite.com/example\"}]\n   * @param {Array} meta: [{\"name\": \"description\", \"content\": \"Test description\"}]\n   * @param {Array} noscript: [{\"innerHTML\": \"<img src='http://mysite.com/js/test.js'\"}]\n   * @param {Function} onChangeClientState: \"(newState) => console.log(newState)\"\n   * @param {Array} script: [{\"type\": \"text/javascript\", \"src\": \"http://mysite.com/js/test.js\"}]\n   * @param {Array} style: [{\"type\": \"text/css\", \"cssText\": \"div { display: block; color: blue; }\"}]\n   * @param {String} title: \"Title\"\n   * @param {Object} titleAttributes: {\"itemprop\": \"name\"}\n   * @param {String} titleTemplate: \"MySite.com - %s\"\n   * @param {Boolean} prioritizeSeoTags: false\n   */\n  /* eslint-disable react/forbid-prop-types, react/require-default-props */\n  static propTypes = {\n    base: PropTypes.object,\n    bodyAttributes: PropTypes.object,\n    children: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.node), PropTypes.node]),\n    defaultTitle: PropTypes.string,\n    defer: PropTypes.bool,\n    encodeSpecialCharacters: PropTypes.bool,\n    htmlAttributes: PropTypes.object,\n    link: PropTypes.arrayOf(PropTypes.object),\n    meta: PropTypes.arrayOf(PropTypes.object),\n    noscript: PropTypes.arrayOf(PropTypes.object),\n    onChangeClientState: PropTypes.func,\n    script: PropTypes.arrayOf(PropTypes.object),\n    style: PropTypes.arrayOf(PropTypes.object),\n    title: PropTypes.string,\n    titleAttributes: PropTypes.object,\n    titleTemplate: PropTypes.string,\n    prioritizeSeoTags: PropTypes.bool,\n    helmetData: PropTypes.object,\n  };\n  /* eslint-enable react/prop-types, react/forbid-prop-types, react/require-default-props */\n\n  static defaultProps = {\n    defer: true,\n    encodeSpecialCharacters: true,\n    prioritizeSeoTags: false,\n  };\n\n  static displayName = 'Helmet';\n\n  shouldComponentUpdate(nextProps) {\n    return !fastCompare(without(this.props, 'helmetData'), without(nextProps, 'helmetData'));\n  }\n\n  mapNestedChildrenToProps(child, nestedChildren) {\n    if (!nestedChildren) {\n      return null;\n    }\n\n    switch (child.type) {\n      case TAG_NAMES.SCRIPT:\n      case TAG_NAMES.NOSCRIPT:\n        return {\n          innerHTML: nestedChildren,\n        };\n\n      case TAG_NAMES.STYLE:\n        return {\n          cssText: nestedChildren,\n        };\n      default:\n        throw new Error(\n          `<${child.type} /> elements are self-closing and can not contain children. Refer to our API for more information.`\n        );\n    }\n  }\n\n  flattenArrayTypeChildren({ child, arrayTypeChildren, newChildProps, nestedChildren }) {\n    return {\n      ...arrayTypeChildren,\n      [child.type]: [\n        ...(arrayTypeChildren[child.type] || []),\n        {\n          ...newChildProps,\n          ...this.mapNestedChildrenToProps(child, nestedChildren),\n        },\n      ],\n    };\n  }\n\n  mapObjectTypeChildren({ child, newProps, newChildProps, nestedChildren }) {\n    switch (child.type) {\n      case TAG_NAMES.TITLE:\n        return {\n          ...newProps,\n          [child.type]: nestedChildren,\n          titleAttributes: { ...newChildProps },\n        };\n\n      case TAG_NAMES.BODY:\n        return {\n          ...newProps,\n          bodyAttributes: { ...newChildProps },\n        };\n\n      case TAG_NAMES.HTML:\n        return {\n          ...newProps,\n          htmlAttributes: { ...newChildProps },\n        };\n      default:\n        return {\n          ...newProps,\n          [child.type]: { ...newChildProps },\n        };\n    }\n  }\n\n  mapArrayTypeChildrenToProps(arrayTypeChildren, newProps) {\n    let newFlattenedProps = { ...newProps };\n\n    Object.keys(arrayTypeChildren).forEach(arrayChildName => {\n      newFlattenedProps = {\n        ...newFlattenedProps,\n        [arrayChildName]: arrayTypeChildren[arrayChildName],\n      };\n    });\n\n    return newFlattenedProps;\n  }\n\n  warnOnInvalidChildren(child, nestedChildren) {\n    invariant(\n      VALID_TAG_NAMES.some(name => child.type === name),\n      typeof child.type === 'function'\n        ? `You may be attempting to nest <Helmet> components within each other, which is not allowed. Refer to our API for more information.`\n        : `Only elements types ${VALID_TAG_NAMES.join(\n            ', '\n          )} are allowed. Helmet does not support rendering <${\n            child.type\n          }> elements. Refer to our API for more information.`\n    );\n\n    invariant(\n      !nestedChildren ||\n        typeof nestedChildren === 'string' ||\n        (Array.isArray(nestedChildren) &&\n          !nestedChildren.some(nestedChild => typeof nestedChild !== 'string')),\n      `Helmet expects a string as a child of <${child.type}>. Did you forget to wrap your children in braces? ( <${child.type}>{\\`\\`}</${child.type}> ) Refer to our API for more information.`\n    );\n\n    return true;\n  }\n\n  mapChildrenToProps(children, newProps) {\n    let arrayTypeChildren = {};\n\n    React.Children.forEach(children, child => {\n      if (!child || !child.props) {\n        return;\n      }\n\n      const { children: nestedChildren, ...childProps } = child.props;\n      // convert React props to HTML attributes\n      const newChildProps = Object.keys(childProps).reduce((obj, key) => {\n        obj[HTML_TAG_MAP[key] || key] = childProps[key];\n        return obj;\n      }, {});\n\n      let { type } = child;\n      if (typeof type === 'symbol') {\n        type = type.toString();\n      } else {\n        this.warnOnInvalidChildren(child, nestedChildren);\n      }\n\n      switch (type) {\n        case TAG_NAMES.FRAGMENT:\n          newProps = this.mapChildrenToProps(nestedChildren, newProps);\n          break;\n\n        case TAG_NAMES.LINK:\n        case TAG_NAMES.META:\n        case TAG_NAMES.NOSCRIPT:\n        case TAG_NAMES.SCRIPT:\n        case TAG_NAMES.STYLE:\n          arrayTypeChildren = this.flattenArrayTypeChildren({\n            child,\n            arrayTypeChildren,\n            newChildProps,\n            nestedChildren,\n          });\n          break;\n\n        default:\n          newProps = this.mapObjectTypeChildren({\n            child,\n            newProps,\n            newChildProps,\n            nestedChildren,\n          });\n          break;\n      }\n    });\n\n    return this.mapArrayTypeChildrenToProps(arrayTypeChildren, newProps);\n  }\n\n  render() {\n    const { children, ...props } = this.props;\n    let newProps = { ...props };\n    let { helmetData } = props;\n\n    if (children) {\n      newProps = this.mapChildrenToProps(children, newProps);\n    }\n\n    if (helmetData && !(helmetData instanceof HelmetData)) {\n      helmetData = new HelmetData(helmetData.context, helmetData.instances);\n    }\n\n    return helmetData ? (\n      // eslint-disable-next-line react/jsx-props-no-spreading\n      <Dispatcher {...newProps} context={helmetData.value} helmetData={undefined} />\n    ) : (\n      <Context.Consumer>\n        {(\n          context // eslint-disable-next-line react/jsx-props-no-spreading\n        ) => <Dispatcher {...newProps} context={context} />}\n      </Context.Consumer>\n    );\n  }\n}\n"], "names": ["TAG_PROPERTIES", "TAG_NAMES", "BASE", "BODY", "HEAD", "HTML", "LINK", "META", "NOSCRIPT", "SCRIPT", "STYLE", "TITLE", "FRAGMENT", "SEO_PRIORITY_TAGS", "rel", "type", "charset", "name", "property", "VALID_TAG_NAMES", "Object", "keys", "map", "REACT_TAG_MAP", "accesskey", "class", "contenteditable", "contextmenu", "itemprop", "tabindex", "HTML_TAG_MAP", "reduce", "obj", "key", "getInnermostProperty", "propsList", "i", "length", "props", "prototype", "hasOwnProperty", "call", "getTitleFromPropsList", "innermostTitle", "innermostTemplate", "Array", "isArray", "join", "replace", "innermostDefaultTitle", "undefined", "getOnChangeClientState", "getAttributesFromPropsList", "tagType", "filter", "tagAttrs", "current", "getBaseTagFromPropsList", "primaryAttributes", "reverse", "innermostBaseTag", "tag", "lowerCaseAttributeKey", "toLowerCase", "indexOf", "concat", "getTagsFromPropsList", "tagName", "approvedSeenTags", "console", "warn", "approvedTags", "instanceTags", "instanceSeenTags", "primaryAttributeKey", "<PERSON><PERSON><PERSON>", "value", "for<PERSON>ach", "push", "tagUnion", "getAnyTrueFromPropsList", "checkedTag", "index", "flattenArray", "possible<PERSON><PERSON>y", "prioritizer", "elementsList", "propsToMatch", "acc", "elementAttrs", "toMatch", "includes", "checkIfPropsMatch", "priority", "default", "without", "_extends2", "SELF_CLOSING_TAGS", "encodeSpecialCharacters", "str", "encode", "String", "generateElementAttributesAsString", "attributes", "attr", "convertElementAttributesToReactProps", "initProps", "generateTagsAsReactComponent", "tags", "_mappedTag", "mappedTag", "attribute", "mappedAttribute", "dangerouslySetInnerHTML", "__html", "innerHTML", "cssText", "React", "createElement", "getMethodsForTag", "toComponent", "generateTitleAsReactComponent", "titleAttributes", "title", "_initProps", "toString", "attributeString", "flattenedTitle", "generateTitleAsString", "generateTagsAsString", "attributeHtml", "string", "tagContent", "isSelfClosing", "mapStateOnServer", "baseTag", "bodyAttributes", "htmlAttributes", "noscriptTags", "styleTags", "_props$title", "linkTags", "metaTags", "scriptTags", "priorityMethods", "prioritizeSeoTags", "_getPriorityMethods", "_ref", "meta", "link", "script", "getPriorityMethods", "base", "noscript", "style", "instances", "HelmetData", "context", "canUseDOM", "_this", "this", "document", "setHelmet", "serverState", "helmet", "helmetInstances", "get", "add", "instance", "remove", "splice", "Context", "createContext", "providerShape", "PropTypes", "shape", "func", "Provider", "helmetData", "render", "children", "Component", "propTypes", "node", "isRequired", "defaultProps", "displayName", "updateTags", "indexToDelete", "headElement", "head", "querySelector", "tagNodes", "querySelectorAll", "oldTags", "slice", "newTags", "newElement", "styleSheet", "append<PERSON><PERSON><PERSON>", "createTextNode", "setAttribute", "some", "existingTag", "isEqualNode", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "updateAttributes", "elementTag", "getElementsByTagName", "helmetAttributeString", "getAttribute", "helmetAttributes", "split", "attributesToRemove", "<PERSON><PERSON><PERSON><PERSON>", "indexToSave", "removeAttribute", "commitTagChanges", "newState", "cb", "onChangeClientState", "updateTitle", "tagUpdates", "addedTags", "removedTags", "_tagUpdates$tagType", "_helmet<PERSON><PERSON><PERSON>", "Di<PERSON>atcher", "rendered", "shouldComponentUpdate", "nextProps", "shallowEqual", "componentDidUpdate", "emitChange", "componentWillUnmount", "_this$props$context", "state", "_extends", "defer", "cancelAnimationFrame", "requestAnimationFrame", "init", "<PERSON><PERSON><PERSON>", "fastCompare", "mapNestedChildrenToProps", "child", "nested<PERSON><PERSON><PERSON><PERSON>", "Error", "flattenArrayTypeChildren", "arrayTypeChildren", "newChildProps", "_proto", "mapObjectTypeChildren", "_ref2", "_extends3", "_extends4", "newProps", "mapArrayTypeChildrenToProps", "newFlattenedProps", "arrayChildName", "_extends5", "warnOnInvalidChildren", "invariant", "nested<PERSON><PERSON><PERSON>", "mapChildrenToProps", "Children", "_child$props", "childProps", "_objectWithoutPropertiesLoose", "_excluded", "_this$props", "_excluded2", "Consumer", "object", "oneOfType", "arrayOf", "defaultTitle", "bool", "titleTemplate"], "mappings": "yzBAAaA,IAmBAC,EAAY,CACvBC,KAAM,OACNC,KAAM,OACNC,KAAM,OACNC,KAAM,OACNC,KAAM,OACNC,KAAM,OACNC,SAAU,WACVC,OAAQ,SACRC,MAAO,QACPC,MAAO,QACPC,SAAU,0BAGCC,EACL,CAAEC,IAAK,CAAC,UAAW,YAAa,cAD3BD,EAEH,CAAEE,KAAM,CAAC,wBAFNF,EAGL,CACJG,QAAS,GACTC,KAAM,CAAC,SAAU,eACjBC,SAAU,CACR,UACA,WACA,SACA,WACA,eACA,iBACA,cACA,gBACA,sBACA,gBACA,oBACA,eACA,iBAKOC,EAAkBC,OAAOC,KAAKpB,GAAWqB,IAAI,SAAAL,GAAQhB,OAAAA,EAAUgB,KAE/DM,EAAgB,CAC3BC,UAAW,YACXR,QAAS,UACTS,MAAO,YACPC,gBAAiB,kBACjBC,YAAa,cACb,aAAc,YACdC,SAAU,WACVC,SAAU,YAGCC,EAAeV,OAAOC,KAAKE,GAAeQ,OAAO,SAACC,EAAKC,GAElE,OADAD,EAAIT,EAAcU,IAAQA,EACnBD,GACN,IC9DGE,EAAuB,SAACC,EAAWjB,GACvC,IAAK,IAAIkB,EAAID,EAAUE,OAAS,EAAGD,GAAK,EAAGA,GAAK,EAAG,CACjD,IAAME,EAAQH,EAAUC,GAExB,GAAIhB,OAAOmB,UAAUC,eAAeC,KAAKH,EAAOpB,GAC9C,OAAOoB,EAAMpB,GAIjB,OAAO,MAGHwB,EAAwB,SAAAP,GAC5B,IAAIQ,EAAiBT,EAAqBC,EAAWlC,EAAUU,OACzDiC,EAAoBV,EAAqBC,EAlB/B,iBAsBhB,GAHIU,MAAMC,QAAQH,KAChBA,EAAiBA,EAAeI,KAAK,KAEnCH,GAAqBD,EAEvB,OAAOC,EAAkBI,QAAQ,MAAO,WAAA,OAAML,IAGhD,IAAMM,EAAwBf,EAAqBC,EA/BpC,gBAiCf,OAAOQ,GAAkBM,QAAyBC,GAG9CC,EAAyB,SAAAhB,GAC7BD,OAAAA,EAAqBC,EAlCG,wBAkCiD,cAErEiB,EAA6B,SAACC,EAASlB,GAC3CA,OAAAA,EACGmB,OAAO,SAAAhB,GAAS,YAA0B,IAAnBA,EAAMe,KAC7B/B,IAAI,SAAAgB,UAASA,EAAMe,KACnBtB,OAAO,SAACwB,EAAUC,GAAkBD,OAAAA,EAAAA,GAAAA,EAAaC,IAAY,KAE5DC,EAA0B,SAACC,EAAmBvB,GAApB,OAC9BA,EACGmB,OAAO,SAAAhB,eAA0C,IAA1BA,EAAMrC,EAAUC,QACvCoB,IAAI,SAAAgB,GAASA,OAAAA,EAAMrC,EAAUC,QAC7ByD,UACA5B,OAAO,SAAC6B,EAAkBC,GACzB,IAAKD,EAAiBvB,OAGpB,IAFA,IAAMhB,EAAOD,OAAOC,KAAKwC,GAEhBzB,EAAI,EAAGA,EAAIf,EAAKgB,OAAQD,GAAK,EAAG,CACvC,IACM0B,EADezC,EAAKe,GACiB2B,cAE3C,IACwD,IAAtDL,EAAkBM,QAAQF,IAC1BD,EAAIC,GAEJ,OAAOF,EAAiBK,OAAOJ,GAKrC,OAAOD,GACN,KAKDM,EAAuB,SAACC,EAAST,EAAmBvB,GAExD,IAAMiC,EAAmB,GAEzB,OAAOjC,EACJmB,OAAO,SAAAhB,GACN,QAAIO,MAAMC,QAAQR,EAAM6B,WAGM,IAAnB7B,EAAM6B,IAXHE,SAAmC,mBAAjBA,QAAQC,MAAuBD,QAAQC,KAYjE,WACSH,EAA0D,0DAAO7B,EAC1E6B,cAMP7C,IAAI,SAAAgB,GAAK,OAAIA,EAAM6B,KACnBR,UACA5B,OAAO,SAACwC,EAAcC,GACrB,IAAMC,EAAmB,GAEzBD,EACGlB,OAAO,SAAAO,GAGN,IAFA,IAAIa,EACErD,EAAOD,OAAOC,KAAKwC,GAChBzB,EAAI,EAAGA,EAAIf,EAAKgB,OAAQD,GAAK,EAAG,CACvC,IAAMuC,EAAetD,EAAKe,GACpB0B,EAAwBa,EAAaZ,eAIa,IAAtDL,EAAkBM,QAAQF,IDpGjC,QCsGSY,GAC2C,cAA3Cb,EAAIa,GAAqBX,eDvGlC,QC0GSD,GAC6C,eAA7CD,EAAIC,GAAuBC,gBAG7BW,EAAsBZ,IAIuB,IAA7CJ,EAAkBM,QAAQW,IDtH1B,cCuHCA,GD1HH,YC2HIA,GDvHH,aCwHGA,IAEFD,EAAsBC,GAI1B,IAAKD,IAAwBb,EAAIa,GAC/B,OAAO,EAGT,IAAME,EAAQf,EAAIa,GAAqBX,cAUvC,OARKK,EAAiBM,KACpBN,EAAiBM,GAAuB,IAGrCD,EAAiBC,KACpBD,EAAiBC,GAAuB,KAGrCN,EAAiBM,GAAqBE,KACzCH,EAAiBC,GAAqBE,IAAS,GAEhD,KAIFjB,UACAkB,QAAQ,SAAAhB,GAAG,OAAIU,EAAaO,KAAKjB,KAIpC,IADA,IAAMxC,EAAOD,OAAOC,KAAKoD,GAChBrC,EAAI,EAAGA,EAAIf,EAAKgB,OAAQD,GAAK,EAAG,CACvC,IAAMuC,EAAetD,EAAKe,GACpB2C,EACDX,EAAAA,GAAAA,EAAiBO,GACjBF,EAAiBE,IAGtBP,EAAiBO,GAAgBI,EAGnC,OAAOR,GACN,IACFZ,WAGCqB,EAA0B,SAAC7C,EAAW8C,GAC1C,GAAIpC,MAAMC,QAAQX,IAAcA,EAAUE,OACxC,IAAK,IAAI6C,EAAQ,EAAGA,EAAQ/C,EAAUE,OAAQ6C,GAAS,EAErD,GADa/C,EAAU+C,GACdD,GACP,SAIN,OAAO,GAsCIE,EAAe,SAAAC,GAC1BvC,OAAAA,MAAMC,QAAQsC,GAAiBA,EAAcrC,KAAK,IAAMqC,GAe7CC,EAAc,SAACC,EAAcC,GACxC,OAAI1C,MAAMC,QAAQwC,GACTA,EAAavD,OAClB,SAACyD,EAAKC,GAMJ,OApBkB,SAACnD,EAAOoD,GAEhC,IADA,IAAMrE,EAAOD,OAAOC,KAAKiB,GAChBF,EAAI,EAAGA,EAAIf,EAAKgB,OAAQD,GAAK,EAEpC,GAAIsD,EAAQrE,EAAKe,KAAOsD,EAAQrE,EAAKe,IAAIuD,SAASrD,EAAMjB,EAAKe,KAC3D,SAGJ,OACD,EAMWwD,CAAkBH,EAAcF,GAClCC,EAAIK,SAASf,KAAKW,GAElBD,EAAA,QAAYV,KAAKW,GAEZD,GAET,CAAEK,SAAU,GAAIC,QAAS,KAGtB,CAAEA,QAASR,IAGPS,EAAU,SAAC/D,EAAKC,GAAQ,IAAA+D,EACnC,YACKhE,IADLgE,EAAA,IAEG/D,QAAMiB,OCrPL+C,EAAoB,CAAChG,EAAUO,SAAUP,EAAUQ,OAAQR,EAAUS,OAErEwF,EAA0B,SAACC,EAAKC,GACpC,YADoCA,IAAAA,IAAAA,GAAS,IAC9B,IAAXA,EACKC,OAAOF,GAGTE,OAAOF,GACXnD,QAAQ,KAAM,SACdA,QAAQ,KAAM,QACdA,QAAQ,KAAM,QACdA,QAAQ,KAAM,UACdA,QAAQ,KAAM,WAGbsD,EAAoC,SAAAC,GAAU,OAClDnF,OAAOC,KAAKkF,GAAYxE,OAAO,SAACoE,EAAKlE,GACnC,IAAMuE,OAAkC,IAApBD,EAAWtE,GAA0BA,EAA5C,KAAoDsE,EAAWtE,GAA/D,IAAA,GAA4EA,EACzF,OAAOkE,EAASA,EAAN,IAAaK,EAASA,GAC/B,KAwCCC,EAAuC,SAACF,EAAYG,GAAb,YAAA,IAAaA,IAAAA,EAAY,IACpEtF,OAAOC,KAAKkF,GAAYxE,OAAO,SAACC,EAAKC,GAEnC,OADAD,EAAIT,EAAcU,IAAQA,GAAOsE,EAAWtE,GACrCD,GACN0E,IAaCC,EAA+B,SAAC5F,EAAM6F,GAAP,OACnCA,EAAKtF,IAAI,SAACuC,EAAKzB,GACb,IAAAyE,EAAMC,OACJ7E,IAAKG,IFfqB,YEgBN,EAFtByE,GAmBA,OAdAzF,OAAOC,KAAKwC,GAAKgB,QAAQ,SAAAkC,GACvB,IAAMC,EAAkBzF,EAAcwF,IAAcA,EF1F5C,cE6FNC,GFhGI,YEiGJA,EAGAF,EAAUG,wBAA0B,CAAEC,OADtBrD,EAAIsD,WAAatD,EAAIuD,SAGrCN,EAAUE,GAAmBnD,EAAIkD,KAI9BM,EAAAA,QAAMC,cAAcvG,EAAM+F,MAG/BS,EAAmB,SAACxG,EAAM6F,EAAMR,GACpC,OAAQrF,GACN,KAAKd,EAAUU,MACb,MAAO,CACL6G,YAAa,WACXC,OAxC0ClB,EAwCMK,EAAKc,iBArC3DzF,EAAAA,CAAAA,IAHyC0F,EAwCCf,EAAKe,QFzCnB,YEKR,EAEhBrF,EAAQmE,EAAqCF,EAJnDqB,GAMO,CAACP,UAAMC,cAAcrH,EAAUU,MAAO2B,EAAOqF,IARhB,IAAOA,EAAOpB,EAElDqB,EAIMtF,GAmCAuF,SAAU,WAAA,OArFY,SAAC9G,EAAM4G,EAAOpB,EAAYH,GACtD,IAAM0B,EAAkBxB,EAAkCC,GACpDwB,EAAiB5C,EAAawC,GACpC,OAAOG,EACC/G,IAAAA,EAAAA,mBAAmC+G,EADrB,IACwC5B,EACxD6B,EACA3B,GAHgB,KAIZrF,EAJY,IAAA,IAKdA,EALc,mBAKqBmF,EACrC6B,EACA3B,QACIrF,EARY,IAkFAiH,CAAsBjH,EAAM6F,EAAKe,MAAOf,EAAKc,gBAAiBtB,KAElF,IFzGI,iBE0GJ,IFzGI,iBE0GF,MAAO,CACLoB,YAAa,WAAMf,OAAAA,EAAqCG,IACxDiB,SAAU,WAAA,OAAMvB,EAAkCM,KAEtD,QACE,MAAO,CACLY,YAAa,WAAA,OAAMb,EAA6B5F,EAAM6F,IACtDiB,SAAU,WAAMI,OAlFK,SAAClH,EAAM6F,EAAMR,GAAb,OAC3BQ,EAAK7E,OAAO,SAACoE,EAAKtC,GAChB,IAAMqE,EAAgB9G,OAAOC,KAAKwC,GAC/BP,OACC,SAAAyD,GAAS,QF7CH,cE8CFA,GFjDA,YEiD2CA,KAEhDhF,OAAO,SAACoG,EAAQpB,GACf,IAAMP,OACsB,IAAnB3C,EAAIkD,GACPA,EACGA,EAFP,KAEqBb,EAAwBrC,EAAIkD,GAAYX,GAF7D,IAGF,OAAO+B,EAAYA,EAAN,IAAgB3B,EAASA,GACrC,IAEC4B,EAAavE,EAAIsD,WAAatD,EAAIuD,SAAW,GAE7CiB,GAAqD,IAArCpC,EAAkBjC,QAAQjD,GAEhD,OAAUoF,EAAOpF,IAAAA,EAAPoF,mBAA0C+B,GAClDG,EAA2BD,KAAAA,IAAAA,EAAerH,KAAAA,QAE3C,IA4DmBkH,CAAqBlH,EAAM6F,EAAMR,OAkCnDkC,EAAmB,SAAAhG,GACvB,IACEiG,EASEjG,EATFiG,QACAC,EAQElG,EARFkG,eACApC,EAOE9D,EAPF8D,OACAqC,EAMEnG,EANFmG,eACAC,EAKEpG,EALFoG,aACAC,EAIErG,EAJFqG,UANFC,EAUItG,EAHFqF,MAAAA,OAAQ,IAAAiB,EAAA,KACRlB,EAEEpF,EAFFoF,gBAGImB,EAAmCvG,EAAnCuG,SAAUC,EAAyBxG,EAAzBwG,SAAUC,EAAezG,EAAfyG,WACtBC,EAAkB,CACpBxB,YAAa,aACbK,SAAU,WAAA,MAAM,KAElB,GANIvF,EADF2G,kBAOqB,CAAA,IAAAC,EA9CE,SAAgDC,GAAA,IAAnCN,EAAmCM,EAAnCN,SAAUE,EAAAA,EAAAA,WAAY3C,EAAa+C,EAAb/C,OACtDgD,EAAO/D,IADeyD,SACOjI,GAC7BwI,EAAOhE,EAAYwD,EAAUhI,GAC7ByI,EAASjE,EAAY0D,EAAYlI,GAkBvC,MAAO,CACLmI,gBAhBsB,CACtBxB,YAAa,WAAA,MAAA,GAAAvD,OACR0C,EAA6B1G,EAAUM,KAAM6I,EAAKvD,UAClDc,EAA6B1G,EAAUK,KAAM+I,EAAKxD,UAClDc,EAA6B1G,EAAUQ,OAAQ6I,EAAOzD,YAE3DgC,SAAU,WAELN,OAAAA,EAAiBtH,EAAUM,KAAM6I,EAAKvD,SAAUO,GAF3C,IAEsDmB,EAC5DtH,EAAUK,KACV+I,EAAKxD,SACLO,OACGmB,EAAiBtH,EAAUQ,OAAQ6I,EAAOzD,SAAUO,KAK3D0C,SAAUM,EAAI,QACdP,SAAUQ,EAAI,QACdN,WAAYO,EAAM,SAsBqCC,CAAmBjH,GAAvE0G,EADkBE,EAClBF,gBAAiBH,EADCK,EACDL,SAAUC,EAAAA,EAAAA,SAAUC,EADnBG,EACmBH,WAE1C,MAAO,CACLlD,SAAUmD,EACVQ,KAAMjC,EAAiBtH,EAAUC,KAAMqI,EAASnC,GAChDoC,eAAgBjB,EF3KZ,iBE2KmDiB,EAAgBpC,GACvEqC,eAAgBlB,EF3KZ,iBE2KmDkB,EAAgBrC,GACvEiD,KAAM9B,EAAiBtH,EAAUK,KAAMuI,EAAUzC,GACjDgD,KAAM7B,EAAiBtH,EAAUM,KAAMuI,EAAU1C,GACjDqD,SAAUlC,EAAiBtH,EAAUO,SAAUkI,EAActC,GAC7DkD,OAAQ/B,EAAiBtH,EAAUQ,OAAQsI,EAAY3C,GACvDsD,MAAOnC,EAAiBtH,EAAUS,MAAOiI,EAAWvC,GACpDuB,MAAOJ,EAAiBtH,EAAUU,MAAO,CAAEgH,MAAAA,EAAOD,gBAAAA,GAAmBtB,KC9LnEuD,EAAY,GAMGC,EAmBnB,SAAYC,EAASC,GAA6C,IAAAC,EAAAC,UAA7CF,IAAAA,IAAAA,EAAgC,oBAAbG,UAA0BD,KAlBlEL,UAAY,GAkBsDK,KAhBlEpF,MAAQ,CACNsF,UAAW,SAAAC,GACTJ,EAAKF,QAAQO,OAASD,GAExBE,gBAAiB,CACfC,IAAK,WAAA,OAAOP,EAAKD,UAAYH,EAAYI,EAAKJ,WAC9CY,IAAK,SAAAC,IACFT,EAAKD,UAAYH,EAAYI,EAAKJ,WAAW7E,KAAK0F,IAErDC,OAAQ,SAAAD,GACN,IAAMtF,GAAS6E,EAAKD,UAAYH,EAAYI,EAAKJ,WAAW3F,QAAQwG,IACnET,EAAKD,UAAYH,EAAYI,EAAKJ,WAAWe,OAAOxF,EAAO,MAMhE8E,KAAKH,QAAUA,EACfG,KAAKF,UAAYA,EAEZA,IACHD,EAAQO,OAAS9B,EAAiB,CAChCC,QAAS,GACTC,eAAgB,GAChBtC,yBAAyB,EACzBuC,eAAgB,GAChBI,SAAU,GACVC,SAAU,GACVJ,aAAc,GACdK,WAAY,GACZJ,UAAW,GACXhB,MAAO,GACPD,gBAAiB,OCrCZiD,EAAUtD,EAAAA,QAAMuD,cAFR,IAIRC,EAAgBC,EAAS,QAACC,MAAM,CAC3Cb,UAAWY,EAAS,QAACE,KACrBX,gBAAiBS,EAAAA,QAAUC,MAAM,CAC/BT,IAAKQ,EAAAA,QAAUE,KACfT,IAAKO,EAAS,QAACE,KACfP,OAAQK,EAAAA,QAAUE,SAIhBlB,EAAgC,oBAAbG,SAEJgB,2BAgBnB,SAAY3I,EAAAA,GAAO,IAAAyH,EAAA,OACjBA,cAAMzH,IAAN0H,MAEKkB,WAAa,IAAItB,EAAWG,EAAKzH,MAAMuH,QAASoB,EAASnB,WAH7CC,4BAMnBoB,OAAA,wBACE,OAAO9D,wBAACsD,EAAQM,SAAS,CAAArG,MAAOoF,KAAKkB,WAAWtG,OAAQoF,KAAK1H,MAAM8I,cAvBjCC,EAAAA,WAAjBJ,EACZnB,UAAYA,EADAmB,EAGZK,UAAY,CACjBzB,QAASiB,EAAS,QAACC,MAAM,CACvBX,OAAQU,EAAS,QAACC,UAEpBK,SAAUN,EAAS,QAACS,KAAKC,YAPRP,EAUZQ,aAAe,CACpB5B,QAAS,IAXQoB,EAcZS,YAAc,iBC9BvB,IAAMC,EAAa,SAAC5K,EAAM6F,GACxB,IAIIgF,EAJEC,EAAc5B,SAAS6B,MAAQ7B,SAAS8B,cAAc9L,EAAUG,MAChE4L,EAAWH,EAAYI,iBAAoBlL,EAAAA,aAC3CmL,EAAU,GAAGC,MAAM1J,KAAKuJ,GACxBI,EAAU,GA4ChB,OAzCIxF,GAAQA,EAAKvE,QACfuE,EAAK/B,QAAQ,SAAAhB,GACX,IAAMwI,EAAapC,SAAS3C,cAAcvG,GAG1C,IAAK,IAAMgG,KAAalD,EAClBzC,OAAOmB,UAAUC,eAAeC,KAAKoB,EAAKkD,KLXxC,cKYAA,EACFsF,EAAWlF,UAAYtD,EAAIsD,ULhB3B,YKiBSJ,EACLsF,EAAWC,WACbD,EAAWC,WAAWlF,QAAUvD,EAAIuD,QAEpCiF,EAAWE,YAAYtC,SAASuC,eAAe3I,EAAIuD,UAIrDiF,EAAWI,aAAa1F,OADgB,IAAnBlD,EAAIkD,GAA6B,GAAKlD,EAAIkD,KAMrEsF,EAAWI,aL2Ce,UK3CgB,QAIxCP,EAAQQ,KAAK,SAACC,EAAazH,GAEzB,OADA0G,EAAgB1G,EACTmH,EAAWO,YAAYD,KAGhCT,EAAQxB,OAAOkB,EAAe,GAE9BQ,EAAQtH,KAAKuH,KAKnBH,EAAQrH,QAAQ,SAAAhB,GAAOA,OAAAA,EAAIgJ,WAAWC,YAAYjJ,KAClDuI,EAAQvH,QAAQ,SAAAhB,GAAOgI,OAAAA,EAAYU,YAAY1I,KAExC,CACLqI,QAAAA,EACAE,QAAAA,IAIEW,EAAmB,SAAC5I,EAASoC,GACjC,IAAMyG,EAAa/C,SAASgD,qBAAqB9I,GAAS,GAE1D,GAAK6I,EAAL,CASA,IALA,IAAME,EAAwBF,EAAWG,aLWX,WKVxBC,EAAmBF,EAAwBA,EAAsBG,MAAM,KAAO,GAC9EC,EAAqB,GAAGrJ,OAAOmJ,GAC/BG,EAAgBnM,OAAOC,KAAKkF,GAEzBnE,EAAI,EAAGA,EAAImL,EAAclL,OAAQD,GAAK,EAAG,CAChD,IAAM2E,EAAYwG,EAAcnL,GAC1BwC,EAAQ2B,EAAWQ,IAAc,GAEnCiG,EAAWG,aAAapG,KAAenC,GACzCoI,EAAWP,aAAa1F,EAAWnC,IAGQ,IAAzCwI,EAAiBpJ,QAAQ+C,IAC3BqG,EAAiBtI,KAAKiC,GAGxB,IAAMyG,EAAcF,EAAmBtJ,QAAQ+C,IAC1B,IAAjByG,GACFF,EAAmB5C,OAAO8C,EAAa,GAI3C,IAAK,IAAIpL,EAAIkL,EAAmBjL,OAAS,EAAGD,GAAK,EAAGA,GAAK,EACvD4K,EAAWS,gBAAgBH,EAAmBlL,IAG5CgL,EAAiB/K,SAAWiL,EAAmBjL,OACjD2K,EAAWS,gBLjBiB,WKkBnBT,EAAWG,aLlBQ,aKkB2BI,EAAcxK,KAAK,MAC1EiK,EAAWP,aLnBiB,UKmBcc,EAAcxK,KAAK,QAY3D2K,EAAmB,SAACC,EAAUC,GAClC,IACErF,EAWEoF,EAXFpF,QAEAE,EASEkF,EATFlF,eACAI,EAQE8E,EARF9E,SACAC,EAOE6E,EAPF7E,SACAJ,EAMEiF,EANFjF,aACAmF,EAKEF,EALFE,oBACA9E,EAIE4E,EAJF5E,WACAJ,EAGEgF,EAHFhF,UACAhB,EAEEgG,EAFFhG,MACAD,EACEiG,EADFjG,gBAEFqF,EAAiB9M,EAAUE,KADvBwN,EAVFnF,gBAYFuE,EAAiB9M,EAAUI,KAAMoI,GAvBf,SAACd,EAAOpB,QACL,IAAVoB,GAAyBsC,SAAStC,QAAUA,IACrDsC,SAAStC,MAAQxC,EAAawC,IAGhCoF,EAAiB9M,EAAUU,MAAO4F,GAoBlCuH,CAAYnG,EAAOD,GAEnB,IAAMqG,EAAa,CACjBxF,QAASoD,EAAW1L,EAAUC,KAAMqI,GACpCM,SAAU8C,EAAW1L,EAAUK,KAAMuI,GACrCC,SAAU6C,EAAW1L,EAAUM,KAAMuI,GACrCJ,aAAciD,EAAW1L,EAAUO,SAAUkI,GAC7CK,WAAY4C,EAAW1L,EAAUQ,OAAQsI,GACzCJ,UAAWgD,EAAW1L,EAAUS,MAAOiI,IAGnCqF,EAAY,GACZC,EAAc,GAEpB7M,OAAOC,KAAK0M,GAAYlJ,QAAQ,SAAAxB,GAC9B,IAA6B0K,EAAAA,EAAW1K,GAAhC+I,EAAR8B,EAAQ9B,QAASF,EAAjBgC,EAAiBhC,QAEbE,EAAQ/J,SACV2L,EAAU3K,GAAW+I,GAEnBF,EAAQ7J,SACV4L,EAAY5K,GAAW0K,EAAW1K,GAAS6I,WAI3C0B,GACFA,IAGFC,EAAoBF,EAAUK,EAAWC,IAIvCE,EAAkB,KCrJDC,eAOnBC,SAAAA,GAAAA,SAAAA,IAAAA,IAAAA,IAAAA,EAAAA,EAAAA,UAAAA,OAAAA,EAAAA,IAAAA,MAAAA,GAAAA,EAAAA,EAAAA,EAAAA,EAAAA,IAAAA,EAAAA,GAAAA,UAAAA,GAEAC,OAFAD,EAAAA,EAAAA,KAAAA,MAAAA,EAAAA,CAAAA,MAAAA,OAAAA,KAAAA,MAAAA,UAAW,EAEXC,EAFAD,EAAAA,EAAAA,GAEAC,IAAAA,EAAAA,EAAAA,UATsCjD,OAStCiD,EAAAA,sBAAA,SAAsBC,GACpB,OAAQC,EAAAA,QAAaD,EAAWvE,KAAK1H,UAGvCmM,mBAAA,WACEzE,KAAK0E,cAGPC,EAAAA,qBAAA,WAC8B3E,KAAK1H,MAAMuH,QAA/BQ,gBACQI,OAAOT,MACvBA,KAAK0E,cAGPA,EAAAA,WAAA,WACE,IL0JuBvM,EI3BOwL,EC/H9BiB,EAAuC5E,KAAK1H,MAAMuH,QAAzBK,EAAAA,EAAAA,UACrBC,EAAc,KACZ0E,GLwJiB1M,EK1JfkI,EAAAA,gBAGUC,MAAMhJ,IAAI,SAAAkJ,GACxB,IAAMlI,EAAKwM,EAAA,GAAQtE,EAASlI,OAE5B,cADOA,EAAMuH,QACNvH,ILoJ0B,CACvCiG,QAAS9E,EAAwB,CDvL3B,QCuLkDtB,GACxDqG,eAAgBpF,ED7KV,iBC6K2DjB,GACjE4M,MAAO7M,EAAqBC,EAxLrB,SAyLPiE,OAAQlE,EAAqBC,EAxLF,2BAyL3BsG,eAAgBrF,ED/KV,iBC+K2DjB,GACjE0G,SAAU3E,EACRjE,EAAUK,KACV,CDxLG,MANC,QC+LJ6B,GAEF2G,SAAU5E,EACRjE,EAAUM,KACV,CD/LI,OANG,UAGE,aAID,WAFC,YCuMT4B,GAEFuG,aAAcxE,EAAqBjE,EAAUO,SAAU,CD1M3C,aC0MwE2B,GACpF0L,oBAAqB1K,EAAuBhB,GAC5C4G,WAAY7E,EACVjE,EAAUQ,OACV,CDzMG,MALO,aC+MV0B,GAEFwG,UAAWzE,EAAqBjE,EAAUS,MAAO,CDpNvC,WCoNkEyB,GAC5EwF,MAAOjF,EAAsBP,GAC7BuF,gBAAiBtE,EDxMV,kBCwM4DjB,GACnE8G,kBAAmBjE,EAAwB7C,EAjNtB,uBKgCf8I,EAASnB,WDsHiB6D,ECrHFkB,EDsH1BV,GACFa,qBAAqBb,GAGnBR,EAASoB,MACXZ,EAAkBc,sBAAsB,WACtCvB,EAAiBC,EAAU,WACzBQ,EAAkB,UAItBT,EAAiBC,GACjBQ,EAAkB,OCjIP7F,IACT6B,EAAc7B,EAAiBuG,IAEjC3E,EAAUC,IAMZ+E,EAAAA,KAAA,WACMlF,KAAKqE,WAITrE,KAAKqE,UAAW,EAEYrE,KAAK1H,MAAMuH,QAA/BQ,gBACQE,IAAIP,MACpBA,KAAK0E,iBAGPvD,OAAA,WAGE,OAFAnB,KAAKkF,OAGN,MA5DqC7D,EAOtCgD,CAPsChD,aAAnB+C,EACZ9C,UAAY,CACjBzB,QAASgB,EAAcW,YAFN4C,EAKZ1C,YAAc,qDCEVyD,eAkDXb,SAAAA,GAAAA,SAAAA,IAAAA,OAAAA,EAAAA,MAAAA,KAAAA,YAAAA,KAAAA,EAAAA,EAAAA,GAAAA,IAAAA,EAAAA,EAAAA,UAlD0BjD,OAkD1BiD,EAAAA,sBAAA,SAAsBC,GACpB,OAAQa,EAAAA,QAAYrJ,EAAQiE,KAAK1H,MAAO,cAAeyD,EAAQwI,EAAW,gBAG5Ec,EAAAA,yBAAA,SAAyBC,EAAOC,GAC9B,IAAKA,EACH,OAAO,KAGT,OAAQD,EAAMvO,MACZ,KAAKd,EAAUQ,OACf,KAAKR,EAAUO,SACb,MAAO,CACL2G,UAAWoI,GAGf,KAAKtP,EAAUS,MACb,MAAO,CACL0G,QAASmI,GAEb,QACE,MAAM,IAAIC,MACJF,IAAAA,EAAMvO,KADZ,wGAMN0O,EAAAA,yBAAA,SAAAtG,GAA2BmG,IAAAA,EAAAA,EAA2DnG,EAA3DmG,MAAOI,EAAoDvG,EAApDuG,kBAChC,OAAAZ,EAAA,GACKY,IADL1J,EAAA,IAEGsJ,EAAMvO,MAFT,GAAAkD,OAGQyL,EAAkBJ,EAAMvO,OAAS,GAEhC4O,CAAAA,EAAAA,GAN0CA,EAAAA,cAO1C3F,KAAKqF,yBAAyBC,EAP2BC,EAAAA,mBAClEvJ,KA9EJ4J,EA0FEC,sBAAA,SAA0EC,GAAA,IAAAC,EAAAC,EAAlDV,EAAAA,EAAAA,MAAOW,EAAAA,EAAAA,SAAUN,EAAAA,EAAAA,cAAeJ,EAAAA,EAAAA,eACtD,OAAQD,EAAMvO,MACZ,KAAKd,EAAUU,MACb,OAAAmO,EAAA,GACKmB,IADLF,EAAA,IAEGT,EAAMvO,MAAOwO,EACd7H,EAAAA,gBAAsBiI,EAAAA,GAAAA,GAG1BI,IAAA,KAAK9P,EAAUE,KACb,OAAA2O,EAAA,GACKmB,EADL,CAEEzH,eAAqBmH,EAAAA,GAAAA,KAGzB,KAAK1P,EAAUI,KACb,OACK4P,EAAAA,GAAAA,EACHxH,CAAAA,eAAqBkH,EAAAA,GAAAA,KAEzB,QACE,OAAAb,EAAA,GACKmB,IADLD,EAAA,IAEGV,EAAMvO,MAFT+N,EAAA,GAEqBa,GAFrBK,QAONE,4BAAA,SAA4BR,EAAmBO,GAC7C,IAAIE,EAAiBrB,EAAA,GAAQmB,GAS7B,OAPA7O,OAAOC,KAAKqO,GAAmB7K,QAAQ,SAAAuL,GAAkB,IAAAC,EACvDF,EAAiBrB,EAAA,GACZqB,IACFC,EAAAA,IAAAA,GAAiBV,EAAkBU,GAFrBC,MAMZF,GAGTG,EAAAA,sBAAA,SAAsBhB,EAAOC,GAoB3B,OAnBAgB,EAAAA,QACEpP,EAAgBuL,KAAK,SAAAzL,GAAQqO,OAAAA,EAAMvO,OAASE,IACtB,mBAAfqO,EAAMvO,KAEcI,oIAAAA,uBAAAA,EAAgB4B,KACrC,MAHN,oDAKMuM,EAAMvO,KALZ,sDASFwP,EAAAA,SACGhB,GAC2B,iBAAnBA,GACN1M,MAAMC,QAAQyM,KACZA,EAAe7C,KAAK,SAAA8D,GAAW,MAA2B,iBAAhBA,IACLlB,0CAAAA,EAAMvO,KAA6DuO,yDAAAA,EAAMvO,KAAgBuO,UAAAA,EAAMvO,KAL3I,+CAQO,GAvJX6O,EA0JEa,mBAAA,SAAmBrF,EAAU6E,GAAU,IAAAlG,EAAAC,KACjC0F,EAAoB,GAkDxB,OAhDArI,EAAK,QAACqJ,SAAS7L,QAAQuG,EAAU,SAAAkE,GAC/B,GAAKA,GAAUA,EAAMhN,MAArB,CAIA,IAAAqO,EAAoDrB,EAAMhN,MAAxCiN,EAAVnE,EAAAA,SAA6BwF,EAErCC,EAAAF,EAAAG,GAAMnB,EAAgBvO,OAAOC,KAAKuP,GAAY7O,OAAO,SAACC,EAAKC,GAEzD,OADAD,EAAIF,EAAaG,IAAQA,GAAO2O,EAAW3O,GACpCD,GACN,IAEGjB,EAASuO,EAATvO,KAON,OANoB,iBAATA,EACTA,EAAOA,EAAK8G,WAEZkC,EAAKuG,sBAAsBhB,EAAOC,GAG5BxO,GACN,KAAKd,EAAUW,SACbqP,EAAWlG,EAAK0G,mBAAmBlB,EAAgBU,GACnD,MAEF,KAAKhQ,EAAUK,KACf,KAAKL,EAAUM,KACf,KAAKN,EAAUO,SACf,KAAKP,EAAUQ,OACf,KAAKR,EAAUS,MACbgP,EAAoB3F,EAAK0F,yBAAyB,CAChDH,MAAAA,EACAI,kBAAAA,EACAC,cAAAA,EACAJ,eAAAA,IAEF,MAEF,QACEU,EAAWlG,EAAK8F,sBAAsB,CACpCP,MAAAA,EACAW,SAAAA,EACAN,cAAAA,EACAJ,eAAAA,QAMDvF,KAAKkG,4BAA4BR,EAAmBO,IA7M/DL,EAgNEzE,OAAA,WACE,IAA+B4F,EAAA/G,KAAK1H,MAA5B8I,EAAR2F,EAAQ3F,SAAa9I,EAArBuO,EAAAE,EAAAC,GACIf,EAAgB3N,EAAAA,GAAAA,GACd4I,EAAe5I,EAAf4I,WAUN,OARIE,IACF6E,EAAWjG,KAAKyG,mBAAmBrF,EAAU6E,KAG3C/E,GAAgBA,aAAsBtB,IACxCsB,EAAa,IAAItB,EAAWsB,EAAWrB,QAASqB,EAAWvB,YAGtDuB,eAEL7D,EAAC,QAAAC,cAAA8G,OAAe6B,EAAhB,CAA0BpG,QAASqB,EAAWtG,MAAOsG,gBAAYhI,kBAEjEmE,EAAA,QAAAC,cAACqD,EAAQsG,SAAT,KACG,SACCpH,gBACG,OAAAxC,UAAAC,cAAC8G,EAADU,EAAA,GAAgBmB,EAAhB,CAA0BpG,QAASA,QApOpBwB,EAkD1BiD,CAlD0BjD,EAAAA,WAAf8D,EAoBJ7D,UAAY,CACjB9B,KAAMsB,EAAAA,QAAUoG,OAChB1I,eAAgBsC,EAAS,QAACoG,OAC1B9F,SAAUN,EAAAA,QAAUqG,UAAU,CAACrG,EAAAA,QAAUsG,QAAQtG,EAAS,QAACS,MAAOT,EAAAA,QAAUS,OAC5E8F,aAAcvG,EAAS,QAAC3C,OACxB4G,MAAOjE,EAAAA,QAAUwG,KACjBpL,wBAAyB4E,EAAAA,QAAUwG,KACnC7I,eAAgBqC,UAAUoG,OAC1B7H,KAAMyB,EAAS,QAACsG,QAAQtG,EAAS,QAACoG,QAClC9H,KAAM0B,EAAS,QAACsG,QAAQtG,EAAS,QAACoG,QAClCzH,SAAUqB,EAAS,QAACsG,QAAQtG,UAAUoG,QACtCrD,oBAAqB/C,EAAAA,QAAUE,KAC/B1B,OAAQwB,EAAS,QAACsG,QAAQtG,EAAS,QAACoG,QACpCxH,MAAOoB,EAAS,QAACsG,QAAQtG,UAAUoG,QACnCvJ,MAAOmD,EAAS,QAAC3C,OACjBT,gBAAiBoD,EAAAA,QAAUoG,OAC3BK,cAAezG,UAAU3C,OACzBc,kBAAmB6B,EAAAA,QAAUwG,KAC7BpG,WAAYJ,EAAS,QAACoG,QAtCb/B,EA0CJ1D,aAAe,CACpBsD,OAAO,EACP7I,yBAAyB,EACzB+C,mBAAmB,GA7CVkG,EAgDJzD,YAAc"}