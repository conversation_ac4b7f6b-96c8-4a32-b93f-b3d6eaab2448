"""
KnowledgeBot - Corporate Memory System
Health Check API Endpoints

This module provides health check endpoints for monitoring system status,
component health, and performance metrics.
"""

from typing import Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import get_db, get_current_user, require_permissions
from app.models.user import User
from app.services.health_service import HealthService

router = APIRouter()
health_service = HealthService()


@router.get("/", response_model=Dict[str, Any])
async def basic_health_check() -> Dict[str, Any]:
    """
    Basic health check endpoint for load balancers and monitoring.
    
    Returns:
        Dict[str, Any]: Basic health status
    """
    return await health_service.get_basic_health()


@router.get("/detailed", response_model=Dict[str, Any])
async def detailed_health_check(
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permissions(["system.health.read"]))
) -> Dict[str, Any]:
    """
    Detailed health check with component status.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        Dict[str, Any]: Detailed health information
        
    Raises:
        HTTPException: If user doesn't have permission
    """
    try:
        return await health_service.get_detailed_health()
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Health check failed: {str(e)}"
        )


@router.get("/metrics", response_model=Dict[str, Any])
async def get_system_metrics(
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permissions(["system.metrics.read"]))
) -> Dict[str, Any]:
    """
    Get system performance metrics.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        Dict[str, Any]: System metrics
        
    Raises:
        HTTPException: If user doesn't have permission
    """
    try:
        return await health_service.get_metrics()
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get metrics: {str(e)}"
        )


@router.get("/database", response_model=Dict[str, Any])
async def database_health_check(
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permissions(["system.health.read"]))
) -> Dict[str, Any]:
    """
    Database-specific health check.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        Dict[str, Any]: Database health status
    """
    try:
        detailed_health = await health_service.get_detailed_health()
        return {
            "status": "healthy",
            "database": detailed_health.get("checks", {}).get("database", {})
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Database health check failed: {str(e)}"
        )


@router.get("/cache", response_model=Dict[str, Any])
async def cache_health_check(
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permissions(["system.health.read"]))
) -> Dict[str, Any]:
    """
    Cache (Redis) specific health check.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        Dict[str, Any]: Cache health status
    """
    try:
        detailed_health = await health_service.get_detailed_health()
        return {
            "status": "healthy",
            "redis": detailed_health.get("checks", {}).get("redis", {})
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Cache health check failed: {str(e)}"
        )


@router.get("/storage", response_model=Dict[str, Any])
async def storage_health_check(
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permissions(["system.health.read"]))
) -> Dict[str, Any]:
    """
    Storage service health check.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        Dict[str, Any]: Storage health status
    """
    try:
        detailed_health = await health_service.get_detailed_health()
        return {
            "status": "healthy",
            "storage": detailed_health.get("checks", {}).get("storage", {})
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Storage health check failed: {str(e)}"
        )


@router.get("/memvid", response_model=Dict[str, Any])
async def memvid_health_check(
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permissions(["system.health.read"]))
) -> Dict[str, Any]:
    """
    MemVid service health check.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        Dict[str, Any]: MemVid health status
    """
    try:
        detailed_health = await health_service.get_detailed_health()
        return {
            "status": "healthy",
            "memvid": detailed_health.get("checks", {}).get("memvid", {})
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"MemVid health check failed: {str(e)}"
        )


@router.post("/cache/clear")
async def clear_health_cache(
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permissions(["system.admin"]))
) -> Dict[str, str]:
    """
    Clear health check cache.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        Dict[str, str]: Success message
    """
    try:
        health_service.clear_cache()
        return {"message": "Health check cache cleared successfully"}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to clear cache: {str(e)}"
        )


@router.get("/readiness", response_model=Dict[str, Any])
async def readiness_check() -> Dict[str, Any]:
    """
    Kubernetes readiness probe endpoint.
    
    Returns:
        Dict[str, Any]: Readiness status
    """
    try:
        # Check critical components for readiness
        detailed_health = await health_service.get_detailed_health()
        checks = detailed_health.get("checks", {})
        
        # Check if critical services are healthy
        critical_services = ["database", "redis", "memvid"]
        for service in critical_services:
            service_status = checks.get(service, {}).get("status", "unknown")
            if service_status not in ["healthy", "degraded"]:
                return {
                    "status": "not_ready",
                    "reason": f"{service} is not healthy",
                    "timestamp": detailed_health.get("timestamp")
                }
        
        return {
            "status": "ready",
            "timestamp": detailed_health.get("timestamp")
        }
        
    except Exception as e:
        return {
            "status": "not_ready",
            "reason": f"Health check failed: {str(e)}"
        }


@router.get("/liveness", response_model=Dict[str, Any])
async def liveness_check() -> Dict[str, Any]:
    """
    Kubernetes liveness probe endpoint.
    
    Returns:
        Dict[str, Any]: Liveness status
    """
    try:
        # Basic liveness check - just ensure the service is responding
        basic_health = await health_service.get_basic_health()
        return {
            "status": "alive",
            "timestamp": basic_health.get("timestamp")
        }
        
    except Exception as e:
        return {
            "status": "dead",
            "reason": f"Service not responding: {str(e)}"
        }


@router.get("/startup", response_model=Dict[str, Any])
async def startup_check() -> Dict[str, Any]:
    """
    Kubernetes startup probe endpoint.
    
    Returns:
        Dict[str, Any]: Startup status
    """
    try:
        # Check if all services have started successfully
        detailed_health = await health_service.get_detailed_health()
        checks = detailed_health.get("checks", {})
        
        # All services should be at least responding (not necessarily healthy)
        required_services = ["database", "redis", "storage", "memvid"]
        for service in required_services:
            service_status = checks.get(service, {}).get("status", "unknown")
            if service_status == "error":
                return {
                    "status": "starting",
                    "reason": f"{service} failed to start",
                    "timestamp": detailed_health.get("timestamp")
                }
        
        return {
            "status": "started",
            "timestamp": detailed_health.get("timestamp")
        }
        
    except Exception as e:
        return {
            "status": "starting",
            "reason": f"Startup check failed: {str(e)}"
        }
