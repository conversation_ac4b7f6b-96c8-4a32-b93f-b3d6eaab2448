/**
 * KnowledgeBot - Corporate Memory System
 * Document Upload Page Component
 */

import React from 'react'
import { Helmet } from 'react-helmet-async'

export default function DocumentUploadPage() {
  return (
    <>
      <Helmet>
        <title>Upload Document - KnowledgeBot</title>
      </Helmet>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            Upload Document
          </h1>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Add new documents to your knowledge base
          </p>
        </div>
        
        <div className="card">
          <div className="card-body text-center py-12">
            <p className="text-gray-600 dark:text-gray-400">
              Document upload interface coming soon...
            </p>
          </div>
        </div>
      </div>
    </>
  )
}
