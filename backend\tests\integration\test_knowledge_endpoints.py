"""
KnowledgeBot Backend - Knowledge Endpoints Integration Tests
Integration tests for knowledge query API endpoints.
"""

import pytest
from httpx import AsyncClient
from fastapi import status
from unittest.mock import patch, AsyncMock, MagicMock

from app.models.user import User
from app.models.document import Document, DocumentChunk
from app.core.security import get_password_hash


class TestKnowledgeEndpoints:
    """Integration tests for knowledge endpoints."""
    
    @pytest.mark.asyncio
    async def test_query_knowledge_success(self, async_client: AsyncClient, auth_headers, db_session):
        """Test successful knowledge query."""
        # Create test document and chunk
        document = Document(
            title="Test Document",
            content="This is a test document about artificial intelligence.",
            file_type="txt",
            file_size=100,
            uploaded_by_id=auth_headers.get("user_id", "test-user-id")
        )
        db_session.add(document)
        await db_session.commit()
        
        chunk = DocumentChunk(
            document_id=document.id,
            content="This is a test document about artificial intelligence.",
            chunk_index=0,
            embedding=[0.1] * 384
        )
        db_session.add(chunk)
        await db_session.commit()
        
        # Mock services
        with patch('app.api.v1.endpoints.knowledge.memvid_service') as mock_memvid:
            with patch('app.api.v1.endpoints.knowledge.search_service') as mock_search:
                # Mock MemVid service
                mock_memvid.generate_embedding.return_value = [0.1] * 384
                mock_memvid.generate_answer.return_value = MagicMock(
                    answer="Artificial intelligence is a field of computer science.",
                    confidence_score=0.85,
                    processing_time=0.5
                )
                
                # Mock search service
                mock_search.vector_similarity_search.return_value = [chunk]
                mock_search.get_related_queries.return_value = ["machine learning", "deep learning"]
                
                # Query knowledge
                query_data = {
                    "question": "What is artificial intelligence?",
                    "max_answer_length": 500,
                    "include_sources": True
                }
                
                response = await async_client.post(
                    "/api/v1/knowledge/query",
                    json=query_data,
                    headers=auth_headers
                )
                
                assert response.status_code == status.HTTP_200_OK
                data = response.json()
                assert "answer" in data
                assert "confidence_score" in data
                assert "sources" in data
                assert "related_queries" in data
                assert data["confidence_score"] == 0.85
    
    @pytest.mark.asyncio
    async def test_query_knowledge_empty_question(self, async_client: AsyncClient, auth_headers):
        """Test knowledge query with empty question."""
        query_data = {
            "question": "",
            "max_answer_length": 500
        }
        
        response = await async_client.post(
            "/api/v1/knowledge/query",
            json=query_data,
            headers=auth_headers
        )
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        data = response.json()
        assert "empty" in data["detail"].lower()
    
    @pytest.mark.asyncio
    async def test_query_knowledge_no_results(self, async_client: AsyncClient, auth_headers):
        """Test knowledge query with no relevant results."""
        with patch('app.api.v1.endpoints.knowledge.memvid_service') as mock_memvid:
            with patch('app.api.v1.endpoints.knowledge.search_service') as mock_search:
                # Mock no results found
                mock_memvid.generate_embedding.return_value = [0.1] * 384
                mock_search.vector_similarity_search.return_value = []
                mock_search.get_related_queries.return_value = ["try different keywords"]
                
                query_data = {
                    "question": "What is quantum computing?",
                    "max_answer_length": 500
                }
                
                response = await async_client.post(
                    "/api/v1/knowledge/query",
                    json=query_data,
                    headers=auth_headers
                )
                
                assert response.status_code == status.HTTP_200_OK
                data = response.json()
                assert "couldn't find" in data["answer"].lower()
                assert data["confidence_score"] == 0.0
                assert len(data["sources"]) == 0
    
    @pytest.mark.asyncio
    async def test_search_documents_success(self, async_client: AsyncClient, auth_headers, db_session):
        """Test successful document search."""
        # Create test documents
        doc1 = Document(
            title="Machine Learning Guide",
            content="A comprehensive guide to machine learning algorithms.",
            file_type="pdf",
            file_size=1000,
            uploaded_by_id=auth_headers.get("user_id", "test-user-id")
        )
        doc2 = Document(
            title="Deep Learning Basics",
            content="Introduction to deep learning and neural networks.",
            file_type="pdf", 
            file_size=800,
            uploaded_by_id=auth_headers.get("user_id", "test-user-id")
        )
        db_session.add_all([doc1, doc2])
        await db_session.commit()
        
        with patch('app.api.v1.endpoints.knowledge.search_service') as mock_search:
            mock_search.hybrid_search.return_value = [
                MagicMock(
                    document=doc1,
                    content="Machine learning algorithms",
                    similarity_score=0.9
                ),
                MagicMock(
                    document=doc2,
                    content="Deep learning networks",
                    similarity_score=0.8
                )
            ]
            
            response = await async_client.get(
                "/api/v1/knowledge/search",
                params={"q": "machine learning"},
                headers=auth_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert "results" in data
            assert "total" in data
            assert len(data["results"]) == 2
    
    @pytest.mark.asyncio
    async def test_search_documents_with_filters(self, async_client: AsyncClient, auth_headers):
        """Test document search with filters."""
        with patch('app.api.v1.endpoints.knowledge.search_service') as mock_search:
            mock_search.hybrid_search.return_value = []
            
            response = await async_client.get(
                "/api/v1/knowledge/search",
                params={
                    "q": "machine learning",
                    "document_type": "pdf",
                    "classification_level": "public",
                    "date_from": "2023-01-01",
                    "date_to": "2023-12-31",
                    "sort_by": "relevance",
                    "limit": 10
                },
                headers=auth_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert "results" in data
    
    @pytest.mark.asyncio
    async def test_search_documents_pagination(self, async_client: AsyncClient, auth_headers):
        """Test document search with pagination."""
        with patch('app.api.v1.endpoints.knowledge.search_service') as mock_search:
            mock_search.hybrid_search.return_value = []
            
            response = await async_client.get(
                "/api/v1/knowledge/search",
                params={
                    "q": "test query",
                    "skip": 20,
                    "limit": 10
                },
                headers=auth_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert "results" in data
            assert "total" in data
            assert "skip" in data
            assert "limit" in data
    
    @pytest.mark.asyncio
    async def test_get_search_suggestions(self, async_client: AsyncClient, auth_headers):
        """Test getting search suggestions."""
        with patch('app.api.v1.endpoints.knowledge.search_service') as mock_search:
            mock_search.get_search_suggestions.return_value = [
                {"suggestion": "machine learning", "frequency": 10},
                {"suggestion": "machine vision", "frequency": 5}
            ]
            
            response = await async_client.get(
                "/api/v1/knowledge/suggestions",
                params={"q": "mach"},
                headers=auth_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert "suggestions" in data
            assert len(data["suggestions"]) == 2
    
    @pytest.mark.asyncio
    async def test_knowledge_query_unauthorized(self, async_client: AsyncClient):
        """Test knowledge query without authentication."""
        query_data = {
            "question": "What is AI?",
            "max_answer_length": 500
        }
        
        response = await async_client.post("/api/v1/knowledge/query", json=query_data)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
    
    @pytest.mark.asyncio
    async def test_search_documents_unauthorized(self, async_client: AsyncClient):
        """Test document search without authentication."""
        response = await async_client.get(
            "/api/v1/knowledge/search",
            params={"q": "test"}
        )
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
    
    @pytest.mark.asyncio
    async def test_knowledge_query_rate_limiting(self, async_client: AsyncClient, auth_headers):
        """Test knowledge query rate limiting."""
        with patch('app.api.v1.endpoints.knowledge.RateLimitTier') as mock_rate_tier:
            mock_rate_tier.check_rate_limit.return_value = False
            
            # This test would need the actual rate limiting middleware
            # For now, we just test the rate limit check
            query_data = {
                "question": "What is AI?",
                "max_answer_length": 500
            }
            
            # In a real implementation, this would return 429 Too Many Requests
            # when rate limit is exceeded
            response = await async_client.post(
                "/api/v1/knowledge/query",
                json=query_data,
                headers=auth_headers
            )
            
            # The actual response depends on implementation
            assert response.status_code in [
                status.HTTP_200_OK,  # If rate limiting not yet implemented
                status.HTTP_429_TOO_MANY_REQUESTS  # If rate limiting is implemented
            ]
    
    @pytest.mark.asyncio
    async def test_knowledge_query_analytics_tracking(self, async_client: AsyncClient, auth_headers):
        """Test that knowledge queries are tracked for analytics."""
        with patch('app.api.v1.endpoints.knowledge.memvid_service') as mock_memvid:
            with patch('app.api.v1.endpoints.knowledge.search_service') as mock_search:
                with patch('app.api.v1.endpoints.knowledge.analytics_service') as mock_analytics:
                    # Mock services
                    mock_memvid.generate_embedding.return_value = [0.1] * 384
                    mock_memvid.generate_answer.return_value = MagicMock(
                        answer="Test answer",
                        confidence_score=0.8,
                        processing_time=0.3
                    )
                    mock_search.vector_similarity_search.return_value = []
                    mock_search.get_related_queries.return_value = []
                    
                    query_data = {
                        "question": "Test question for analytics",
                        "max_answer_length": 500
                    }
                    
                    response = await async_client.post(
                        "/api/v1/knowledge/query",
                        json=query_data,
                        headers=auth_headers
                    )
                    
                    assert response.status_code == status.HTTP_200_OK
                    
                    # Verify analytics service was called
                    # (This would be called as a background task)
                    # mock_analytics.update_query_analytics.assert_called_once()
