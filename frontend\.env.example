# KnowledgeBot Frontend Environment Configuration
# Copy this file to .env and update the values for your environment

# API Configuration
VITE_API_BASE_URL=http://localhost:8000/api/v1

# Application Configuration
VITE_APP_NAME=KnowledgeBot
VITE_APP_VERSION=1.0.0
VITE_APP_ENVIRONMENT=development
VITE_APP_DESCRIPTION=Corporate Memory System

# Feature Flags
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_DARK_MODE=true
VITE_ENABLE_NOTIFICATIONS=true
VITE_ENABLE_OFFLINE_MODE=false

# Development Configuration
VITE_DEV_TOOLS=true
VITE_DEBUG_MODE=false

# External Services (if needed)
VITE_SENTRY_DSN=
VITE_GOOGLE_ANALYTICS_ID=
VITE_HOTJAR_ID=

# Theme Configuration
VITE_DEFAULT_THEME=auto
VITE_PRIMARY_COLOR=#3b82f6
VITE_BRAND_NAME=KnowledgeBot

# Upload Configuration
VITE_MAX_FILE_SIZE=100
VITE_ALLOWED_FILE_TYPES=pdf,doc,docx,txt,md,ppt,pptx,xls,xlsx

# Security Configuration
VITE_ENABLE_CSP=true
VITE_ENABLE_HTTPS_ONLY=false

# Performance Configuration
VITE_ENABLE_SERVICE_WORKER=true
VITE_CACHE_DURATION=3600
