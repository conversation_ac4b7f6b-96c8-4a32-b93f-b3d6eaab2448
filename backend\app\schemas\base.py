"""
KnowledgeBot - Corporate Memory System
Base Pydantic Schemas

This module defines base Pydantic models and common schemas used across
the application for consistent API responses and validation.
"""

from typing import Any, Dict, List, Optional, Union
from datetime import datetime
from uuid import UUID
from pydantic import BaseModel, Field


class BaseResponse(BaseModel):
    """Base response schema with common fields."""
    success: bool = True
    message: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class ErrorResponse(BaseModel):
    """Schema for error responses."""
    success: bool = False
    error: str
    error_code: Optional[str] = None
    details: Optional[Dict[str, Any]] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class ValidationErrorResponse(BaseModel):
    """Schema for validation error responses."""
    success: bool = False
    error: str = "Validation Error"
    validation_errors: List[Dict[str, Any]]
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class PaginationMeta(BaseModel):
    """Schema for pagination metadata."""
    total: int
    skip: int
    limit: int
    has_more: bool
    page: Optional[int] = None
    total_pages: Optional[int] = None


class PaginatedResponse(BaseResponse):
    """Base schema for paginated responses."""
    data: List[Any]
    pagination: PaginationMeta


class HealthCheckResponse(BaseModel):
    """Schema for health check responses."""
    status: str
    timestamp: datetime
    service: str
    version: Optional[str] = None
    environment: Optional[str] = None
    checks: Optional[Dict[str, Any]] = None
    response_time: Optional[float] = None


class MetricsResponse(BaseModel):
    """Schema for metrics responses."""
    timestamp: datetime
    system: Dict[str, Any]
    process: Dict[str, Any]
    application: Optional[Dict[str, Any]] = None


class BulkOperationResponse(BaseModel):
    """Schema for bulk operation responses."""
    operation_id: UUID
    operation_type: str
    total_items: int
    successful: int
    failed: int
    errors: List[Dict[str, str]]
    started_at: datetime
    completed_at: Optional[datetime] = None
    status: str


class FileUploadResponse(BaseModel):
    """Schema for file upload responses."""
    file_id: UUID
    filename: str
    file_size: int
    mime_type: str
    upload_status: str
    message: str
    uploaded_at: datetime


class SearchFacet(BaseModel):
    """Schema for search facets."""
    name: str
    count: int
    selected: bool = False


class SearchFacetGroup(BaseModel):
    """Schema for search facet groups."""
    name: str
    display_name: str
    facets: List[SearchFacet]
    facet_type: str = "terms"  # terms, range, date


class SearchResponse(BaseModel):
    """Base schema for search responses."""
    query: str
    total: int
    search_time: float
    suggestions: List[str] = []
    facets: Dict[str, SearchFacetGroup] = {}
    did_you_mean: Optional[str] = None


class ExportRequest(BaseModel):
    """Schema for export requests."""
    format: str = Field(..., regex="^(csv|xlsx|json|pdf)$")
    filters: Optional[Dict[str, Any]] = None
    fields: Optional[List[str]] = None
    date_range: Optional[Dict[str, str]] = None


class ExportResponse(BaseModel):
    """Schema for export responses."""
    export_id: UUID
    format: str
    status: str
    download_url: Optional[str] = None
    file_size: Optional[int] = None
    expires_at: Optional[datetime] = None
    created_at: datetime


class NotificationResponse(BaseModel):
    """Schema for notification responses."""
    id: UUID
    type: str
    title: str
    message: str
    priority: str = "normal"
    read: bool = False
    created_at: datetime
    expires_at: Optional[datetime] = None
    metadata: Optional[Dict[str, Any]] = None


class AuditLogEntry(BaseModel):
    """Schema for audit log entries."""
    id: UUID
    user_id: Optional[UUID] = None
    action: str
    resource_type: str
    resource_id: Optional[UUID] = None
    details: Dict[str, Any]
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    timestamp: datetime


class SystemConfigResponse(BaseModel):
    """Schema for system configuration responses."""
    key: str
    value: Any
    description: Optional[str] = None
    category: str
    is_sensitive: bool = False
    updated_at: datetime
    updated_by: Optional[UUID] = None


class SystemConfigUpdate(BaseModel):
    """Schema for system configuration updates."""
    value: Any
    description: Optional[str] = None


class RateLimitInfo(BaseModel):
    """Schema for rate limit information."""
    limit: int
    remaining: int
    reset_time: datetime
    retry_after: Optional[int] = None


class APIResponse(BaseResponse):
    """Generic API response wrapper."""
    data: Optional[Any] = None
    meta: Optional[Dict[str, Any]] = None
    rate_limit: Optional[RateLimitInfo] = None


class BatchRequest(BaseModel):
    """Schema for batch requests."""
    operations: List[Dict[str, Any]] = Field(..., min_items=1, max_items=100)
    continue_on_error: bool = True


class BatchResponse(BaseModel):
    """Schema for batch responses."""
    batch_id: UUID
    total_operations: int
    successful: int
    failed: int
    results: List[Dict[str, Any]]
    errors: List[Dict[str, Any]]
    processed_at: datetime


class WebhookRequest(BaseModel):
    """Schema for webhook requests."""
    event_type: str
    data: Dict[str, Any]
    timestamp: datetime
    signature: Optional[str] = None


class WebhookResponse(BaseModel):
    """Schema for webhook responses."""
    webhook_id: UUID
    event_type: str
    status: str
    processed_at: datetime
    response_time: float


class FeatureFlagResponse(BaseModel):
    """Schema for feature flag responses."""
    name: str
    enabled: bool
    description: Optional[str] = None
    conditions: Optional[Dict[str, Any]] = None
    updated_at: datetime


class MaintenanceMode(BaseModel):
    """Schema for maintenance mode information."""
    enabled: bool
    message: Optional[str] = None
    scheduled_start: Optional[datetime] = None
    scheduled_end: Optional[datetime] = None
    allowed_ips: List[str] = []


class SystemStatus(BaseModel):
    """Schema for system status information."""
    status: str  # operational, degraded, maintenance, outage
    message: Optional[str] = None
    components: Dict[str, str]
    last_updated: datetime
    incidents: List[Dict[str, Any]] = []


class CacheInfo(BaseModel):
    """Schema for cache information."""
    key: str
    hit_rate: float
    miss_rate: float
    size: int
    max_size: int
    ttl: Optional[int] = None
    last_accessed: Optional[datetime] = None


class LogEntry(BaseModel):
    """Schema for log entries."""
    id: UUID
    level: str
    message: str
    logger: str
    module: Optional[str] = None
    function: Optional[str] = None
    line_number: Optional[int] = None
    user_id: Optional[UUID] = None
    request_id: Optional[str] = None
    extra_data: Optional[Dict[str, Any]] = None
    timestamp: datetime


class TaskStatus(BaseModel):
    """Schema for background task status."""
    task_id: UUID
    name: str
    status: str  # pending, running, completed, failed, cancelled
    progress: Optional[int] = Field(None, ge=0, le=100)
    result: Optional[Any] = None
    error: Optional[str] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    metadata: Optional[Dict[str, Any]] = None
