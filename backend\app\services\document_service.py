"""
KnowledgeBot - Corporate Memory System
Document Service for Enterprise Document Management

This module handles document processing, text extraction, and management
with comprehensive security and access control.
"""

import asyncio
import logging
import hashlib
import mimetypes
from typing import List, Dict, Any, Optional, BinaryIO
from uuid import UUID
from pathlib import Path
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_
from sqlalchemy.orm import selectinload

# Document processing libraries
import PyPDF2
from docx import Document as DocxDocument
from pptx import Presentation
from bs4 import BeautifulSoup
import pytesseract
from PIL import Image
import io

from app.models.document import Document, Tag
from app.models.user import User, Department
from app.core.config import settings
from app.services.storage_service import StorageService

logger = logging.getLogger(__name__)


class DocumentService:
    """
    Enterprise document service with multi-format processing and access control.
    """
    
    def __init__(self):
        self.storage_service = StorageService()
        self.supported_formats = {
            'application/pdf': self._extract_pdf_text,
            'application/msword': self._extract_doc_text,
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': self._extract_docx_text,
            'application/vnd.ms-powerpoint': self._extract_ppt_text,
            'application/vnd.openxmlformats-officedocument.presentationml.presentation': self._extract_pptx_text,
            'text/html': self._extract_html_text,
            'text/plain': self._extract_plain_text,
            'text/markdown': self._extract_plain_text,
            'image/jpeg': self._extract_image_text,
            'image/png': self._extract_image_text,
            'image/tiff': self._extract_image_text
        }
    
    async def extract_text_content(self, file_content: bytes, mime_type: str) -> str:
        """
        Extract text content from various file formats.
        
        Args:
            file_content: Binary file content
            mime_type: MIME type of the file
            
        Returns:
            str: Extracted text content
            
        Raises:
            ValueError: If file format is not supported
        """
        try:
            if mime_type not in self.supported_formats:
                raise ValueError(f"Unsupported file format: {mime_type}")
            
            extractor = self.supported_formats[mime_type]
            
            # Run extraction in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            text_content = await loop.run_in_executor(
                None, 
                extractor, 
                file_content
            )
            
            # Clean and validate extracted text
            cleaned_text = self._clean_extracted_text(text_content)
            
            if not cleaned_text.strip():
                logger.warning(f"No text extracted from {mime_type} file")
                return "No text content could be extracted from this document."
            
            logger.info(f"Extracted {len(cleaned_text)} characters from {mime_type} file")
            return cleaned_text
            
        except Exception as e:
            logger.error(f"Error extracting text from {mime_type}: {e}")
            raise
    
    def _extract_pdf_text(self, file_content: bytes) -> str:
        """Extract text from PDF files."""
        try:
            pdf_file = io.BytesIO(file_content)
            pdf_reader = PyPDF2.PdfReader(pdf_file)
            
            text_content = []
            for page_num, page in enumerate(pdf_reader.pages):
                try:
                    page_text = page.extract_text()
                    if page_text.strip():
                        text_content.append(f"[Page {page_num + 1}]\n{page_text}")
                except Exception as e:
                    logger.warning(f"Error extracting text from PDF page {page_num + 1}: {e}")
                    continue
            
            return "\n\n".join(text_content)
            
        except Exception as e:
            logger.error(f"Error processing PDF: {e}")
            # Fallback to OCR if available
            if settings.ENABLE_DOCUMENT_OCR:
                return self._extract_pdf_with_ocr(file_content)
            raise
    
    def _extract_docx_text(self, file_content: bytes) -> str:
        """Extract text from DOCX files."""
        try:
            doc_file = io.BytesIO(file_content)
            doc = DocxDocument(doc_file)
            
            text_content = []
            
            # Extract paragraphs
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_content.append(paragraph.text)
            
            # Extract tables
            for table in doc.tables:
                table_text = []
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        if cell.text.strip():
                            row_text.append(cell.text.strip())
                    if row_text:
                        table_text.append(" | ".join(row_text))
                
                if table_text:
                    text_content.append("\n[Table]\n" + "\n".join(table_text))
            
            return "\n\n".join(text_content)
            
        except Exception as e:
            logger.error(f"Error processing DOCX: {e}")
            raise
    
    def _extract_doc_text(self, file_content: bytes) -> str:
        """Extract text from legacy DOC files."""
        # This would require python-docx2txt or similar library
        # For now, return a placeholder
        logger.warning("Legacy DOC format extraction not fully implemented")
        return "Legacy DOC format - text extraction requires additional setup"
    
    def _extract_pptx_text(self, file_content: bytes) -> str:
        """Extract text from PPTX files."""
        try:
            ppt_file = io.BytesIO(file_content)
            presentation = Presentation(ppt_file)
            
            text_content = []
            
            for slide_num, slide in enumerate(presentation.slides):
                slide_text = [f"[Slide {slide_num + 1}]"]
                
                for shape in slide.shapes:
                    if hasattr(shape, "text") and shape.text.strip():
                        slide_text.append(shape.text)
                
                if len(slide_text) > 1:  # More than just the slide header
                    text_content.append("\n".join(slide_text))
            
            return "\n\n".join(text_content)
            
        except Exception as e:
            logger.error(f"Error processing PPTX: {e}")
            raise
    
    def _extract_ppt_text(self, file_content: bytes) -> str:
        """Extract text from legacy PPT files."""
        # This would require python-pptx or similar library for legacy format
        logger.warning("Legacy PPT format extraction not fully implemented")
        return "Legacy PPT format - text extraction requires additional setup"
    
    def _extract_html_text(self, file_content: bytes) -> str:
        """Extract text from HTML files."""
        try:
            html_content = file_content.decode('utf-8', errors='ignore')
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()
            
            # Extract text
            text = soup.get_text()
            
            # Clean up whitespace
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = ' '.join(chunk for chunk in chunks if chunk)
            
            return text
            
        except Exception as e:
            logger.error(f"Error processing HTML: {e}")
            raise
    
    def _extract_plain_text(self, file_content: bytes) -> str:
        """Extract text from plain text files."""
        try:
            # Try different encodings
            encodings = ['utf-8', 'utf-16', 'latin-1', 'cp1252']
            
            for encoding in encodings:
                try:
                    return file_content.decode(encoding)
                except UnicodeDecodeError:
                    continue
            
            # If all encodings fail, use utf-8 with error handling
            return file_content.decode('utf-8', errors='replace')
            
        except Exception as e:
            logger.error(f"Error processing plain text: {e}")
            raise
    
    def _extract_image_text(self, file_content: bytes) -> str:
        """Extract text from images using OCR."""
        if not settings.ENABLE_DOCUMENT_OCR:
            return "OCR is disabled - cannot extract text from images"
        
        try:
            image = Image.open(io.BytesIO(file_content))
            
            # Use pytesseract for OCR
            text = pytesseract.image_to_string(image)
            
            return text.strip()
            
        except Exception as e:
            logger.error(f"Error processing image with OCR: {e}")
            return "Error extracting text from image"
    
    def _extract_pdf_with_ocr(self, file_content: bytes) -> str:
        """Extract text from PDF using OCR as fallback."""
        if not settings.ENABLE_DOCUMENT_OCR:
            return "OCR is disabled - cannot extract text from PDF images"
        
        try:
            # This would require pdf2image library
            # For now, return a placeholder
            logger.warning("PDF OCR extraction not fully implemented")
            return "PDF OCR extraction requires additional setup"
            
        except Exception as e:
            logger.error(f"Error processing PDF with OCR: {e}")
            return "Error extracting text from PDF with OCR"
    
    def _clean_extracted_text(self, text: str) -> str:
        """Clean and normalize extracted text."""
        if not text:
            return ""
        
        # Remove excessive whitespace
        import re
        text = re.sub(r'\s+', ' ', text)
        
        # Remove control characters
        text = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x84\x86-\x9f]', '', text)
        
        # Normalize line breaks
        text = text.replace('\r\n', '\n').replace('\r', '\n')
        
        return text.strip()
    
    async def get_document_with_access_check(
        self,
        db: AsyncSession,
        document_id: UUID,
        user: User,
        require_write: bool = False
    ) -> Optional[Document]:
        """
        Get document with access control validation.
        
        Args:
            db: Database session
            document_id: Document UUID
            user: Current user
            require_write: Whether write access is required
            
        Returns:
            Optional[Document]: Document if user has access, None otherwise
        """
        try:
            # Get document with relationships
            stmt = select(Document).options(
                selectinload(Document.departments),
                selectinload(Document.uploader)
            ).where(
                and_(
                    Document.id == document_id,
                    Document.status == "active"
                )
            )
            
            result = await db.execute(stmt)
            document = result.scalar_one_or_none()
            
            if not document:
                return None
            
            # Check access permissions
            if user.is_superuser:
                return document
            
            # Check if user is the uploader
            if document.uploaded_by == user.id:
                return document
            
            # Check if document is public
            if document.is_public and not require_write:
                return document
            
            # Check departmental access
            user_departments = {dept.id for dept in user.departments}
            document_departments = {dept.id for dept in document.departments}
            
            if user_departments.intersection(document_departments):
                # Check if user has write access if required
                if require_write:
                    # This would check specific write permissions
                    # For now, allow if user is in the department
                    return document
                else:
                    return document
            
            return None
            
        except Exception as e:
            logger.error(f"Error checking document access: {e}")
            return None
    
    async def add_tags_to_document(
        self,
        db: AsyncSession,
        document_id: UUID,
        tag_names: List[str]
    ):
        """Add tags to a document, creating new tags if necessary."""
        try:
            document = await db.get(Document, document_id)
            if not document:
                raise ValueError("Document not found")
            
            for tag_name in tag_names:
                tag_name = tag_name.strip().lower()
                if not tag_name:
                    continue
                
                # Get or create tag
                stmt = select(Tag).where(Tag.name == tag_name)
                result = await db.execute(stmt)
                tag = result.scalar_one_or_none()
                
                if not tag:
                    tag = Tag(
                        name=tag_name,
                        display_name=tag_name.title(),
                        tag_type="custom"
                    )
                    db.add(tag)
                    await db.flush()
                
                # Add tag to document if not already present
                if tag not in document.tags:
                    document.tags.append(tag)
                    tag.usage_count += 1
                    tag.last_used = datetime.utcnow()
            
            await db.commit()
            
        except Exception as e:
            logger.error(f"Error adding tags to document: {e}")
            raise
    
    async def assign_document_to_departments(
        self,
        db: AsyncSession,
        document_id: UUID,
        department_ids: List[UUID]
    ):
        """Assign document to departments for access control."""
        try:
            document = await db.get(Document, document_id)
            if not document:
                raise ValueError("Document not found")
            
            # Get departments
            stmt = select(Department).where(Department.id.in_(department_ids))
            result = await db.execute(stmt)
            departments = result.scalars().all()
            
            # Assign departments to document
            for dept in departments:
                if dept not in document.departments:
                    document.departments.append(dept)
            
            await db.commit()
            
        except Exception as e:
            logger.error(f"Error assigning document to departments: {e}")
            raise
    
    async def get_document_statistics(self, db: AsyncSession) -> Dict[str, Any]:
        """Get document processing statistics."""
        try:
            # Total documents
            total_stmt = select(func.count(Document.id)).where(Document.status == "active")
            total_result = await db.execute(total_stmt)
            total_documents = total_result.scalar() or 0
            
            # Processed documents
            processed_stmt = select(func.count(Document.id)).where(
                and_(
                    Document.status == "active",
                    Document.processing_status == "completed"
                )
            )
            processed_result = await db.execute(processed_stmt)
            processed_documents = processed_result.scalar() or 0
            
            # Processing documents
            processing_stmt = select(func.count(Document.id)).where(
                and_(
                    Document.status == "active",
                    Document.processing_status == "processing"
                )
            )
            processing_result = await db.execute(processing_stmt)
            processing_documents = processing_result.scalar() or 0
            
            # Failed documents
            failed_stmt = select(func.count(Document.id)).where(
                and_(
                    Document.status == "active",
                    Document.processing_status == "failed"
                )
            )
            failed_result = await db.execute(failed_stmt)
            failed_documents = failed_result.scalar() or 0
            
            # Documents by type
            type_stmt = select(
                Document.document_type,
                func.count(Document.id).label('count')
            ).where(Document.status == "active").group_by(Document.document_type)
            
            type_result = await db.execute(type_stmt)
            documents_by_type = {
                row[0] or "unknown": row[1] 
                for row in type_result.fetchall()
            }
            
            return {
                "total_documents": total_documents,
                "processed_documents": processed_documents,
                "processing_documents": processing_documents,
                "failed_documents": failed_documents,
                "processing_rate": (processed_documents / total_documents * 100) if total_documents > 0 else 0,
                "documents_by_type": documents_by_type
            }
            
        except Exception as e:
            logger.error(f"Error getting document statistics: {e}")
            return {}
    
    def get_supported_formats(self) -> List[str]:
        """Get list of supported file formats."""
        return list(self.supported_formats.keys())
    
    def is_format_supported(self, mime_type: str) -> bool:
        """Check if a file format is supported."""
        return mime_type in self.supported_formats
