/**
 * KnowledgeBot - Corporate Memory System
 * TypeScript Type Definitions
 * 
 * This file contains all the TypeScript interfaces and types used throughout
 * the frontend application for type safety and better development experience.
 */

// Base types
export interface BaseEntity {
  id: string
  created_at: string
  updated_at?: string
}

// User and Authentication types
export interface User extends BaseEntity {
  email: string
  first_name?: string
  last_name?: string
  display_name?: string
  is_active: boolean
  is_superuser: boolean
  is_verified: boolean
  is_locked: boolean
  must_change_password: boolean
  failed_login_attempts: number
  last_login?: string
  last_password_change?: string
  roles: Role[]
  departments: Department[]
  sso_provider?: string
  preferences?: UserPreferences
}

export interface Role extends BaseEntity {
  name: string
  display_name: string
  description?: string
  is_system_role: boolean
  permissions: Permission[]
  user_count?: number
}

export interface Permission extends BaseEntity {
  name: string
  display_name: string
  description?: string
  category: string
  is_system_permission: boolean
}

export interface Department extends BaseEntity {
  name: string
  display_name: string
  description?: string
  parent_department_id?: string
  is_active: boolean
  user_count?: number
  document_count?: number
  subdepartments?: Department[]
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto'
  language: string
  timezone: string
  notifications_enabled: boolean
  email_notifications: boolean
  [key: string]: any
}

// Authentication types
export interface LoginRequest {
  email: string
  password: string
  remember_me?: boolean
}

export interface LoginResponse {
  access_token: string
  refresh_token: string
  token_type: string
  expires_in: number
  user: User
  permissions: string[]
  departments: string[]
}

export interface RegisterRequest {
  email: string
  password: string
  first_name: string
  last_name: string
  display_name?: string
  terms_accepted: boolean
}

// Document types
export interface Document extends BaseEntity {
  title: string
  description?: string
  filename: string
  file_size: number
  mime_type: string
  file_hash: string
  storage_path: string
  document_type?: string
  category?: string
  classification_level: 'public' | 'internal' | 'confidential' | 'restricted'
  is_public: boolean
  processing_status: 'pending' | 'processing' | 'completed' | 'failed'
  processing_error?: string
  text_content?: string
  summary?: string
  key_points?: string[]
  status: 'active' | 'archived' | 'deleted'
  uploader?: User
  departments: Department[]
  tags: Tag[]
  chunks?: DocumentChunk[]
}

export interface DocumentChunk extends BaseEntity {
  document_id: string
  content: string
  chunk_index: number
  memvid_embedding?: number[]
  similarity_score?: number
}

export interface Tag extends BaseEntity {
  name: string
  display_name: string
  tag_type: string
  usage_count: number
  last_used?: string
}

export interface DocumentUploadRequest {
  title: string
  description?: string
  category?: string
  document_type?: string
  classification_level: string
  is_public: boolean
  department_ids?: string[]
  tag_names?: string[]
}

// Knowledge and Search types
export interface KnowledgeQuery {
  query: string
  context?: Record<string, any>
  max_results?: number
  include_sources?: boolean
  confidence_threshold?: number
  department_filter?: string[]
  document_types?: string[]
  date_range?: {
    start?: string
    end?: string
  }
}

export interface KnowledgeResponse {
  query_id: string
  query: string
  answer: string
  confidence_score: number
  sources: SourceDocument[]
  processing_time: number
  model_used: string
  suggestions?: string[]
  related_queries?: string[]
  metadata?: Record<string, any>
  generated_at: string
}

export interface SourceDocument {
  document_id: string
  title: string
  excerpt: string
  relevance_score: number
  document_type: string
  category?: string
  url: string
  created_at: string
  metadata?: Record<string, any>
}

export interface SearchRequest {
  query: string
  filters?: Record<string, any>
  sort_by?: 'relevance' | 'date' | 'title'
  skip?: number
  limit?: number
}

export interface SearchResult {
  document_id: string
  title: string
  content_excerpt: string
  relevance_score: number
  document_type: string
  category: string
  created_at: string
  url: string
  metadata: Record<string, any>
}

export interface SearchResponse {
  results: SearchResult[]
  total: number
  search_time: number
  suggestions: string[]
  facets: Record<string, any>
  query: string
}

// Analytics types
export interface AnalyticsMetric {
  name: string
  value: number | string
  change_percent?: number
  trend?: 'up' | 'down' | 'stable'
  metadata?: Record<string, any>
}

export interface DashboardMetrics {
  metrics: AnalyticsMetric[]
  period_days: number
  generated_at: string
}

export interface UsageReport {
  period: string
  total_queries: number
  total_searches: number
  total_documents: number
  active_users: number
  top_queries: Array<Record<string, any>>
  top_documents: Array<Record<string, any>>
  department_usage: Array<Record<string, any>>
  performance_metrics: Record<string, any>
  generated_at: string
}

// System and Health types
export interface HealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy'
  timestamp: string
  service: string
  version?: string
  environment?: string
  checks?: Record<string, any>
  response_time?: number
}

export interface SystemMetrics {
  timestamp: string
  system: Record<string, any>
  process: Record<string, any>
  application?: Record<string, any>
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  timestamp: string
  meta?: Record<string, any>
}

export interface PaginatedResponse<T = any> {
  data: T[]
  total: number
  skip: number
  limit: number
  has_more: boolean
  pagination?: {
    page?: number
    total_pages?: number
  }
}

export interface ErrorResponse {
  success: false
  error: string
  error_code?: string
  details?: Record<string, any>
  timestamp: string
}

// UI and Component types
export interface TableColumn<T = any> {
  key: string
  title: string
  dataIndex?: keyof T
  render?: (value: any, record: T, index: number) => React.ReactNode
  sorter?: boolean | ((a: T, b: T) => number)
  filters?: Array<{ text: string; value: any }>
  width?: number | string
  align?: 'left' | 'center' | 'right'
  fixed?: 'left' | 'right'
  ellipsis?: boolean
}

export interface FilterOption {
  label: string
  value: string | number
  count?: number
}

export interface SortOption {
  label: string
  value: string
  direction?: 'asc' | 'desc'
}

export interface BreadcrumbItem {
  title: string
  href?: string
  icon?: React.ReactNode
}

// Form types
export interface FormField {
  name: string
  label: string
  type: 'text' | 'email' | 'password' | 'textarea' | 'select' | 'checkbox' | 'radio' | 'file' | 'date'
  placeholder?: string
  required?: boolean
  disabled?: boolean
  options?: Array<{ label: string; value: any }>
  validation?: Record<string, any>
  help?: string
}

// Theme types
export interface ThemeConfig {
  theme: 'light' | 'dark' | 'auto'
  primaryColor: string
  borderRadius: number
  compactMode: boolean
}

// Notification types
export interface Notification {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message: string
  duration?: number
  actions?: Array<{
    label: string
    action: () => void
  }>
}

// File upload types
export interface FileUpload {
  file: File
  progress: number
  status: 'pending' | 'uploading' | 'completed' | 'error'
  error?: string
  result?: Document
}

// Chart and visualization types
export interface ChartData {
  labels: string[]
  datasets: Array<{
    label: string
    data: number[]
    backgroundColor?: string | string[]
    borderColor?: string | string[]
    borderWidth?: number
  }>
}

export interface TimeSeriesData {
  timestamp: string
  value: number
  label?: string
}

// Export types
export type DocumentStatus = 'active' | 'archived' | 'deleted'
export type ProcessingStatus = 'pending' | 'processing' | 'completed' | 'failed'
export type ClassificationLevel = 'public' | 'internal' | 'confidential' | 'restricted'
export type UserRole = 'superuser' | 'admin' | 'manager' | 'user' | 'guest'
export type ThemeMode = 'light' | 'dark' | 'auto'
export type NotificationType = 'success' | 'error' | 'warning' | 'info'
