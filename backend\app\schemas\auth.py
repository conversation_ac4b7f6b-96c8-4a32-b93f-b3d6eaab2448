"""
KnowledgeBot - Corporate Memory System
Authentication Pydantic Schemas

This module defines Pydantic models for authentication-related API requests
and responses including login, registration, and token management.
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from uuid import UUID
from pydantic import BaseModel, EmailStr, Field, validator

from app.schemas.base import BaseResponse


class LoginRequest(BaseModel):
    """Schema for login requests."""
    email: EmailStr
    password: str = Field(..., min_length=1, max_length=128)
    remember_me: bool = False
    
    @validator('email')
    def validate_email(cls, v):
        return v.lower()


class LoginResponse(BaseModel):
    """Schema for login responses."""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int
    user: Dict[str, Any]
    permissions: List[str]
    departments: List[str]


class RefreshTokenRequest(BaseModel):
    """Schema for token refresh requests."""
    refresh_token: str


class RefreshTokenResponse(BaseModel):
    """Schema for token refresh responses."""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int


class LogoutRequest(BaseModel):
    """Schema for logout requests."""
    refresh_token: Optional[str] = None
    logout_all_sessions: bool = False


class LogoutResponse(BaseModel):
    """Schema for logout responses."""
    message: str
    logged_out_sessions: int


class RegisterRequest(BaseModel):
    """Schema for user registration requests."""
    email: EmailStr
    password: str = Field(..., min_length=8, max_length=128)
    first_name: str = Field(..., min_length=1, max_length=100)
    last_name: str = Field(..., min_length=1, max_length=100)
    display_name: Optional[str] = Field(None, max_length=200)
    terms_accepted: bool = Field(..., description="User must accept terms and conditions")
    
    @validator('email')
    def validate_email(cls, v):
        return v.lower()
    
    @validator('password')
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not any(c.isupper() for c in v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not any(c.islower() for c in v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not any(c.isdigit() for c in v):
            raise ValueError('Password must contain at least one digit')
        return v
    
    @validator('terms_accepted')
    def validate_terms(cls, v):
        if not v:
            raise ValueError('Terms and conditions must be accepted')
        return v


class RegisterResponse(BaseModel):
    """Schema for registration responses."""
    user_id: UUID
    email: str
    message: str
    verification_required: bool


class PasswordChangeRequest(BaseModel):
    """Schema for password change requests."""
    current_password: str
    new_password: str = Field(..., min_length=8, max_length=128)
    
    @validator('new_password')
    def validate_new_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not any(c.isupper() for c in v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not any(c.islower() for c in v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not any(c.isdigit() for c in v):
            raise ValueError('Password must contain at least one digit')
        return v


class PasswordChangeResponse(BaseModel):
    """Schema for password change responses."""
    message: str
    password_changed_at: datetime


class PasswordResetRequest(BaseModel):
    """Schema for password reset requests."""
    email: EmailStr
    
    @validator('email')
    def validate_email(cls, v):
        return v.lower()


class PasswordResetResponse(BaseModel):
    """Schema for password reset responses."""
    message: str
    reset_token_sent: bool


class PasswordResetConfirmRequest(BaseModel):
    """Schema for password reset confirmation requests."""
    token: str
    new_password: str = Field(..., min_length=8, max_length=128)
    
    @validator('new_password')
    def validate_new_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not any(c.isupper() for c in v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not any(c.islower() for c in v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not any(c.isdigit() for c in v):
            raise ValueError('Password must contain at least one digit')
        return v


class PasswordResetConfirmResponse(BaseModel):
    """Schema for password reset confirmation responses."""
    message: str
    password_reset: bool


class EmailVerificationRequest(BaseModel):
    """Schema for email verification requests."""
    token: str


class EmailVerificationResponse(BaseModel):
    """Schema for email verification responses."""
    message: str
    email_verified: bool
    user_id: UUID


class ResendVerificationRequest(BaseModel):
    """Schema for resend verification requests."""
    email: EmailStr
    
    @validator('email')
    def validate_email(cls, v):
        return v.lower()


class ResendVerificationResponse(BaseModel):
    """Schema for resend verification responses."""
    message: str
    verification_sent: bool


class SSOLoginRequest(BaseModel):
    """Schema for SSO login requests."""
    provider: str = Field(..., regex="^(saml|oauth|oidc)$")
    assertion: Optional[str] = None
    code: Optional[str] = None
    state: Optional[str] = None
    redirect_uri: Optional[str] = None


class SSOLoginResponse(BaseModel):
    """Schema for SSO login responses."""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int
    user: Dict[str, Any]
    permissions: List[str]
    departments: List[str]
    sso_provider: str


class SSOConfigurationRequest(BaseModel):
    """Schema for SSO configuration requests."""
    provider: str = Field(..., regex="^(saml|oauth|oidc)$")
    enabled: bool
    configuration: Dict[str, Any]
    attribute_mapping: Dict[str, str]
    auto_create_users: bool = True
    default_role: Optional[str] = None
    default_departments: Optional[List[str]] = None


class SSOConfigurationResponse(BaseModel):
    """Schema for SSO configuration responses."""
    provider: str
    enabled: bool
    configuration: Dict[str, Any]
    attribute_mapping: Dict[str, str]
    auto_create_users: bool
    default_role: Optional[str] = None
    default_departments: List[str]
    updated_at: datetime
    updated_by: UUID


class TokenValidationRequest(BaseModel):
    """Schema for token validation requests."""
    token: str
    token_type: str = Field("access", regex="^(access|refresh)$")


class TokenValidationResponse(BaseModel):
    """Schema for token validation responses."""
    valid: bool
    user_id: Optional[UUID] = None
    expires_at: Optional[datetime] = None
    permissions: Optional[List[str]] = None
    token_type: str


class SessionInfo(BaseModel):
    """Schema for session information."""
    session_id: UUID
    user_id: UUID
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    created_at: datetime
    last_activity: datetime
    expires_at: datetime
    is_current: bool


class SessionListResponse(BaseResponse):
    """Schema for session list responses."""
    sessions: List[SessionInfo]
    total: int
    current_session_id: UUID


class SessionRevokeRequest(BaseModel):
    """Schema for session revoke requests."""
    session_ids: List[UUID] = Field(..., min_items=1)
    revoke_all_except_current: bool = False


class SessionRevokeResponse(BaseModel):
    """Schema for session revoke responses."""
    message: str
    revoked_sessions: int
    remaining_sessions: int


class TwoFactorSetupRequest(BaseModel):
    """Schema for 2FA setup requests."""
    method: str = Field(..., regex="^(totp|sms|email)$")
    phone_number: Optional[str] = Field(None, regex="^\\+[1-9]\\d{1,14}$")
    
    @validator('phone_number')
    def validate_phone_number(cls, v, values):
        if values.get('method') == 'sms' and not v:
            raise ValueError('Phone number is required for SMS 2FA')
        return v


class TwoFactorSetupResponse(BaseModel):
    """Schema for 2FA setup responses."""
    method: str
    setup_key: Optional[str] = None  # For TOTP
    qr_code: Optional[str] = None    # For TOTP
    backup_codes: List[str]
    message: str


class TwoFactorVerifyRequest(BaseModel):
    """Schema for 2FA verification requests."""
    code: str = Field(..., min_length=6, max_length=8)
    backup_code: bool = False


class TwoFactorVerifyResponse(BaseModel):
    """Schema for 2FA verification responses."""
    verified: bool
    message: str
    backup_codes_remaining: Optional[int] = None


class TwoFactorDisableRequest(BaseModel):
    """Schema for 2FA disable requests."""
    password: str
    confirmation_code: Optional[str] = None


class TwoFactorDisableResponse(BaseModel):
    """Schema for 2FA disable responses."""
    disabled: bool
    message: str


class SecurityEventResponse(BaseModel):
    """Schema for security event responses."""
    id: UUID
    event_type: str
    description: str
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    location: Optional[str] = None
    risk_level: str
    timestamp: datetime
    
    class Config:
        from_attributes = True


class SecurityEventsResponse(BaseResponse):
    """Schema for security events list responses."""
    events: List[SecurityEventResponse]
    total: int
    skip: int
    limit: int
    has_more: bool


class AccountSecurityResponse(BaseModel):
    """Schema for account security responses."""
    two_factor_enabled: bool
    two_factor_method: Optional[str] = None
    last_password_change: Optional[datetime] = None
    failed_login_attempts: int
    account_locked: bool
    locked_until: Optional[datetime] = None
    recent_security_events: List[SecurityEventResponse]
    trusted_devices: int
    active_sessions: int
