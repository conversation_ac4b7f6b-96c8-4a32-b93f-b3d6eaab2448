"""
KnowledgeBot - Corporate Memory System
Knowledge Query Pydantic Schemas

This module defines Pydantic models for knowledge query API requests and responses
with comprehensive validation and serialization.
"""

from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from uuid import UUID
from pydantic import BaseModel, Field, validator

from app.schemas.base import BaseResponse


class KnowledgeQueryRequest(BaseModel):
    """Schema for knowledge query requests."""
    query: str = Field(..., min_length=1, max_length=2000, description="Natural language question")
    context: Optional[Dict[str, Any]] = Field(None, description="Additional context for the query")
    max_results: int = Field(5, ge=1, le=20, description="Maximum number of source documents to return")
    include_sources: bool = Field(True, description="Whether to include source documents")
    confidence_threshold: float = Field(0.5, ge=0.0, le=1.0, description="Minimum confidence threshold")
    department_filter: Optional[List[UUID]] = Field(None, description="Filter by specific departments")
    document_types: Optional[List[str]] = Field(None, description="Filter by document types")
    date_range: Optional[Dict[str, str]] = Field(None, description="Filter by date range")
    
    @validator('query')
    def validate_query(cls, v):
        # Remove excessive whitespace
        v = ' '.join(v.split())
        if len(v.strip()) == 0:
            raise ValueError('Query cannot be empty')
        return v
    
    @validator('date_range')
    def validate_date_range(cls, v):
        if v:
            if 'start' in v or 'end' in v:
                # Basic date format validation
                from datetime import datetime
                try:
                    if 'start' in v:
                        datetime.fromisoformat(v['start'].replace('Z', '+00:00'))
                    if 'end' in v:
                        datetime.fromisoformat(v['end'].replace('Z', '+00:00'))
                except ValueError:
                    raise ValueError('Invalid date format. Use ISO format (YYYY-MM-DDTHH:MM:SS)')
        return v


class KnowledgeSourceDocument(BaseModel):
    """Schema for source document information in knowledge responses."""
    document_id: UUID
    title: str
    excerpt: str
    relevance_score: float
    document_type: str
    category: Optional[str] = None
    url: str
    created_at: datetime
    metadata: Optional[Dict[str, Any]] = None


class KnowledgeQueryResponse(BaseModel):
    """Schema for knowledge query responses."""
    query_id: UUID
    query: str
    answer: str
    confidence_score: float
    sources: List[KnowledgeSourceDocument]
    processing_time: float
    model_used: str
    suggestions: Optional[List[str]] = None
    related_queries: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None
    generated_at: datetime


class KnowledgeQueryFeedback(BaseModel):
    """Schema for knowledge query feedback."""
    query_id: UUID
    feedback_type: str = Field(..., regex="^(helpful|not_helpful|incorrect|incomplete)$")
    rating: Optional[int] = Field(None, ge=1, le=5, description="Rating from 1-5")
    comment: Optional[str] = Field(None, max_length=1000, description="Additional feedback")
    suggested_answer: Optional[str] = Field(None, max_length=2000, description="User's suggested answer")


class KnowledgeQueryFeedbackResponse(BaseModel):
    """Schema for feedback submission responses."""
    feedback_id: UUID
    query_id: UUID
    message: str
    submitted_at: datetime


class KnowledgeSuggestionRequest(BaseModel):
    """Schema for knowledge suggestion requests."""
    partial_query: str = Field(..., min_length=1, max_length=200)
    limit: int = Field(10, ge=1, le=20)
    include_popular: bool = Field(True, description="Include popular queries")
    include_similar: bool = Field(True, description="Include similar queries")


class KnowledgeSuggestionResponse(BaseModel):
    """Schema for knowledge suggestion responses."""
    suggestions: List[Dict[str, Any]]
    query: str
    generated_at: datetime


class KnowledgeHistoryRequest(BaseModel):
    """Schema for knowledge query history requests."""
    limit: int = Field(50, ge=1, le=200)
    skip: int = Field(0, ge=0)
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    include_feedback: bool = Field(False, description="Include feedback information")


class KnowledgeHistoryItem(BaseModel):
    """Schema for individual knowledge history items."""
    query_id: UUID
    query: str
    answer: str
    confidence_score: float
    source_count: int
    processing_time: float
    has_feedback: bool
    created_at: datetime


class KnowledgeHistoryResponse(BaseResponse):
    """Schema for knowledge query history responses."""
    queries: List[KnowledgeHistoryItem]
    total: int
    skip: int
    limit: int
    has_more: bool


class KnowledgeAnalyticsRequest(BaseModel):
    """Schema for knowledge analytics requests."""
    period: str = Field("30d", regex="^(1d|7d|30d|90d|1y)$")
    group_by: str = Field("day", regex="^(hour|day|week|month)$")
    include_trends: bool = Field(True, description="Include trend analysis")
    include_popular: bool = Field(True, description="Include popular queries")


class KnowledgeAnalyticsResponse(BaseModel):
    """Schema for knowledge analytics responses."""
    period: str
    total_queries: int
    average_confidence: float
    average_response_time: float
    popular_queries: List[Dict[str, Any]]
    query_trends: List[Dict[str, Any]]
    confidence_distribution: Dict[str, int]
    response_time_distribution: Dict[str, int]
    generated_at: datetime


class KnowledgeExportRequest(BaseModel):
    """Schema for knowledge export requests."""
    format: str = Field("json", regex="^(json|csv|xlsx)$")
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    include_sources: bool = Field(True, description="Include source documents")
    include_feedback: bool = Field(True, description="Include feedback data")
    department_filter: Optional[List[UUID]] = None


class KnowledgeExportResponse(BaseModel):
    """Schema for knowledge export responses."""
    export_id: UUID
    format: str
    status: str
    download_url: Optional[str] = None
    expires_at: Optional[datetime] = None
    created_at: datetime


class KnowledgeConfigurationRequest(BaseModel):
    """Schema for knowledge system configuration requests."""
    confidence_threshold: Optional[float] = Field(None, ge=0.0, le=1.0)
    max_sources: Optional[int] = Field(None, ge=1, le=50)
    enable_suggestions: Optional[bool] = None
    enable_related_queries: Optional[bool] = None
    response_language: Optional[str] = Field(None, regex="^[a-z]{2}$")
    custom_prompts: Optional[Dict[str, str]] = None


class KnowledgeConfigurationResponse(BaseModel):
    """Schema for knowledge system configuration responses."""
    confidence_threshold: float
    max_sources: int
    enable_suggestions: bool
    enable_related_queries: bool
    response_language: str
    custom_prompts: Dict[str, str]
    updated_at: datetime
    updated_by: UUID


class KnowledgeSearchRequest(BaseModel):
    """Schema for knowledge-based search requests."""
    query: str = Field(..., min_length=1, max_length=1000)
    search_type: str = Field("hybrid", regex="^(semantic|keyword|hybrid)$")
    filters: Optional[Dict[str, Any]] = None
    limit: int = Field(20, ge=1, le=100)
    include_snippets: bool = Field(True, description="Include content snippets")
    highlight_terms: bool = Field(True, description="Highlight matching terms")


class KnowledgeSearchResult(BaseModel):
    """Schema for knowledge search results."""
    document_id: UUID
    title: str
    snippet: str
    relevance_score: float
    document_type: str
    category: Optional[str] = None
    highlights: List[str]
    url: str
    created_at: datetime


class KnowledgeSearchResponse(BaseModel):
    """Schema for knowledge search responses."""
    results: List[KnowledgeSearchResult]
    total: int
    search_time: float
    search_type: str
    query: str
    suggestions: List[str]
    facets: Dict[str, Any]


class KnowledgeRelatedRequest(BaseModel):
    """Schema for related content requests."""
    document_id: Optional[UUID] = None
    query: Optional[str] = None
    limit: int = Field(5, ge=1, le=20)
    similarity_threshold: float = Field(0.6, ge=0.0, le=1.0)


class KnowledgeRelatedResponse(BaseModel):
    """Schema for related content responses."""
    related_documents: List[KnowledgeSourceDocument]
    related_queries: List[str]
    similarity_method: str
    generated_at: datetime


class KnowledgeStatsResponse(BaseModel):
    """Schema for knowledge system statistics."""
    total_queries: int
    total_documents: int
    average_confidence: float
    average_response_time: float
    popular_categories: List[Dict[str, Any]]
    recent_activity: List[Dict[str, Any]]
    system_health: Dict[str, Any]
    generated_at: datetime
