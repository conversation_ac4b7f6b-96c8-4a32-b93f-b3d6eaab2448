# KnowledgeBot - Corporate Memory System

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Python 3.9+](https://img.shields.io/badge/python-3.9+-blue.svg)](https://www.python.org/downloads/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-green.svg)](https://fastapi.tiangolo.com/)
[![React](https://img.shields.io/badge/React-18+-blue.svg)](https://reactjs.org/)
[![Docker](https://img.shields.io/badge/Docker-ready-blue.svg)](https://www.docker.com/)

## 🚀 Overview

KnowledgeBot is an enterprise-grade Corporate Memory System that transforms how organizations capture, store, and retrieve institutional knowledge. Built with MemVid-based encoding technology, it provides intelligent knowledge management with departmental isolation, real-time search, and seamless integration with existing enterprise workflows.

### 🎯 Pain Points Solved

- **Knowledge Silos**: Breaks down departmental barriers while maintaining security boundaries
- **Information Loss**: Prevents critical knowledge from walking out the door with departing employees
- **Search Inefficiency**: Eliminates time wasted searching through scattered documents and systems
- **Compliance Gaps**: Ensures audit trails and regulatory compliance for knowledge access
- **Integration Complexity**: Seamlessly connects with existing enterprise tools (Slack, Teams, SSO)
- **Scalability Issues**: Handles enterprise-scale document volumes with real-time processing

## Recommended Alternative Domain Names
Here are 10 strategic domain alternatives that should be available, brandable, and under $20/year:

- corpknowledge.ai - Enterprise-focused, modern AI extension
- memvidsys.com - Highlights the MemVid technology differentiator
- enterprisememory.io - Tech-forward, describes core function
- knowledgevault.pro - Professional, secure connotation
- smartcorpmem.com - Combines intelligence + corporate memory
- orgknowledge.app - Modern app extension, organizational focus
- corpmemorysys.com - Clear enterprise memory system branding
- knowledgecore.biz - Business-focused, central system implication
- memvidbot.com - Unique MemVid + bot combination
- enterpriseiq.systems - Intelligence quotient + systems focus

## Branding Recommendations:

- Top Choice: corpknowledge.ai - Perfect balance of enterprise focus and AI positioning
- Tech-Forward: memvidsys.com - Highlights unique MemVid differentiator
- Professional: knowledgevault.pro - Conveys security and professionalism

## SEO Benefits:

- All suggestions include relevant keywords for enterprise knowledge management
- Modern TLDs (.ai, .io, .app) signal innovation
- Professional TLDs (.pro, .biz, .systems) convey enterprise credibility

## 🏗️ System Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        A[React Admin Dashboard]
        B[Slack Bot]
        C[Teams Bot]
        D[Mobile App]
    end
    
    subgraph "API Gateway"
        E[FastAPI Backend]
        F[Authentication Service]
        G[Rate Limiting]
    end
    
    subgraph "Core Services"
        H[Document Ingestion Pipeline]
        I[MemVid Encoding Engine]
        J[Knowledge Search Service]
        K[User Management Service]
        L[Audit Logging Service]
    end
    
    subgraph "Data Layer"
        M[PostgreSQL Database]
        N[Vector Database]
        O[Document Storage]
        P[Cache Layer]
    end
    
    subgraph "External Integrations"
        Q[SSO/SAML Provider]
        R[Enterprise File Systems]
        S[Third-party APIs]
    end
    
    A --> E
    B --> E
    C --> E
    D --> E
    
    E --> F
    E --> G
    E --> H
    E --> I
    E --> J
    E --> K
    E --> L
    
    H --> M
    I --> N
    J --> N
    K --> M
    L --> M
    
    H --> O
    J --> P
    
    F --> Q
    H --> R
    E --> S
```

## 🔄 User Workflow

```mermaid
sequenceDiagram
    participant U as User
    participant B as Bot/Dashboard
    participant API as FastAPI Backend
    participant M as MemVid Engine
    participant DB as Database
    participant V as Vector Store
    
    Note over U,V: Document Upload Workflow
    U->>B: Upload Document
    B->>API: POST /documents/upload
    API->>M: Process & Encode Document
    M->>V: Store Vector Embeddings
    API->>DB: Store Metadata
    API->>B: Return Success
    B->>U: Confirmation
    
    Note over U,V: Knowledge Query Workflow
    U->>B: Ask Question
    B->>API: POST /knowledge/query
    API->>V: Vector Similarity Search
    V->>API: Return Relevant Chunks
    API->>M: Generate Contextual Answer
    API->>DB: Log Query & Response
    API->>B: Return Answer
    B->>U: Display Answer + Sources
```

## 📁 Project Structure

```
corpknowledge/
├── backend/                          # FastAPI Backend
│   ├── app/
│   │   ├── api/                      # API Routes
│   │   │   ├── v1/
│   │   │   │   ├── auth.py
│   │   │   │   ├── documents.py
│   │   │   │   ├── knowledge.py
│   │   │   │   ├── users.py
│   │   │   │   └── admin.py
│   │   │   └── deps.py               # Dependencies
│   │   ├── core/                     # Core Configuration
│   │   │   ├── config.py
│   │   │   ├── security.py
│   │   │   └── database.py
│   │   ├── models/                   # Database Models
│   │   │   ├── user.py
│   │   │   ├── document.py
│   │   │   ├── knowledge.py
│   │   │   └── audit.py
│   │   ├── services/                 # Business Logic
│   │   │   ├── auth_service.py
│   │   │   ├── document_service.py
│   │   │   ├── memvid_service.py
│   │   │   ├── search_service.py
│   │   │   └── integration_service.py
│   │   ├── schemas/                  # Pydantic Schemas
│   │   │   ├── user.py
│   │   │   ├── document.py
│   │   │   └── knowledge.py
│   │   └── main.py                   # FastAPI App
│   ├── alembic/                      # Database Migrations
│   ├── tests/                        # Backend Tests
│   ├── requirements.txt
│   └── Dockerfile
├── frontend/                         # React Admin Dashboard
│   ├── src/
│   │   ├── components/               # Reusable Components
│   │   ├── pages/                    # Page Components
│   │   ├── services/                 # API Services
│   │   ├── hooks/                    # Custom Hooks
│   │   ├── utils/                    # Utilities
│   │   └── App.tsx
│   ├── public/
│   ├── package.json
│   └── Dockerfile
├── bots/                            # Chat Bot Integrations
│   ├── slack/
│   │   ├── bot.py
│   │   ├── handlers/
│   │   └── requirements.txt
│   ├── teams/
│   │   ├── bot.py
│   │   ├── handlers/
│   │   └── requirements.txt
│   └── shared/
│       └── bot_utils.py
├── infrastructure/                   # Deployment Configs
│   ├── docker/
│   │   └── docker-compose.yml
│   ├── kubernetes/
│   │   ├── namespace.yaml
│   │   ├── configmap.yaml
│   │   ├── secrets.yaml
│   │   ├── backend-deployment.yaml
│   │   ├── frontend-deployment.yaml
│   │   └── ingress.yaml
│   └── terraform/                    # Infrastructure as Code
├── docs/                            # Documentation
│   ├── api/                         # API Documentation
│   ├── deployment/                  # Deployment Guides
│   └── user-guides/                 # User Documentation
├── scripts/                         # Utility Scripts
│   ├── setup.sh
│   ├── migrate.sh
│   └── backup.sh
├── .github/                         # GitHub Actions
│   └── workflows/
│       ├── ci.yml
│       ├── cd.yml
│       └── security.yml
├── .env.example                     # Environment Template
├── docker-compose.yml               # Local Development
├── README.md
└── LICENSE
```

## 🚀 Quick Start

### Prerequisites

- Python 3.9+
- Node.js 18+
- Docker & Docker Compose
- PostgreSQL 14+

### Local Development Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/HectorTa1989/corpknowledge.git
   cd corpknowledge
   ```

2. **Environment Configuration**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start with Docker Compose**
   ```bash
   docker-compose up -d
   ```

4. **Access the Application**
   - Admin Dashboard: http://localhost:3000
   - API Documentation: http://localhost:8000/docs
   - Health Check: http://localhost:8000/health

### Manual Setup

1. **Backend Setup**
   ```bash
   cd backend
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt
   alembic upgrade head
   uvicorn app.main:app --reload
   ```

2. **Frontend Setup**
   ```bash
   cd frontend
   npm install
   npm start
   ```

## 📚 API Documentation

### Authentication Endpoints
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/logout` - User logout
- `POST /api/v1/auth/refresh` - Refresh token
- `GET /api/v1/auth/me` - Get current user

### Document Management
- `POST /api/v1/documents/upload` - Upload document
- `GET /api/v1/documents` - List documents
- `GET /api/v1/documents/{id}` - Get document details
- `DELETE /api/v1/documents/{id}` - Delete document

### Knowledge Query
- `POST /api/v1/knowledge/query` - Query knowledge base
- `GET /api/v1/knowledge/search` - Search documents
- `GET /api/v1/knowledge/suggestions` - Get query suggestions

### User Management
- `GET /api/v1/users` - List users (admin)
- `POST /api/v1/users` - Create user (admin)
- `PUT /api/v1/users/{id}` - Update user
- `DELETE /api/v1/users/{id}` - Delete user (admin)

## 🔧 Production Deployment

### Kubernetes Deployment

1. **Create Namespace and Secrets**
   ```bash
   kubectl apply -f infrastructure/kubernetes/namespace.yaml
   kubectl apply -f infrastructure/kubernetes/secrets.yaml
   kubectl apply -f infrastructure/kubernetes/configmap.yaml
   ```

2. **Deploy Database and Storage**
   ```bash
   # PostgreSQL
   helm install postgresql bitnami/postgresql -n knowledgebot

   # Redis
   helm install redis bitnami/redis -n knowledgebot

   # MinIO (S3-compatible storage)
   helm install minio bitnami/minio -n knowledgebot
   ```

3. **Deploy Application Services**
   ```bash
   kubectl apply -f infrastructure/kubernetes/backend-deployment.yaml
   kubectl apply -f infrastructure/kubernetes/frontend-deployment.yaml
   kubectl apply -f infrastructure/kubernetes/ingress.yaml
   ```

### Environment Variables

```bash
# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/knowledgebot
REDIS_URL=redis://localhost:6379/0

# Authentication
JWT_SECRET_KEY=your-super-secret-jwt-key
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# MemVid Configuration
MEMVID_API_KEY=your-memvid-api-key
MEMVID_MODEL=enterprise-v1

# Vector Database
PINECONE_API_KEY=your-pinecone-api-key
PINECONE_ENVIRONMENT=us-west1-gcp

# Storage
S3_BUCKET_NAME=knowledgebot-documents
S3_ACCESS_KEY=your-s3-access-key
S3_SECRET_KEY=your-s3-secret-key

# SSO Configuration
SAML_ENTITY_ID=knowledgebot
SAML_SSO_URL=https://your-idp.com/sso
SAML_X509_CERT=your-certificate

# Slack Integration
SLACK_BOT_TOKEN=xoxb-your-slack-bot-token
SLACK_SIGNING_SECRET=your-slack-signing-secret

# Teams Integration
TEAMS_APP_ID=your-teams-app-id
TEAMS_APP_PASSWORD=your-teams-app-password
```

## 🧪 Testing

### Backend Testing
```bash
cd backend
pytest tests/ -v --cov=app --cov-report=html
```

### Frontend Testing
```bash
cd frontend
npm test -- --coverage --watchAll=false
```

### Integration Testing
```bash
docker-compose -f docker-compose.test.yml up --abort-on-container-exit
```

### Load Testing
```bash
# Install k6
brew install k6  # macOS
# or
sudo apt install k6  # Ubuntu

# Run load tests
k6 run tests/load/api-load-test.js
```

## 🔒 Security Features

- **Enterprise SSO/SAML Integration**
- **Role-Based Access Control (RBAC)**
- **Departmental Data Isolation**
- **End-to-End Encryption**
- **Audit Logging & Compliance**
- **Rate Limiting & DDoS Protection**
- **Input Sanitization & Validation**
- **Secure File Upload with Virus Scanning**

## 📊 Monitoring & Analytics

### Metrics Dashboard
- User engagement analytics
- Document processing metrics
- Query response times
- System resource utilization
- Error rates and alerts

### Health Checks
- `/health` - Basic health status
- `/health/detailed` - Comprehensive system status
- `/metrics` - Prometheus metrics endpoint

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guidelines](CONTRIBUTING.md) for details.

### Development Workflow

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes and add tests
4. Ensure all tests pass: `npm test` and `pytest`
5. Commit your changes: `git commit -m 'Add amazing feature'`
6. Push to the branch: `git push origin feature/amazing-feature`
7. Open a Pull Request

### Code Standards

- Python: Follow PEP 8, use Black formatter
- TypeScript/React: Follow Airbnb style guide, use Prettier
- Commit messages: Use Conventional Commits format
- Tests: Maintain >90% code coverage

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- 📧 Email: <EMAIL>
- 💬 Slack: [Join our community](https://slack.corpknowledge.ai)
- 📖 Documentation: [docs.corpknowledge.ai](https://docs.corpknowledge.ai)
- 🐛 Issues: [GitHub Issues](https://github.com/HectorTa1989/corpknowledge/issues)

---

**Built with ❤️ by Hector Ta**
