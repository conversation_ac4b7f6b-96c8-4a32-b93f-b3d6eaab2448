# KnowledgeBot - Corporate Memory System
# Production Docker Compose Configuration
# Optimized for production deployment with security and performance

version: '3.8'

services:
  # PostgreSQL Database with optimizations
  postgres:
    image: postgres:15-alpine
    container_name: knowledgebot-postgres-prod
    environment:
      POSTGRES_DB: knowledgebot
      POSTGRES_USER: knowledgebot
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
      - ./postgres/postgresql.conf:/etc/postgresql/postgresql.conf:ro
    command: postgres -c config_file=/etc/postgresql/postgresql.conf
    networks:
      - knowledgebot-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U knowledgebot -d knowledgebot"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Redis with persistence and security
  redis:
    image: redis:7-alpine
    container_name: knowledgebot-redis-prod
    command: redis-server /usr/local/etc/redis/redis.conf
    environment:
      REDIS_PASSWORD: ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
    networks:
      - knowledgebot-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # MinIO with SSL and security
  minio:
    image: minio/minio:latest
    container_name: knowledgebot-minio-prod
    command: server /data --console-address ":9001" --certs-dir /root/.minio/certs
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD}
      MINIO_BROWSER_REDIRECT_URL: https://${DOMAIN}/minio
    volumes:
      - minio_data:/data
      - ./minio/certs:/root/.minio/certs:ro
    networks:
      - knowledgebot-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # FastAPI Backend - Production optimized
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
      args:
        - ENVIRONMENT=production
    container_name: knowledgebot-backend-prod
    environment:
      # Database
      DATABASE_URL: postgresql+asyncpg://knowledgebot:${POSTGRES_PASSWORD}@postgres:5432/knowledgebot
      DATABASE_POOL_SIZE: 20
      DATABASE_MAX_OVERFLOW: 30
      
      # Redis
      REDIS_URL: redis://:${REDIS_PASSWORD}@redis:6379/0
      
      # Security
      JWT_SECRET_KEY: ${JWT_SECRET_KEY}
      JWT_ALGORITHM: HS256
      ACCESS_TOKEN_EXPIRE_MINUTES: 15
      REFRESH_TOKEN_EXPIRE_DAYS: 7
      
      # MemVid Configuration
      MEMVID_MODEL_PATH: ./models/memvid
      MEMVID_MODEL_NAME: sentence-transformers/all-MiniLM-L6-v2
      MEMVID_DEVICE: ${MEMVID_DEVICE:-cpu}
      MEMVID_BATCH_SIZE: 64
      
      # Storage
      STORAGE_TYPE: minio
      MINIO_ENDPOINT: minio:9000
      MINIO_ACCESS_KEY: ${MINIO_ROOT_USER}
      MINIO_SECRET_KEY: ${MINIO_ROOT_PASSWORD}
      MINIO_BUCKET_NAME: knowledgebot-documents
      MINIO_SECURE: true
      
      # Features
      ENABLE_VIRUS_SCANNING: true
      ENABLE_DOCUMENT_OCR: true
      ENABLE_DATA_ENCRYPTION: true
      ENABLE_AUDIT_LOGGING: true
      
      # Performance
      WORKERS_PER_CORE: 2
      MAX_WORKERS: 8
      WEB_CONCURRENCY: 8
      WORKER_TIMEOUT: 120
      KEEPALIVE: 2
      
      # Logging
      LOG_LEVEL: INFO
      LOG_FORMAT: json
      ENABLE_ACCESS_LOG: true
      
      # CORS
      BACKEND_CORS_ORIGINS: '["https://${DOMAIN}"]'
      
      # Environment
      ENVIRONMENT: production
      DEBUG: false
      
      # Monitoring
      ENABLE_METRICS: true
      METRICS_PORT: 9090
    volumes:
      - backend_storage:/app/storage
      - backend_models:/app/models
      - backend_logs:/app/logs
    networks:
      - knowledgebot-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
    restart: unless-stopped
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 2G
          cpus: '1.0'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "5"

  # Celery Worker - Production scaled
  celery-worker:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    deploy:
      replicas: 3
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    command: celery -A app.core.celery worker --loglevel=info --concurrency=4 --max-tasks-per-child=1000
    environment:
      # Same as backend but optimized for workers
      DATABASE_URL: postgresql+asyncpg://knowledgebot:${POSTGRES_PASSWORD}@postgres:5432/knowledgebot
      REDIS_URL: redis://:${REDIS_PASSWORD}@redis:6379/0
      JWT_SECRET_KEY: ${JWT_SECRET_KEY}
      MEMVID_MODEL_PATH: ./models/memvid
      MEMVID_MODEL_NAME: sentence-transformers/all-MiniLM-L6-v2
      MEMVID_DEVICE: ${MEMVID_DEVICE:-cpu}
      MEMVID_BATCH_SIZE: 32
      STORAGE_TYPE: minio
      MINIO_ENDPOINT: minio:9000
      MINIO_ACCESS_KEY: ${MINIO_ROOT_USER}
      MINIO_SECRET_KEY: ${MINIO_ROOT_PASSWORD}
      MINIO_BUCKET_NAME: knowledgebot-documents
      MINIO_SECURE: true
      ENABLE_VIRUS_SCANNING: true
      ENABLE_DOCUMENT_OCR: true
      LOG_LEVEL: INFO
      ENVIRONMENT: production
      WORKER_PREFETCH_MULTIPLIER: 1
      WORKER_MAX_MEMORY_PER_CHILD: 200000
    volumes:
      - backend_storage:/app/storage
      - backend_models:/app/models
      - backend_logs:/app/logs
    networks:
      - knowledgebot-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "celery", "-A", "app.core.celery", "inspect", "ping"]
      interval: 60s
      timeout: 10s
      retries: 3
      start_period: 60s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Celery Beat Scheduler
  celery-beat:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    container_name: knowledgebot-celery-beat-prod
    command: celery -A app.core.celery beat --loglevel=info --pidfile=/tmp/celerybeat.pid
    environment:
      DATABASE_URL: postgresql+asyncpg://knowledgebot:${POSTGRES_PASSWORD}@postgres:5432/knowledgebot
      REDIS_URL: redis://:${REDIS_PASSWORD}@redis:6379/0
      JWT_SECRET_KEY: ${JWT_SECRET_KEY}
      LOG_LEVEL: INFO
      ENVIRONMENT: production
    volumes:
      - backend_logs:/app/logs
      - celery_beat_data:/tmp
    networks:
      - knowledgebot-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
        reservations:
          memory: 128M
          cpus: '0.1'
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # React Frontend - Production build
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        - VITE_API_BASE_URL=https://${DOMAIN}/api/v1
        - VITE_APP_VERSION=${APP_VERSION:-1.0.0}
        - VITE_APP_ENVIRONMENT=production
    container_name: knowledgebot-frontend-prod
    environment:
      VITE_API_BASE_URL: https://${DOMAIN}/api/v1
      VITE_APP_VERSION: ${APP_VERSION:-1.0.0}
      VITE_APP_ENVIRONMENT: production
    networks:
      - knowledgebot-network
    depends_on:
      backend:
        condition: service_healthy
    restart: unless-stopped
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Nginx Load Balancer and SSL Termination
  nginx:
    image: nginx:alpine
    container_name: knowledgebot-nginx-prod
    volumes:
      - ./nginx/nginx.prod.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./nginx/logs:/var/log/nginx
    ports:
      - "80:80"
      - "443:443"
    networks:
      - knowledgebot-network
    depends_on:
      - frontend
      - backend
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.25'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "5"

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  minio_data:
    driver: local
  backend_storage:
    driver: local
  backend_models:
    driver: local
  backend_logs:
    driver: local
  celery_beat_data:
    driver: local

networks:
  knowledgebot-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
