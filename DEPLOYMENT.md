# 🚀 KnowledgeBot Deployment Guide

Complete deployment guide for the KnowledgeBot Corporate Memory System in production environments.

## 📋 Prerequisites

### System Requirements
- **OS**: Ubuntu 20.04+ / CentOS 8+ / RHEL 8+
- **CPU**: 4+ cores (8+ recommended)
- **RAM**: 8GB minimum (16GB+ recommended)
- **Storage**: 50GB+ available space
- **Network**: Public IP with ports 80/443 accessible

### Software Requirements
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **Git**: 2.0+
- **OpenSSL**: For certificate generation
- **Curl**: For health checks

## 🔧 Quick Deployment

### 1. Clone Repository
```bash
git clone https://github.com/your-org/knowledgebot.git
cd knowledgebot
```

### 2. Configure Environment
```bash
# Copy and edit production environment
cp .env.example .env.prod
nano .env.prod
```

**Required Configuration:**
```bash
# Domain Configuration
DOMAIN=your-domain.com
SSL_EMAIL=<EMAIL>

# Security (auto-generated if not set)
POSTGRES_PASSWORD=your-secure-password
REDIS_PASSWORD=your-secure-password
MINIO_ROOT_PASSWORD=your-secure-password
JWT_SECRET_KEY=your-jwt-secret-key

# AI Configuration
MEMVID_DEVICE=cpu  # or 'cuda' for GPU acceleration
```

### 3. Deploy
```bash
# Make deployment script executable
chmod +x deploy.sh

# Run full deployment
./deploy.sh deploy
```

### 4. Access Application
- **Frontend**: https://your-domain.com
- **API Docs**: https://your-domain.com/docs
- **Admin Panel**: https://your-domain.com/admin

**Default Admin Credentials:**
- Email: `<EMAIL>`
- Password: `Admin123!`

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     Nginx       │    │    Frontend     │    │    Backend      │
│  Load Balancer  │◄──►│   React App     │◄──►│   FastAPI       │
│   SSL Termination│    │   (Port 3000)   │    │   (Port 8000)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                                              │
         ▼                                              ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   PostgreSQL    │    │     Redis       │    │     MinIO       │
│   Database      │    │     Cache       │    │  Object Storage │
│   (Port 5432)   │    │   (Port 6379)   │    │   (Port 9000)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Celery Workers  │    │  Celery Beat    │    │   Monitoring    │
│ Background Tasks│    │   Scheduler     │    │   & Logging     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🔐 Security Configuration

### SSL/TLS Setup
The deployment script automatically handles SSL certificate generation using Let's Encrypt:

```bash
# Manual SSL setup (if needed)
sudo certbot certonly --standalone -d your-domain.com
```

### Firewall Configuration
```bash
# Ubuntu/Debian
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable

# CentOS/RHEL
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

### Security Headers
Nginx is configured with security headers:
- Content Security Policy (CSP)
- X-Frame-Options
- X-Content-Type-Options
- X-XSS-Protection
- Strict-Transport-Security

## 📊 Monitoring & Maintenance

### Health Checks
```bash
# Check all services
./deploy.sh health

# View service status
docker-compose -f docker-compose.prod.yml ps

# View logs
./deploy.sh logs [service-name]
```

### Backup & Restore
```bash
# Create backup
./deploy.sh backup

# Restore from backup
docker-compose -f docker-compose.prod.yml exec postgres \
  psql -U knowledgebot -d knowledgebot < backup.sql
```

### Updates
```bash
# Pull latest changes
git pull origin main

# Rebuild and restart services
docker-compose -f docker-compose.prod.yml build --no-cache
docker-compose -f docker-compose.prod.yml up -d
```

## 🔧 Configuration Options

### Environment Variables

#### Database Configuration
```bash
DATABASE_URL=postgresql+asyncpg://user:pass@host:port/db
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30
```

#### Redis Configuration
```bash
REDIS_URL=redis://:password@host:port/db
REDIS_MAX_CONNECTIONS=100
```

#### AI Configuration
```bash
MEMVID_MODEL_NAME=sentence-transformers/all-MiniLM-L6-v2
MEMVID_DEVICE=cpu  # or cuda
MEMVID_BATCH_SIZE=32
```

#### Storage Configuration
```bash
STORAGE_TYPE=minio  # or local, s3
MINIO_ENDPOINT=minio:9000
MINIO_BUCKET_NAME=knowledgebot-documents
```

### Performance Tuning

#### Backend Scaling
```bash
# Scale backend instances
docker-compose -f docker-compose.prod.yml up -d --scale backend=3

# Scale Celery workers
docker-compose -f docker-compose.prod.yml up -d --scale celery-worker=5
```

#### Database Optimization
```bash
# PostgreSQL configuration in postgres/postgresql.conf
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB
```

#### Redis Optimization
```bash
# Redis configuration in redis/redis.conf
maxmemory 512mb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
```

## 🚨 Troubleshooting

### Common Issues

#### Services Not Starting
```bash
# Check logs
docker-compose -f docker-compose.prod.yml logs

# Check system resources
docker system df
docker system prune
```

#### Database Connection Issues
```bash
# Check database status
docker-compose -f docker-compose.prod.yml exec postgres pg_isready

# Reset database connection
docker-compose -f docker-compose.prod.yml restart postgres backend
```

#### SSL Certificate Issues
```bash
# Renew certificates
sudo certbot renew

# Copy renewed certificates
sudo cp /etc/letsencrypt/live/your-domain.com/* ./nginx/ssl/
```

#### Memory Issues
```bash
# Check memory usage
docker stats

# Increase swap space
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

### Log Locations
- **Application Logs**: `./backend/logs/`
- **Nginx Logs**: `./nginx/logs/`
- **Docker Logs**: `docker-compose logs [service]`

## 📈 Scaling

### Horizontal Scaling
```bash
# Scale specific services
docker-compose -f docker-compose.prod.yml up -d --scale backend=3
docker-compose -f docker-compose.prod.yml up -d --scale celery-worker=5
docker-compose -f docker-compose.prod.yml up -d --scale frontend=2
```

### Load Balancing
Nginx is configured to load balance between multiple backend instances automatically.

### Database Scaling
For high-load scenarios, consider:
- Read replicas for PostgreSQL
- Redis clustering
- Database connection pooling

## 🔄 CI/CD Integration

### GitHub Actions Example
```yaml
name: Deploy KnowledgeBot
on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Deploy to production
        run: |
          ssh user@server 'cd /opt/knowledgebot && git pull && ./deploy.sh'
```

### Automated Backups
```bash
# Add to crontab
0 2 * * * /opt/knowledgebot/deploy.sh backup
```

## 📞 Support

### Getting Help
- **Documentation**: Check `/docs` directory
- **Logs**: Use `./deploy.sh logs` for debugging
- **Health Check**: Use `./deploy.sh health`
- **Issues**: GitHub Issues for bug reports

### Emergency Procedures
```bash
# Stop all services
./deploy.sh stop

# Emergency restart
docker-compose -f docker-compose.prod.yml restart

# Rollback (if needed)
git checkout previous-commit
./deploy.sh deploy
```

---

**🎉 Congratulations! Your KnowledgeBot Corporate Memory System is now deployed and ready for enterprise use.**
