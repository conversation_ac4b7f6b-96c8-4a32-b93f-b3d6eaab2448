"""
KnowledgeBot Backend - Authentication Service Tests
Unit tests for authentication service functionality.
"""

import pytest
from unittest.mock import AsyncMock, patch
from datetime import datetime, timedelta

from app.services.auth_service import AuthService
from app.models.user import User
from app.core.security import verify_password, get_password_hash
from app.schemas.auth import LoginRequest, RegisterRequest


class TestAuthService:
    """Test cases for AuthService."""
    
    @pytest.fixture
    def auth_service(self):
        """Create AuthService instance."""
        return AuthService()
    
    @pytest.mark.asyncio
    async def test_authenticate_user_success(self, auth_service, db_session):
        """Test successful user authentication."""
        # Create test user
        hashed_password = get_password_hash("testpassword")
        user = User(
            email="<EMAIL>",
            username="testuser",
            hashed_password=hashed_password,
            is_active=True,
            is_verified=True
        )
        db_session.add(user)
        await db_session.commit()
        
        # Test authentication
        authenticated_user = await auth_service.authenticate_user(
            db_session, "<EMAIL>", "testpassword"
        )
        
        assert authenticated_user is not None
        assert authenticated_user.email == "<EMAIL>"
        assert authenticated_user.username == "testuser"
    
    @pytest.mark.asyncio
    async def test_authenticate_user_wrong_password(self, auth_service, db_session):
        """Test authentication with wrong password."""
        # Create test user
        hashed_password = get_password_hash("testpassword")
        user = User(
            email="<EMAIL>",
            username="testuser",
            hashed_password=hashed_password,
            is_active=True,
            is_verified=True
        )
        db_session.add(user)
        await db_session.commit()
        
        # Test authentication with wrong password
        authenticated_user = await auth_service.authenticate_user(
            db_session, "<EMAIL>", "wrongpassword"
        )
        
        assert authenticated_user is None
    
    @pytest.mark.asyncio
    async def test_authenticate_user_not_found(self, auth_service, db_session):
        """Test authentication with non-existent user."""
        authenticated_user = await auth_service.authenticate_user(
            db_session, "<EMAIL>", "password"
        )
        
        assert authenticated_user is None
    
    @pytest.mark.asyncio
    async def test_authenticate_user_inactive(self, auth_service, db_session):
        """Test authentication with inactive user."""
        # Create inactive test user
        hashed_password = get_password_hash("testpassword")
        user = User(
            email="<EMAIL>",
            username="testuser",
            hashed_password=hashed_password,
            is_active=False,
            is_verified=True
        )
        db_session.add(user)
        await db_session.commit()
        
        # Test authentication
        authenticated_user = await auth_service.authenticate_user(
            db_session, "<EMAIL>", "testpassword"
        )
        
        assert authenticated_user is None
    
    @pytest.mark.asyncio
    async def test_create_user_success(self, auth_service, db_session):
        """Test successful user creation."""
        user_data = RegisterRequest(
            email="<EMAIL>",
            username="newuser",
            password="newpassword",
            first_name="New",
            last_name="User"
        )
        
        created_user = await auth_service.create_user(db_session, user_data)
        
        assert created_user is not None
        assert created_user.email == "<EMAIL>"
        assert created_user.username == "newuser"
        assert created_user.first_name == "New"
        assert created_user.last_name == "User"
        assert created_user.is_active is True
        assert created_user.is_verified is False
        assert verify_password("newpassword", created_user.hashed_password)
    
    @pytest.mark.asyncio
    async def test_create_user_duplicate_email(self, auth_service, db_session):
        """Test user creation with duplicate email."""
        # Create first user
        user1 = User(
            email="<EMAIL>",
            username="user1",
            hashed_password=get_password_hash("password"),
            is_active=True
        )
        db_session.add(user1)
        await db_session.commit()
        
        # Try to create user with same email
        user_data = RegisterRequest(
            email="<EMAIL>",
            username="user2",
            password="password",
            first_name="User",
            last_name="Two"
        )
        
        with pytest.raises(ValueError, match="Email already registered"):
            await auth_service.create_user(db_session, user_data)
    
    @pytest.mark.asyncio
    async def test_create_user_duplicate_username(self, auth_service, db_session):
        """Test user creation with duplicate username."""
        # Create first user
        user1 = User(
            email="<EMAIL>",
            username="testuser",
            hashed_password=get_password_hash("password"),
            is_active=True
        )
        db_session.add(user1)
        await db_session.commit()
        
        # Try to create user with same username
        user_data = RegisterRequest(
            email="<EMAIL>",
            username="testuser",
            password="password",
            first_name="User",
            last_name="Two"
        )
        
        with pytest.raises(ValueError, match="Username already taken"):
            await auth_service.create_user(db_session, user_data)
    
    @pytest.mark.asyncio
    async def test_verify_email_success(self, auth_service, db_session):
        """Test successful email verification."""
        # Create unverified user
        user = User(
            email="<EMAIL>",
            username="testuser",
            hashed_password=get_password_hash("password"),
            is_active=True,
            is_verified=False
        )
        db_session.add(user)
        await db_session.commit()
        
        # Verify email
        result = await auth_service.verify_email(db_session, user.id)
        
        assert result is True
        await db_session.refresh(user)
        assert user.is_verified is True
    
    @pytest.mark.asyncio
    async def test_verify_email_user_not_found(self, auth_service, db_session):
        """Test email verification with non-existent user."""
        from uuid import uuid4
        
        result = await auth_service.verify_email(db_session, uuid4())
        
        assert result is False
    
    @pytest.mark.asyncio
    @patch('app.services.auth_service.send_verification_email')
    async def test_send_verification_email(self, mock_send_email, auth_service, test_user):
        """Test sending verification email."""
        mock_send_email.return_value = True
        
        result = await auth_service.send_verification_email(test_user)
        
        assert result is True
        mock_send_email.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_reset_password_success(self, auth_service, db_session):
        """Test successful password reset."""
        # Create test user
        user = User(
            email="<EMAIL>",
            username="testuser",
            hashed_password=get_password_hash("oldpassword"),
            is_active=True,
            is_verified=True
        )
        db_session.add(user)
        await db_session.commit()
        
        # Reset password
        result = await auth_service.reset_password(
            db_session, user.id, "newpassword"
        )
        
        assert result is True
        await db_session.refresh(user)
        assert verify_password("newpassword", user.hashed_password)
    
    @pytest.mark.asyncio
    async def test_reset_password_user_not_found(self, auth_service, db_session):
        """Test password reset with non-existent user."""
        from uuid import uuid4
        
        result = await auth_service.reset_password(
            db_session, uuid4(), "newpassword"
        )
        
        assert result is False
