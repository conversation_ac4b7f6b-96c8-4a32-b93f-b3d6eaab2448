/**
 * KnowledgeBot - Corporate Memory System
 * API Client
 * 
 * Centralized HTTP client with authentication, error handling,
 * and request/response interceptors for the KnowledgeBot API.
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios'
import toast from 'react-hot-toast'
import { tokenStorage } from '@/utils/tokenStorage'

// API configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api/v1'
const REQUEST_TIMEOUT = 30000 // 30 seconds

// Create axios instance
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: REQUEST_TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor to add authentication token
apiClient.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    const token = tokenStorage.getAccessToken()
    if (token) {
      config.headers = {
        ...config.headers,
        Authorization: `Bearer ${token}`,
      }
    }

    // Add request timestamp for debugging
    config.metadata = { startTime: new Date() }

    return config
  },
  (error: AxiosError) => {
    return Promise.reject(error)
  }
)

// Response interceptor for error handling and token refresh
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    // Add response time for debugging
    const endTime = new Date()
    const startTime = response.config.metadata?.startTime
    if (startTime) {
      const duration = endTime.getTime() - startTime.getTime()
      console.debug(`API Request: ${response.config.method?.toUpperCase()} ${response.config.url} - ${duration}ms`)
    }

    return response
  },
  async (error: AxiosError) => {
    const originalRequest = error.config

    // Handle 401 Unauthorized errors
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true

      try {
        // Try to refresh the token
        const refreshToken = tokenStorage.getRefreshToken()
        if (refreshToken) {
          const response = await axios.post(`${API_BASE_URL}/auth/refresh`, {
            refresh_token: refreshToken,
          })

          const { access_token, refresh_token: newRefreshToken } = response.data
          tokenStorage.setTokens(access_token, newRefreshToken)

          // Retry the original request with new token
          originalRequest.headers.Authorization = `Bearer ${access_token}`
          return apiClient(originalRequest)
        }
      } catch (refreshError) {
        // Refresh failed, clear tokens and redirect to login
        tokenStorage.clearTokens()
        window.location.href = '/login'
        return Promise.reject(refreshError)
      }
    }

    // Handle other error responses
    handleApiError(error)
    return Promise.reject(error)
  }
)

/**
 * Handle API errors with user-friendly messages
 */
function handleApiError(error: AxiosError) {
  const response = error.response
  const request = error.request

  if (response) {
    // Server responded with error status
    const status = response.status
    const data = response.data as any

    switch (status) {
      case 400:
        toast.error(data?.detail || 'Bad request. Please check your input.')
        break
      case 401:
        toast.error('Authentication required. Please log in.')
        break
      case 403:
        toast.error('Access denied. You do not have permission to perform this action.')
        break
      case 404:
        toast.error('Resource not found.')
        break
      case 409:
        toast.error(data?.detail || 'Conflict. The resource already exists.')
        break
      case 422:
        // Validation errors
        if (data?.detail && Array.isArray(data.detail)) {
          const validationErrors = data.detail
            .map((err: any) => `${err.loc?.join('.')}: ${err.msg}`)
            .join(', ')
          toast.error(`Validation error: ${validationErrors}`)
        } else {
          toast.error(data?.detail || 'Validation error.')
        }
        break
      case 429:
        toast.error('Too many requests. Please try again later.')
        break
      case 500:
        toast.error('Internal server error. Please try again later.')
        break
      case 502:
        toast.error('Bad gateway. The server is temporarily unavailable.')
        break
      case 503:
        toast.error('Service unavailable. Please try again later.')
        break
      default:
        toast.error(data?.detail || `An error occurred (${status})`)
    }
  } else if (request) {
    // Request was made but no response received
    if (error.code === 'ECONNABORTED') {
      toast.error('Request timeout. Please try again.')
    } else if (error.code === 'ERR_NETWORK') {
      toast.error('Network error. Please check your connection.')
    } else {
      toast.error('Unable to connect to the server. Please try again.')
    }
  } else {
    // Something else happened
    toast.error('An unexpected error occurred.')
  }

  // Log error for debugging
  console.error('API Error:', {
    message: error.message,
    status: response?.status,
    data: response?.data,
    url: error.config?.url,
    method: error.config?.method,
  })
}

/**
 * Upload file with progress tracking
 */
export function uploadFile(
  url: string,
  file: File,
  onProgress?: (progress: number) => void,
  additionalData?: Record<string, any>
): Promise<AxiosResponse> {
  const formData = new FormData()
  formData.append('file', file)

  // Add additional form data if provided
  if (additionalData) {
    Object.entries(additionalData).forEach(([key, value]) => {
      formData.append(key, typeof value === 'string' ? value : JSON.stringify(value))
    })
  }

  return apiClient.post(url, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    onUploadProgress: (progressEvent) => {
      if (onProgress && progressEvent.total) {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        onProgress(progress)
      }
    },
  })
}

/**
 * Download file with proper handling
 */
export async function downloadFile(
  url: string,
  filename?: string
): Promise<void> {
  try {
    const response = await apiClient.get(url, {
      responseType: 'blob',
    })

    // Create blob URL and trigger download
    const blob = new Blob([response.data])
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename || 'download'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(downloadUrl)
  } catch (error) {
    toast.error('Failed to download file')
    throw error
  }
}

/**
 * Health check endpoint
 */
export async function healthCheck(): Promise<boolean> {
  try {
    await apiClient.get('/health')
    return true
  } catch (error) {
    return false
  }
}

// Export the configured axios instance
export { apiClient }

// Export types for use in other services
export type { AxiosResponse, AxiosError }
