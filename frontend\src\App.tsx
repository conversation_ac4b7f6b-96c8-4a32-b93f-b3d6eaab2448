import React, { Suspense } from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { Helmet } from 'react-helmet-async'

import { useAuth } from './hooks/useAuth'
import { useTheme } from './hooks/useTheme'
import Layout from './components/layout/Layout'
import LoadingSpinner from './components/common/LoadingSpinner'
import ProtectedRoute from './components/auth/ProtectedRoute'

// Lazy load pages for better performance
const LoginPage = React.lazy(() => import('./pages/auth/LoginPage'))
const RegisterPage = React.lazy(() => import('./pages/auth/RegisterPage'))
const ForgotPasswordPage = React.lazy(() => import('./pages/auth/ForgotPasswordPage'))
const ResetPasswordPage = React.lazy(() => import('./pages/auth/ResetPasswordPage'))
const VerifyEmailPage = React.lazy(() => import('./pages/auth/VerifyEmailPage'))

const DashboardPage = React.lazy(() => import('./pages/dashboard/DashboardPage'))
const DocumentsPage = React.lazy(() => import('./pages/documents/DocumentsPage'))
const DocumentViewPage = React.lazy(() => import('./pages/documents/DocumentViewPage'))
const DocumentUploadPage = React.lazy(() => import('./pages/documents/DocumentUploadPage'))
const KnowledgePage = React.lazy(() => import('./pages/knowledge/KnowledgePage'))
const SearchPage = React.lazy(() => import('./pages/search/SearchPage'))
const AnalyticsPage = React.lazy(() => import('./pages/analytics/AnalyticsPage'))
const ProfilePage = React.lazy(() => import('./pages/profile/ProfilePage'))
const SettingsPage = React.lazy(() => import('./pages/settings/SettingsPage'))

// Admin pages
const AdminDashboardPage = React.lazy(() => import('./pages/admin/AdminDashboardPage'))
const UsersManagementPage = React.lazy(() => import('./pages/admin/UsersManagementPage'))
const RolesManagementPage = React.lazy(() => import('./pages/admin/RolesManagementPage'))
const DepartmentsManagementPage = React.lazy(() => import('./pages/admin/DepartmentsManagementPage'))
const SystemHealthPage = React.lazy(() => import('./pages/admin/SystemHealthPage'))
const AuditLogsPage = React.lazy(() => import('./pages/admin/AuditLogsPage'))

const NotFoundPage = React.lazy(() => import('./pages/error/NotFoundPage'))

function App() {
  const { user, isLoading } = useAuth()
  const { theme } = useTheme()

  // Apply theme class to document
  React.useEffect(() => {
    const root = document.documentElement
    if (theme === 'dark') {
      root.classList.add('dark')
    } else {
      root.classList.remove('dark')
    }
  }, [theme])

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <>
      <Helmet>
        <title>KnowledgeBot - Corporate Memory System</title>
        <meta name="description" content="Enterprise knowledge management system for corporate memory and document intelligence" />
      </Helmet>

      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <Suspense fallback={<LoadingSpinner size="lg" className="min-h-screen" />}>
          <Routes>
            {/* Public routes */}
            <Route
              path="/login"
              element={
                user ? <Navigate to="/dashboard" replace /> : <LoginPage />
              }
            />
            <Route
              path="/register"
              element={
                user ? <Navigate to="/dashboard" replace /> : <RegisterPage />
              }
            />
            <Route
              path="/forgot-password"
              element={
                user ? <Navigate to="/dashboard" replace /> : <ForgotPasswordPage />
              }
            />
            <Route
              path="/reset-password"
              element={
                user ? <Navigate to="/dashboard" replace /> : <ResetPasswordPage />
              }
            />
            <Route
              path="/verify-email"
              element={<VerifyEmailPage />}
            />

            {/* Protected routes */}
            <Route
              path="/"
              element={
                <ProtectedRoute>
                  <Layout />
                </ProtectedRoute>
              }
            >
              {/* Redirect root to dashboard */}
              <Route index element={<Navigate to="/dashboard" replace />} />

              {/* Main application routes */}
              <Route path="dashboard" element={<DashboardPage />} />

              {/* Document management */}
              <Route path="documents" element={<DocumentsPage />} />
              <Route path="documents/upload" element={<DocumentUploadPage />} />
              <Route path="documents/:id" element={<DocumentViewPage />} />

              {/* Knowledge and search */}
              <Route path="knowledge" element={<KnowledgePage />} />
              <Route path="search" element={<SearchPage />} />

              {/* Analytics */}
              <Route path="analytics" element={<AnalyticsPage />} />

              {/* User profile and settings */}
              <Route path="profile" element={<ProfilePage />} />
              <Route path="settings" element={<SettingsPage />} />


              {/* Admin routes */}
              <Route
                path="admin"
                element={
                  <ProtectedRoute requiredPermissions={['admin.stats.read']}>
                    <AdminDashboardPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="admin/users"
                element={
                  <ProtectedRoute requiredPermissions={['users.read']}>
                    <UsersManagementPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="admin/roles"
                element={
                  <ProtectedRoute requiredPermissions={['admin.roles.read']}>
                    <RolesManagementPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="admin/departments"
                element={
                  <ProtectedRoute requiredPermissions={['admin.departments.read']}>
                    <DepartmentsManagementPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="admin/health"
                element={
                  <ProtectedRoute requiredPermissions={['system.health.read']}>
                    <SystemHealthPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="admin/audit"
                element={
                  <ProtectedRoute requiredPermissions={['admin.stats.read']}>
                    <AuditLogsPage />
                  </ProtectedRoute>
                }
              />
            </Route>

            {/* 404 page */}
            <Route path="*" element={<NotFoundPage />} />
          </Routes>
        </Suspense>
      </div>
    </>
  )
}

export default App
