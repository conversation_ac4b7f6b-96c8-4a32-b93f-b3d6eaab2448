# KnowledgeBot Backend

Enterprise-grade corporate memory system backend built with FastAPI, PostgreSQL, and open-source AI technologies.

## Features

### 🔐 Enterprise Security
- JWT-based authentication with refresh tokens
- Role-based access control (RBAC) with granular permissions
- Department-based document access control
- SSO integration (SAML, OAuth, OIDC)
- Rate limiting and API security
- Comprehensive audit logging

### 📄 Document Management
- Multi-format document processing (PDF, DOCX, PPTX, HTML, TXT, Images)
- Virus scanning and security validation
- Automatic text extraction and OCR support
- Document versioning and metadata management
- Bulk operations and batch processing

### 🧠 Knowledge Processing
- **Local MemVid Implementation**: Uses open-source sentence transformers (no API keys required)
- Vector embeddings for semantic search
- Hybrid search (vector + full-text)
- Automatic document summarization
- Knowledge query answering with confidence scoring

### 📊 Analytics & Monitoring
- Comprehensive usage analytics
- Performance monitoring and health checks
- User activity tracking
- Department-level reporting
- Real-time metrics and alerts

### 🏗️ Enterprise Architecture
- Async/await throughout for high performance
- Horizontal scaling support
- Multiple storage backends (S3, <PERSON><PERSON>, Local)
- Redis caching and session management
- Celery background task processing
- Comprehensive error handling and logging

## Technology Stack

- **Framework**: FastAPI 0.104+
- **Database**: PostgreSQL 15+ with async SQLAlchemy
- **Cache**: Redis 7+
- **AI/ML**: Sentence Transformers (local), PyTorch
- **Storage**: S3/MinIO/Local filesystem
- **Task Queue**: Celery with Redis broker
- **Security**: Passlib, python-jose, bcrypt
- **Monitoring**: Prometheus metrics, structured logging

## Quick Start

### Prerequisites

- Python 3.11+
- PostgreSQL 15+
- Redis 7+
- Docker & Docker Compose (recommended)

### Development Setup

1. **Clone and setup environment**:
```bash
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

2. **Configure environment**:
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. **Start dependencies with Docker**:
```bash
docker-compose up -d postgres redis minio
```

4. **Run database migrations**:
```bash
alembic upgrade head
```

5. **Start the development server**:
```bash
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

6. **Start background workers**:
```bash
celery -A app.core.celery worker --loglevel=info
```

### Docker Development

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f backend

# Run migrations
docker-compose exec backend alembic upgrade head

# Access the API
curl http://localhost:8000/api/v1/health
```

## Configuration

### Environment Variables

Key configuration options in `.env`:

```bash
# Database
DATABASE_URL=postgresql+asyncpg://user:pass@localhost:5432/knowledgebot

# Redis
REDIS_URL=redis://localhost:6379/0

# Security
JWT_SECRET_KEY=your-secret-key
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# MemVid (Local - No API Key Required)
MEMVID_MODEL_PATH=./models/memvid
MEMVID_MODEL_NAME=sentence-transformers/all-MiniLM-L6-v2
MEMVID_DEVICE=cpu

# Storage
STORAGE_TYPE=local  # or s3, minio
LOCAL_STORAGE_PATH=./storage
S3_BUCKET_NAME=knowledgebot-docs

# Features
ENABLE_VIRUS_SCANNING=true
ENABLE_DOCUMENT_OCR=true
ENABLE_DATA_ENCRYPTION=true
```

### Model Configuration

The system uses local sentence transformers by default:

- **Default Model**: `sentence-transformers/all-MiniLM-L6-v2`
- **No API Keys Required**: Everything runs locally
- **GPU Support**: Set `MEMVID_DEVICE=cuda` if available
- **Custom Models**: Change `MEMVID_MODEL_NAME` to any compatible model

## API Documentation

### Interactive Documentation

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI JSON**: http://localhost:8000/openapi.json

### Authentication

```bash
# Login
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "Admin123!"}'

# Use token
curl -H "Authorization: Bearer YOUR_TOKEN" \
  "http://localhost:8000/api/v1/users/me"
```

### Key Endpoints

- **Authentication**: `/api/v1/auth/*`
- **Users**: `/api/v1/users/*`
- **Documents**: `/api/v1/documents/*`
- **Knowledge**: `/api/v1/knowledge/*`
- **Search**: `/api/v1/search/*`
- **Analytics**: `/api/v1/analytics/*`
- **Admin**: `/api/v1/admin/*`
- **Health**: `/api/v1/health/*`

## Database

### Migrations

```bash
# Create new migration
alembic revision --autogenerate -m "Description"

# Apply migrations
alembic upgrade head

# Rollback
alembic downgrade -1
```

### Default Data

The system creates default:
- **Admin User**: `<EMAIL>` / `Admin123!`
- **Roles**: superuser, admin, manager, user, guest
- **Permissions**: Granular permissions for all features
- **Departments**: general, it, hr, finance, marketing

## Development

### Code Structure

```
app/
├── api/                 # API routes and endpoints
│   ├── deps.py         # Dependency injection
│   └── v1/             # API version 1
├── core/               # Core functionality
│   ├── config.py       # Configuration
│   ├── database.py     # Database setup
│   ├── security.py     # Authentication & security
│   └── celery.py       # Background tasks
├── models/             # SQLAlchemy models
├── schemas/            # Pydantic schemas
├── services/           # Business logic
└── main.py            # FastAPI application
```

### Testing

```bash
# Install test dependencies
pip install -r requirements-test.txt

# Run tests
pytest

# Run with coverage
pytest --cov=app --cov-report=html

# Run specific test
pytest tests/test_auth.py::test_login
```

### Code Quality

```bash
# Format code
black app/
isort app/

# Lint code
flake8 app/
mypy app/

# Security check
bandit -r app/
```

## Deployment

### Production Configuration

1. **Environment Variables**:
   - Set strong `JWT_SECRET_KEY`
   - Configure production database
   - Set up proper storage (S3/MinIO)
   - Enable security features

2. **Database**:
   - Use managed PostgreSQL service
   - Enable connection pooling
   - Set up read replicas for scaling

3. **Caching**:
   - Use managed Redis service
   - Configure Redis clustering for HA

4. **Storage**:
   - Use S3 or compatible service
   - Enable encryption at rest
   - Set up proper backup policies

### Docker Production

```bash
# Build production image
docker build -f Dockerfile.prod -t knowledgebot-backend .

# Run with production compose
docker-compose -f docker-compose.prod.yml up -d
```

### Kubernetes

```bash
# Apply Kubernetes manifests
kubectl apply -f infrastructure/kubernetes/

# Check deployment
kubectl get pods -l app=knowledgebot-backend
```

## Monitoring

### Health Checks

- **Basic**: `/api/v1/health/`
- **Detailed**: `/api/v1/health/detailed`
- **Kubernetes Probes**: `/api/v1/health/liveness`, `/api/v1/health/readiness`

### Metrics

- **Prometheus**: Metrics exposed at `/metrics`
- **Custom Metrics**: Application-specific metrics
- **Performance**: Response times, error rates, throughput

### Logging

- **Structured Logging**: JSON format for production
- **Log Levels**: DEBUG, INFO, WARNING, ERROR, CRITICAL
- **Correlation IDs**: Request tracing across services

## Security

### Best Practices

- **Authentication**: JWT with short expiration + refresh tokens
- **Authorization**: Role-based with granular permissions
- **Input Validation**: Pydantic schemas for all inputs
- **SQL Injection**: Parameterized queries with SQLAlchemy
- **XSS Protection**: Input sanitization and output encoding
- **CSRF Protection**: SameSite cookies and CSRF tokens
- **Rate Limiting**: Per-user and global rate limits
- **Audit Logging**: All sensitive operations logged

### Virus Scanning

- **ClamAV Integration**: Automatic virus scanning
- **File Quarantine**: Infected files isolated
- **Signature Updates**: Regular virus definition updates

## Performance

### Optimization

- **Async/Await**: Non-blocking I/O throughout
- **Connection Pooling**: Database connection reuse
- **Caching**: Redis for sessions and frequent queries
- **Background Tasks**: Celery for heavy operations
- **Pagination**: Efficient large dataset handling

### Scaling

- **Horizontal Scaling**: Stateless application design
- **Load Balancing**: Multiple backend instances
- **Database Scaling**: Read replicas and sharding
- **Caching Strategy**: Multi-level caching

## Troubleshooting

### Common Issues

1. **Database Connection**:
   ```bash
   # Check connection
   docker-compose exec backend python -c "from app.core.database import engine; print('OK')"
   ```

2. **Redis Connection**:
   ```bash
   # Test Redis
   docker-compose exec redis redis-cli ping
   ```

3. **Model Loading**:
   ```bash
   # Check model path
   ls -la ./models/memvid/
   ```

4. **Permissions**:
   ```bash
   # Check file permissions
   chmod -R 755 ./storage/
   ```

### Logs

```bash
# Application logs
docker-compose logs -f backend

# Database logs
docker-compose logs -f postgres

# Worker logs
docker-compose logs -f celery-worker
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run quality checks
6. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

- **Documentation**: Check the `/docs` endpoint
- **Issues**: GitHub Issues
- **Security**: Report security issues privately
