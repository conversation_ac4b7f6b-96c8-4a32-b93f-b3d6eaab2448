"""
KnowledgeBot - Corporate Memory System
MemVid Service - Local Implementation

This module implements MemVid functionality using open-source sentence transformers
for document encoding and knowledge retrieval without requiring external API keys.
"""

import asyncio
import logging
import hashlib
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity
import torch
import re
from pathlib import Path

from app.core.config import settings

logger = logging.getLogger(__name__)


class MemVidService:
    """
    Local MemVid implementation using sentence transformers for knowledge encoding.
    """
    
    def __init__(self):
        self.model = None
        self.device = settings.MEMVID_DEVICE
        self.model_name = settings.MEMVID_MODEL_NAME
        self.max_chunk_size = settings.MEMVID_MAX_CHUNK_SIZE
        self.overlap_size = settings.MEMVID_OVERLAP_SIZE
        self.batch_size = settings.MEMVID_BATCH_SIZE
        self._initialize_model()
    
    def _initialize_model(self):
        """Initialize the sentence transformer model."""
        try:
            logger.info(f"Loading MemVid model: {self.model_name}")
            
            # Create model directory if it doesn't exist
            model_path = Path(settings.MEMVID_MODEL_PATH)
            model_path.mkdir(parents=True, exist_ok=True)
            
            # Load or download model
            self.model = SentenceTransformer(
                self.model_name,
                cache_folder=str(model_path),
                device=self.device
            )
            
            logger.info(f"MemVid model loaded successfully on {self.device}")
            
        except Exception as e:
            logger.error(f"Failed to initialize MemVid model: {e}")
            raise
    
    async def generate_embedding(self, text: str) -> List[float]:
        """
        Generate vector embedding for text.
        
        Args:
            text: Input text to encode
            
        Returns:
            List[float]: Vector embedding
        """
        try:
            if not self.model:
                raise ValueError("MemVid model not initialized")
            
            # Clean and preprocess text
            cleaned_text = self._preprocess_text(text)
            
            # Generate embedding in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            embedding = await loop.run_in_executor(
                None, 
                self._generate_embedding_sync, 
                cleaned_text
            )
            
            return embedding.tolist()
            
        except Exception as e:
            logger.error(f"Error generating embedding: {e}")
            raise
    
    def _generate_embedding_sync(self, text: str) -> np.ndarray:
        """Synchronous embedding generation."""
        return self.model.encode([text], convert_to_numpy=True)[0]
    
    async def process_document(
        self, 
        content: str, 
        document_id: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Process document content into chunks with embeddings.
        
        Args:
            content: Document text content
            document_id: Document identifier
            metadata: Additional metadata
            
        Returns:
            List[Dict[str, Any]]: List of processed chunks with embeddings
        """
        try:
            # Split content into chunks
            chunks = self._split_into_chunks(content)
            
            # Process chunks in batches
            processed_chunks = []
            for i in range(0, len(chunks), self.batch_size):
                batch = chunks[i:i + self.batch_size]
                batch_embeddings = await self._process_chunk_batch(batch)
                
                for j, (chunk_text, embedding) in enumerate(zip(batch, batch_embeddings)):
                    chunk_data = {
                        "content": chunk_text,
                        "embedding": embedding,
                        "chunk_index": i + j,
                        "document_id": document_id,
                        "model": self.model_name,
                        "created_at": datetime.utcnow().isoformat(),
                        "metadata": metadata or {}
                    }
                    processed_chunks.append(chunk_data)
            
            logger.info(f"Processed {len(processed_chunks)} chunks for document {document_id}")
            return processed_chunks
            
        except Exception as e:
            logger.error(f"Error processing document {document_id}: {e}")
            raise
    
    async def _process_chunk_batch(self, chunks: List[str]) -> List[List[float]]:
        """Process a batch of chunks to generate embeddings."""
        loop = asyncio.get_event_loop()
        embeddings = await loop.run_in_executor(
            None,
            self._generate_batch_embeddings_sync,
            chunks
        )
        return [emb.tolist() for emb in embeddings]
    
    def _generate_batch_embeddings_sync(self, chunks: List[str]) -> np.ndarray:
        """Synchronous batch embedding generation."""
        cleaned_chunks = [self._preprocess_text(chunk) for chunk in chunks]
        return self.model.encode(cleaned_chunks, convert_to_numpy=True)
    
    def _split_into_chunks(self, content: str) -> List[str]:
        """
        Split content into overlapping chunks.
        
        Args:
            content: Text content to split
            
        Returns:
            List[str]: List of text chunks
        """
        # Split by sentences first
        sentences = self._split_sentences(content)
        
        chunks = []
        current_chunk = []
        current_length = 0
        
        for sentence in sentences:
            sentence_length = len(sentence.split())
            
            # If adding this sentence would exceed max chunk size
            if current_length + sentence_length > self.max_chunk_size and current_chunk:
                # Create chunk from current sentences
                chunk_text = " ".join(current_chunk)
                chunks.append(chunk_text)
                
                # Start new chunk with overlap
                overlap_sentences = current_chunk[-self.overlap_size:] if len(current_chunk) > self.overlap_size else current_chunk
                current_chunk = overlap_sentences + [sentence]
                current_length = sum(len(s.split()) for s in current_chunk)
            else:
                current_chunk.append(sentence)
                current_length += sentence_length
        
        # Add final chunk
        if current_chunk:
            chunk_text = " ".join(current_chunk)
            chunks.append(chunk_text)
        
        return chunks
    
    def _split_sentences(self, text: str) -> List[str]:
        """Split text into sentences."""
        # Simple sentence splitting - could be enhanced with NLTK or spaCy
        sentences = re.split(r'[.!?]+', text)
        return [s.strip() for s in sentences if s.strip()]
    
    def _preprocess_text(self, text: str) -> str:
        """
        Preprocess text for embedding generation.
        
        Args:
            text: Raw text
            
        Returns:
            str: Cleaned text
        """
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove special characters but keep punctuation
        text = re.sub(r'[^\w\s.,!?;:()\-\'"]+', '', text)
        
        # Truncate if too long
        words = text.split()
        if len(words) > 512:  # Model context limit
            text = ' '.join(words[:512])
        
        return text.strip()
    
    async def generate_answer(
        self, 
        question: str, 
        context: str, 
        max_length: int = 500
    ) -> Dict[str, Any]:
        """
        Generate answer using context (simplified implementation).
        
        Args:
            question: User question
            context: Relevant context from documents
            max_length: Maximum answer length
            
        Returns:
            Dict[str, Any]: Generated answer with metadata
        """
        try:
            # For now, return a structured response with the most relevant context
            # In a full implementation, this would use a generative model
            
            # Extract most relevant sentences from context
            context_sentences = self._split_sentences(context)
            
            # Simple relevance scoring based on keyword overlap
            question_words = set(question.lower().split())
            scored_sentences = []
            
            for sentence in context_sentences:
                sentence_words = set(sentence.lower().split())
                overlap = len(question_words.intersection(sentence_words))
                if overlap > 0:
                    scored_sentences.append((sentence, overlap))
            
            # Sort by relevance and take top sentences
            scored_sentences.sort(key=lambda x: x[1], reverse=True)
            top_sentences = [s[0] for s in scored_sentences[:3]]
            
            answer = " ".join(top_sentences)
            
            # Truncate if too long
            if len(answer) > max_length:
                words = answer.split()
                answer = " ".join(words[:max_length//5]) + "..."
            
            confidence_score = min(len(scored_sentences) / 10.0, 1.0)
            
            return {
                "answer": answer or "I couldn't find a specific answer to your question in the available documents.",
                "confidence_score": confidence_score,
                "processing_time": 0.1,  # Placeholder
                "model_used": self.model_name
            }
            
        except Exception as e:
            logger.error(f"Error generating answer: {e}")
            return {
                "answer": "I encountered an error while processing your question.",
                "confidence_score": 0.0,
                "processing_time": 0.0,
                "model_used": self.model_name
            }
    
    async def generate_summary(
        self, 
        content: str, 
        max_length: int = 300,
        document_title: str = ""
    ) -> Dict[str, Any]:
        """
        Generate document summary (simplified implementation).
        
        Args:
            content: Document content
            max_length: Maximum summary length
            document_title: Document title for context
            
        Returns:
            Dict[str, Any]: Summary with metadata
        """
        try:
            # Simple extractive summarization
            sentences = self._split_sentences(content)
            
            if len(sentences) <= 3:
                summary_text = content[:max_length]
            else:
                # Take first, middle, and last sentences as a simple summary
                summary_sentences = [
                    sentences[0],
                    sentences[len(sentences)//2],
                    sentences[-1]
                ]
                summary_text = " ".join(summary_sentences)
            
            # Extract key points (first sentence of each paragraph)
            paragraphs = content.split('\n\n')
            key_points = []
            for para in paragraphs[:5]:  # Limit to 5 key points
                if para.strip():
                    first_sentence = self._split_sentences(para)[0] if self._split_sentences(para) else para[:100]
                    key_points.append(first_sentence)
            
            return {
                "text": summary_text[:max_length],
                "key_points": key_points,
                "confidence_score": 0.8,  # Placeholder
                "generated_at": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error generating summary: {e}")
            return {
                "text": "Unable to generate summary.",
                "key_points": [],
                "confidence_score": 0.0,
                "generated_at": datetime.utcnow().isoformat()
            }
    
    async def calculate_similarity(
        self, 
        embedding1: List[float], 
        embedding2: List[float]
    ) -> float:
        """
        Calculate cosine similarity between two embeddings.
        
        Args:
            embedding1: First embedding vector
            embedding2: Second embedding vector
            
        Returns:
            float: Similarity score (0-1)
        """
        try:
            emb1 = np.array(embedding1).reshape(1, -1)
            emb2 = np.array(embedding2).reshape(1, -1)
            
            similarity = cosine_similarity(emb1, emb2)[0][0]
            return float(similarity)
            
        except Exception as e:
            logger.error(f"Error calculating similarity: {e}")
            return 0.0
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the loaded model."""
        return {
            "model_name": self.model_name,
            "device": self.device,
            "max_chunk_size": self.max_chunk_size,
            "overlap_size": self.overlap_size,
            "batch_size": self.batch_size,
            "embedding_dimension": self.model.get_sentence_embedding_dimension() if self.model else None
        }
