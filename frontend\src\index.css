@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    @apply bg-gray-50 text-gray-900;
  }

  /* Dark mode styles */
  .dark body {
    @apply bg-gray-900 text-gray-100;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    @apply w-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100 dark:bg-gray-800;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 dark:bg-gray-600 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400 dark:bg-gray-500;
  }

  /* Focus styles */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-gray-900;
  }

  /* Selection styles */
  ::selection {
    @apply bg-primary-100 text-primary-900;
  }

  .dark ::selection {
    @apply bg-primary-800 text-primary-100;
  }
}

@layer components {
  /* Button variants */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus-ring disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 active:bg-primary-800 shadow-sm;
  }

  .btn-secondary {
    @apply btn bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 active:bg-gray-100 shadow-sm dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700;
  }

  .btn-ghost {
    @apply btn text-gray-700 hover:bg-gray-100 active:bg-gray-200 dark:text-gray-300 dark:hover:bg-gray-800 dark:active:bg-gray-700;
  }

  .btn-danger {
    @apply btn bg-red-600 text-white hover:bg-red-700 active:bg-red-800 shadow-sm;
  }

  .btn-success {
    @apply btn bg-green-600 text-white hover:bg-green-700 active:bg-green-800 shadow-sm;
  }

  /* Input styles */
  .input {
    @apply block w-full px-3 py-2 text-sm border border-gray-300 rounded-lg bg-white placeholder-gray-400 focus-ring dark:bg-gray-800 dark:border-gray-600 dark:text-gray-100 dark:placeholder-gray-500;
  }

  .input-error {
    @apply input border-red-300 focus:ring-red-500 dark:border-red-600;
  }

  /* Card styles */
  .card {
    @apply bg-white rounded-xl border border-gray-200 shadow-sm dark:bg-gray-800 dark:border-gray-700;
  }

  .card-header {
    @apply px-6 py-4 border-b border-gray-200 dark:border-gray-700;
  }

  .card-body {
    @apply px-6 py-4;
  }

  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 dark:border-gray-700;
  }

  /* Badge styles */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 text-xs font-medium rounded-full;
  }

  .badge-primary {
    @apply badge bg-primary-100 text-primary-800 dark:bg-primary-800 dark:text-primary-100;
  }

  .badge-secondary {
    @apply badge bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100;
  }

  .badge-success {
    @apply badge bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100;
  }

  .badge-warning {
    @apply badge bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100;
  }

  .badge-error {
    @apply badge bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100;
  }

  /* Loading spinner */
  .spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-600;
  }

  /* Skeleton loading */
  .skeleton {
    @apply animate-pulse bg-gray-200 dark:bg-gray-700 rounded;
  }

  /* Divider */
  .divider {
    @apply border-t border-gray-200 dark:border-gray-700;
  }

  /* Text styles */
  .text-muted {
    @apply text-gray-600 dark:text-gray-400;
  }

  .text-subtle {
    @apply text-gray-500 dark:text-gray-500;
  }

  /* Link styles */
  .link {
    @apply text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 transition-colors duration-200;
  }

  /* Table styles */
  .table {
    @apply min-w-full divide-y divide-gray-200 dark:divide-gray-700;
  }

  .table-header {
    @apply bg-gray-50 dark:bg-gray-800;
  }

  .table-header-cell {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider;
  }

  .table-row {
    @apply bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors duration-200;
  }

  .table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100;
  }

  /* Form styles */
  .form-group {
    @apply space-y-1;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 dark:text-gray-300;
  }

  .form-error {
    @apply text-sm text-red-600 dark:text-red-400;
  }

  .form-help {
    @apply text-sm text-gray-500 dark:text-gray-400;
  }

  /* Modal styles */
  .modal-overlay {
    @apply fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-50;
  }

  .modal-content {
    @apply bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-lg w-full mx-4;
  }

  /* Dropdown styles */
  .dropdown-menu {
    @apply absolute right-0 mt-2 w-56 origin-top-right bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50;
  }

  .dropdown-item {
    @apply block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200;
  }

  /* Sidebar styles */
  .sidebar {
    @apply bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700;
  }

  .sidebar-item {
    @apply flex items-center px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors duration-200;
  }

  .sidebar-item-active {
    @apply sidebar-item bg-primary-50 dark:bg-primary-900 text-primary-700 dark:text-primary-300;
  }

  /* Progress bar */
  .progress-bar {
    @apply w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2;
  }

  .progress-fill {
    @apply bg-primary-600 h-2 rounded-full transition-all duration-300;
  }

  /* Tooltip */
  .tooltip {
    @apply absolute z-50 px-2 py-1 text-xs text-white bg-gray-900 dark:bg-gray-700 rounded shadow-lg;
  }
}

@layer utilities {
  /* Custom utilities */
  .text-balance {
    text-wrap: balance;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-slide-in {
    animation: slideIn 0.3s ease-out;
  }

  /* Glass morphism effect */
  .glass {
    @apply bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border border-white/20 dark:border-gray-700/20;
  }

  /* Gradient text */
  .gradient-text {
    @apply bg-gradient-to-r from-primary-600 to-purple-600 bg-clip-text text-transparent;
  }
}
