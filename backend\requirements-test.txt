# KnowledgeBot Backend - Test Dependencies
# Testing framework and utilities for comprehensive test coverage

# Core testing framework
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.12.0
pytest-xdist==3.5.0  # Parallel test execution
pytest-html==4.1.1   # HTML test reports
pytest-json-report==1.5.0  # JSON test reports

# HTTP testing
httpx==0.25.2
requests-mock==1.11.0

# Database testing
pytest-postgresql==5.0.0
sqlalchemy-utils==0.41.1

# Factory and fixtures
factory-boy==3.3.0
faker==20.1.0

# Mocking and patching
responses==0.24.1
freezegun==1.2.2  # Time mocking
pytest-freezegun==0.4.2

# Performance testing
pytest-benchmark==4.0.0

# Security testing
bandit==1.7.5
safety==2.3.5

# Code quality
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# Environment and configuration
python-dotenv==1.0.0

# File and data testing
pillow==10.1.0  # Image testing
openpyxl==3.1.2  # Excel file testing
python-docx==1.1.0  # Word document testing

# Redis testing
fakeredis==2.20.1

# Email testing
aiosmtpd==1.4.4.post2

# Async testing utilities
asynctest==0.13.0
pytest-asyncio-cooperative==0.21.0

# Load testing
locust==2.17.0

# API testing
tavern==2.4.1

# Snapshot testing
syrupy==4.6.0

# Property-based testing
hypothesis==6.92.1
