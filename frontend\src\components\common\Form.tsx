/**
 * KnowledgeBot - Corporate Memory System
 * Form Components
 * 
 * Reusable form components with validation, error handling,
 * and accessibility features.
 */

import React, { forwardRef } from 'react'
import { clsx } from 'clsx'
import { ExclamationCircleIcon, EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline'

// Base Input Component
export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string
  error?: string
  helpText?: string
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
  fullWidth?: boolean
}

export const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      label,
      error,
      helpText,
      leftIcon,
      rightIcon,
      fullWidth = true,
      className,
      id,
      ...props
    },
    ref
  ) => {
    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`

    const inputClasses = clsx(
      'block w-full rounded-md border-gray-300 shadow-sm transition-colors duration-200',
      'focus:border-blue-500 focus:ring-blue-500',
      'dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100',
      'dark:focus:border-blue-400 dark:focus:ring-blue-400',
      'disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed',
      'dark:disabled:bg-gray-800 dark:disabled:text-gray-400',
      {
        'border-red-300 focus:border-red-500 focus:ring-red-500': error,
        'dark:border-red-500 dark:focus:border-red-400 dark:focus:ring-red-400': error,
        'pl-10': leftIcon,
        'pr-10': rightIcon || error,
        'w-full': fullWidth,
      },
      className
    )

    return (
      <div className={clsx({ 'w-full': fullWidth })}>
        {label && (
          <label
            htmlFor={inputId}
            className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
          >
            {label}
          </label>
        )}
        
        <div className="relative">
          {leftIcon && (
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <span className="text-gray-400 dark:text-gray-500 h-5 w-5">
                {leftIcon}
              </span>
            </div>
          )}
          
          <input
            ref={ref}
            id={inputId}
            className={inputClasses}
            {...props}
          />
          
          {(rightIcon || error) && (
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
              {error ? (
                <ExclamationCircleIcon className="h-5 w-5 text-red-500" />
              ) : (
                <span className="text-gray-400 dark:text-gray-500 h-5 w-5">
                  {rightIcon}
                </span>
              )}
            </div>
          )}
        </div>
        
        {error && (
          <p className="mt-1 text-sm text-red-600 dark:text-red-400">
            {error}
          </p>
        )}
        
        {helpText && !error && (
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            {helpText}
          </p>
        )}
      </div>
    )
  }
)

Input.displayName = 'Input'

// Password Input Component
export interface PasswordInputProps extends Omit<InputProps, 'type' | 'rightIcon'> {
  showToggle?: boolean
}

export const PasswordInput = forwardRef<HTMLInputElement, PasswordInputProps>(
  ({ showToggle = true, ...props }, ref) => {
    const [showPassword, setShowPassword] = React.useState(false)

    const togglePassword = () => setShowPassword(!showPassword)

    return (
      <Input
        ref={ref}
        type={showPassword ? 'text' : 'password'}
        rightIcon={
          showToggle ? (
            <button
              type="button"
              onClick={togglePassword}
              className="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300"
            >
              {showPassword ? (
                <EyeSlashIcon className="h-5 w-5" />
              ) : (
                <EyeIcon className="h-5 w-5" />
              )}
            </button>
          ) : undefined
        }
        {...props}
      />
    )
  }
)

PasswordInput.displayName = 'PasswordInput'

// Textarea Component
export interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string
  error?: string
  helpText?: string
  fullWidth?: boolean
  resize?: 'none' | 'vertical' | 'horizontal' | 'both'
}

export const Textarea = forwardRef<HTMLTextAreaElement, TextareaProps>(
  (
    {
      label,
      error,
      helpText,
      fullWidth = true,
      resize = 'vertical',
      className,
      id,
      ...props
    },
    ref
  ) => {
    const textareaId = id || `textarea-${Math.random().toString(36).substr(2, 9)}`

    const textareaClasses = clsx(
      'block w-full rounded-md border-gray-300 shadow-sm transition-colors duration-200',
      'focus:border-blue-500 focus:ring-blue-500',
      'dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100',
      'dark:focus:border-blue-400 dark:focus:ring-blue-400',
      'disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed',
      'dark:disabled:bg-gray-800 dark:disabled:text-gray-400',
      {
        'border-red-300 focus:border-red-500 focus:ring-red-500': error,
        'dark:border-red-500 dark:focus:border-red-400 dark:focus:ring-red-400': error,
        'w-full': fullWidth,
        'resize-none': resize === 'none',
        'resize-y': resize === 'vertical',
        'resize-x': resize === 'horizontal',
        'resize': resize === 'both',
      },
      className
    )

    return (
      <div className={clsx({ 'w-full': fullWidth })}>
        {label && (
          <label
            htmlFor={textareaId}
            className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
          >
            {label}
          </label>
        )}
        
        <textarea
          ref={ref}
          id={textareaId}
          className={textareaClasses}
          {...props}
        />
        
        {error && (
          <p className="mt-1 text-sm text-red-600 dark:text-red-400">
            {error}
          </p>
        )}
        
        {helpText && !error && (
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            {helpText}
          </p>
        )}
      </div>
    )
  }
)

Textarea.displayName = 'Textarea'

// Select Component
export interface SelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
  label?: string
  error?: string
  helpText?: string
  fullWidth?: boolean
  placeholder?: string
  options: Array<{ value: string; label: string; disabled?: boolean }>
}

export const Select = forwardRef<HTMLSelectElement, SelectProps>(
  (
    {
      label,
      error,
      helpText,
      fullWidth = true,
      placeholder,
      options,
      className,
      id,
      ...props
    },
    ref
  ) => {
    const selectId = id || `select-${Math.random().toString(36).substr(2, 9)}`

    const selectClasses = clsx(
      'block w-full rounded-md border-gray-300 shadow-sm transition-colors duration-200',
      'focus:border-blue-500 focus:ring-blue-500',
      'dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100',
      'dark:focus:border-blue-400 dark:focus:ring-blue-400',
      'disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed',
      'dark:disabled:bg-gray-800 dark:disabled:text-gray-400',
      {
        'border-red-300 focus:border-red-500 focus:ring-red-500': error,
        'dark:border-red-500 dark:focus:border-red-400 dark:focus:ring-red-400': error,
        'w-full': fullWidth,
      },
      className
    )

    return (
      <div className={clsx({ 'w-full': fullWidth })}>
        {label && (
          <label
            htmlFor={selectId}
            className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
          >
            {label}
          </label>
        )}
        
        <select
          ref={ref}
          id={selectId}
          className={selectClasses}
          {...props}
        >
          {placeholder && (
            <option value="" disabled>
              {placeholder}
            </option>
          )}
          {options.map((option) => (
            <option
              key={option.value}
              value={option.value}
              disabled={option.disabled}
            >
              {option.label}
            </option>
          ))}
        </select>
        
        {error && (
          <p className="mt-1 text-sm text-red-600 dark:text-red-400">
            {error}
          </p>
        )}
        
        {helpText && !error && (
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            {helpText}
          </p>
        )}
      </div>
    )
  }
)

Select.displayName = 'Select'

// Checkbox Component
export interface CheckboxProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'type'> {
  label?: string
  description?: string
  error?: string
}

export const Checkbox = forwardRef<HTMLInputElement, CheckboxProps>(
  ({ label, description, error, className, id, ...props }, ref) => {
    const checkboxId = id || `checkbox-${Math.random().toString(36).substr(2, 9)}`

    return (
      <div className="flex items-start">
        <div className="flex items-center h-5">
          <input
            ref={ref}
            id={checkboxId}
            type="checkbox"
            className={clsx(
              'h-4 w-4 text-blue-600 border-gray-300 rounded',
              'focus:ring-blue-500 dark:focus:ring-blue-400',
              'dark:border-gray-600 dark:bg-gray-700',
              {
                'border-red-300': error,
                'dark:border-red-500': error,
              },
              className
            )}
            {...props}
          />
        </div>
        
        {(label || description) && (
          <div className="ml-3 text-sm">
            {label && (
              <label
                htmlFor={checkboxId}
                className="font-medium text-gray-700 dark:text-gray-300"
              >
                {label}
              </label>
            )}
            {description && (
              <p className="text-gray-500 dark:text-gray-400">
                {description}
              </p>
            )}
            {error && (
              <p className="text-red-600 dark:text-red-400">
                {error}
              </p>
            )}
          </div>
        )}
      </div>
    )
  }
)

Checkbox.displayName = 'Checkbox'
