{"name": "knowledgebot-frontend", "version": "1.0.0", "description": "KnowledgeBot - Corporate Memory System Frontend", "private": true, "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\""}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-query": "^3.39.3", "axios": "^1.6.2", "zustand": "^4.4.7", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "react-dropzone": "^14.2.3", "react-hot-toast": "^2.4.1", "date-fns": "^2.30.0", "recharts": "^2.8.0", "react-markdown": "^9.0.1", "react-syntax-highlighter": "^15.5.0", "react-virtualized-auto-sizer": "^1.0.20", "react-window": "^1.8.8", "fuse.js": "^7.0.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "lucide-react": "^0.294.0", "framer-motion": "^10.16.16", "react-intersection-observer": "^9.5.3", "react-helmet-async": "^1.3.0", "js-cookie": "^3.0.5"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/js-cookie": "^3.0.6", "@types/react-syntax-highlighter": "^15.5.10", "@types/react-window": "^1.8.8", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "vite": "^5.0.0", "typescript": "^5.2.2", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.7", "vitest": "^1.0.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "@vitest/ui": "^1.0.0", "@vitest/coverage-v8": "^1.0.0", "jsdom": "^23.0.1"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}