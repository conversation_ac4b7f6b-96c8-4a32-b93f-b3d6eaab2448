/**
 * KnowledgeBot - Corporate Memory System
 * Token Storage Utility
 * 
 * Handles secure storage and management of JWT tokens with
 * automatic expiration checking and refresh logic.
 */

import Cookies from 'js-cookie'

// Storage keys
const ACCESS_TOKEN_KEY = 'kb_access_token'
const REFRESH_TOKEN_KEY = 'kb_refresh_token'
const TOKEN_EXPIRY_KEY = 'kb_token_expiry'

// Cookie options
const COOKIE_OPTIONS = {
  secure: window.location.protocol === 'https:',
  sameSite: 'strict' as const,
  path: '/',
}

class TokenStorage {
  /**
   * Set authentication tokens
   */
  setTokens(accessToken: string, refreshToken: string): void {
    try {
      // Parse JWT to get expiration time
      const payload = this.parseJWT(accessToken)
      const expiryTime = payload?.exp ? payload.exp * 1000 : Date.now() + 30 * 60 * 1000 // Default 30 minutes

      // Store in cookies (more secure than localStorage)
      Cookies.set(ACCESS_TOKEN_KEY, accessToken, {
        ...COOKIE_OPTIONS,
        expires: new Date(expiryTime),
      })

      Cookies.set(REFRESH_TOKEN_KEY, refreshToken, {
        ...COOKIE_OPTIONS,
        expires: 7, // 7 days
      })

      Cookies.set(TOKEN_EXPIRY_KEY, expiryTime.toString(), {
        ...COOKIE_OPTIONS,
        expires: new Date(expiryTime),
      })

      // Also store in memory for quick access
      this.memoryStorage.accessToken = accessToken
      this.memoryStorage.refreshToken = refreshToken
      this.memoryStorage.expiryTime = expiryTime

    } catch (error) {
      console.error('Failed to set tokens:', error)
    }
  }

  /**
   * Get access token
   */
  getAccessToken(): string | null {
    // Try memory first for performance
    if (this.memoryStorage.accessToken && !this.isTokenExpired(this.memoryStorage.accessToken)) {
      return this.memoryStorage.accessToken
    }

    // Fallback to cookies
    const token = Cookies.get(ACCESS_TOKEN_KEY)
    if (token && !this.isTokenExpired(token)) {
      this.memoryStorage.accessToken = token
      return token
    }

    return null
  }

  /**
   * Get refresh token
   */
  getRefreshToken(): string | null {
    // Try memory first
    if (this.memoryStorage.refreshToken) {
      return this.memoryStorage.refreshToken
    }

    // Fallback to cookies
    const token = Cookies.get(REFRESH_TOKEN_KEY)
    if (token) {
      this.memoryStorage.refreshToken = token
      return token
    }

    return null
  }

  /**
   * Clear all tokens
   */
  clearTokens(): void {
    // Clear cookies
    Cookies.remove(ACCESS_TOKEN_KEY, COOKIE_OPTIONS)
    Cookies.remove(REFRESH_TOKEN_KEY, COOKIE_OPTIONS)
    Cookies.remove(TOKEN_EXPIRY_KEY, COOKIE_OPTIONS)

    // Clear memory
    this.memoryStorage = {
      accessToken: null,
      refreshToken: null,
      expiryTime: null,
    }
  }

  /**
   * Check if token is expired
   */
  isTokenExpired(token: string): boolean {
    try {
      const payload = this.parseJWT(token)
      if (!payload?.exp) {
        return true
      }

      const currentTime = Date.now() / 1000
      return payload.exp < currentTime
    } catch (error) {
      return true
    }
  }

  /**
   * Check if token is expiring soon (within 5 minutes)
   */
  isTokenExpiringSoon(token: string): boolean {
    try {
      const payload = this.parseJWT(token)
      if (!payload?.exp) {
        return true
      }

      const currentTime = Date.now() / 1000
      const fiveMinutesFromNow = currentTime + 5 * 60 // 5 minutes
      return payload.exp < fiveMinutesFromNow
    } catch (error) {
      return true
    }
  }

  /**
   * Get token expiration time
   */
  getTokenExpiry(): number | null {
    // Try memory first
    if (this.memoryStorage.expiryTime) {
      return this.memoryStorage.expiryTime
    }

    // Fallback to cookies
    const expiry = Cookies.get(TOKEN_EXPIRY_KEY)
    if (expiry) {
      const expiryTime = parseInt(expiry, 10)
      this.memoryStorage.expiryTime = expiryTime
      return expiryTime
    }

    return null
  }

  /**
   * Get time until token expires (in seconds)
   */
  getTimeUntilExpiry(): number {
    const expiry = this.getTokenExpiry()
    if (!expiry) {
      return 0
    }

    const currentTime = Date.now()
    const timeUntilExpiry = Math.max(0, Math.floor((expiry - currentTime) / 1000))
    return timeUntilExpiry
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    const token = this.getAccessToken()
    return token !== null && !this.isTokenExpired(token)
  }

  /**
   * Parse JWT token payload
   */
  private parseJWT(token: string): any {
    try {
      const base64Url = token.split('.')[1]
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/')
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split('')
          .map((c) => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
          .join('')
      )
      return JSON.parse(jsonPayload)
    } catch (error) {
      console.error('Failed to parse JWT:', error)
      return null
    }
  }

  /**
   * Get user ID from token
   */
  getUserId(): string | null {
    const token = this.getAccessToken()
    if (!token) {
      return null
    }

    const payload = this.parseJWT(token)
    return payload?.sub || payload?.user_id || null
  }

  /**
   * Get user permissions from token
   */
  getPermissions(): string[] {
    const token = this.getAccessToken()
    if (!token) {
      return []
    }

    const payload = this.parseJWT(token)
    return payload?.permissions || []
  }

  /**
   * Get user roles from token
   */
  getRoles(): string[] {
    const token = this.getAccessToken()
    if (!token) {
      return []
    }

    const payload = this.parseJWT(token)
    return payload?.roles || []
  }

  /**
   * Memory storage for performance
   */
  private memoryStorage: {
    accessToken: string | null
    refreshToken: string | null
    expiryTime: number | null
  } = {
    accessToken: null,
    refreshToken: null,
    expiryTime: null,
  }
}

// Export singleton instance
export const tokenStorage = new TokenStorage()

// Export class for testing
export { TokenStorage }
