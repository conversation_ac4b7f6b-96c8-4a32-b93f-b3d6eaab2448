<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="KnowledgeBot - Corporate Memory System for Enterprise Knowledge Management" />
    <meta name="theme-color" content="#3b82f6" />
    
    <!-- Preconnect to improve performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    
    <!-- Inter font for modern UI -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
    
    <!-- JetBrains Mono for code -->
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet" />
    
    <title>KnowledgeBot - Corporate Memory System</title>
    
    <!-- Prevent FOUC (Flash of Unstyled Content) -->
    <style>
      html {
        font-family: 'Inter', system-ui, sans-serif;
      }
      
      /* Loading spinner styles */
      .loading-spinner {
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 3px solid #f3f3f3;
        border-top: 3px solid #3b82f6;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* Initial loading screen */
      #initial-loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
      }
      
      .initial-loading-content {
        text-align: center;
      }
      
      .initial-loading-logo {
        width: 64px;
        height: 64px;
        margin: 0 auto 16px;
        background: #3b82f6;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        font-size: 24px;
      }
      
      .initial-loading-text {
        color: #64748b;
        font-size: 14px;
        margin-top: 8px;
      }
    </style>
  </head>
  <body>
    <!-- Initial loading screen -->
    <div id="initial-loading">
      <div class="initial-loading-content">
        <div class="initial-loading-logo">KB</div>
        <div class="loading-spinner"></div>
        <div class="initial-loading-text">Loading KnowledgeBot...</div>
      </div>
    </div>
    
    <!-- React app root -->
    <div id="root"></div>
    
    <!-- Main application script -->
    <script type="module" src="/src/main.tsx"></script>
    
    <!-- Remove loading screen once React loads -->
    <script>
      window.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => {
          const loadingElement = document.getElementById('initial-loading');
          if (loadingElement) {
            loadingElement.style.opacity = '0';
            loadingElement.style.transition = 'opacity 0.3s ease-out';
            setTimeout(() => {
              loadingElement.remove();
            }, 300);
          }
        }, 1000);
      });
    </script>
  </body>
</html>
