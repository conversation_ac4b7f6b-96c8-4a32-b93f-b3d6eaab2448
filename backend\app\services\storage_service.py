"""
KnowledgeBot - Corporate Memory System
Storage Service for Enterprise File Management

This module handles file storage operations with support for multiple backends
including S3, MinIO, and local storage with enterprise security features.
"""

import asyncio
import logging
import hashlib
import mimetypes
from typing import Optional, Dict, Any, <PERSON>ary<PERSON>
from pathlib import Path
from datetime import datetime, timedelta
import aiofiles
import boto3
from botocore.exceptions import ClientError, NoCredentialsError
import os

from app.core.config import settings

logger = logging.getLogger(__name__)


class StorageService:
    """
    Enterprise storage service with multiple backend support.
    """
    
    def __init__(self):
        self.storage_type = settings.STORAGE_TYPE
        self._s3_client = None
        self._initialize_storage()
    
    def _initialize_storage(self):
        """Initialize storage backend based on configuration."""
        try:
            if self.storage_type in ["s3", "minio"]:
                self._initialize_s3_client()
            elif self.storage_type == "local":
                self._initialize_local_storage()
            else:
                raise ValueError(f"Unsupported storage type: {self.storage_type}")
            
            logger.info(f"Storage service initialized with backend: {self.storage_type}")
            
        except Exception as e:
            logger.error(f"Failed to initialize storage service: {e}")
            raise
    
    def _initialize_s3_client(self):
        """Initialize S3/MinIO client."""
        try:
            session = boto3.Session(
                aws_access_key_id=settings.S3_ACCESS_KEY,
                aws_secret_access_key=settings.S3_SECRET_KEY,
                region_name=settings.S3_REGION
            )
            
            # Configure for MinIO if endpoint URL is provided
            if settings.S3_ENDPOINT_URL:
                self._s3_client = session.client(
                    's3',
                    endpoint_url=settings.S3_ENDPOINT_URL,
                    use_ssl=settings.S3_ENDPOINT_URL.startswith('https://'),
                    verify=False  # For development with self-signed certificates
                )
            else:
                self._s3_client = session.client('s3')
            
            # Test connection and create bucket if it doesn't exist
            self._ensure_bucket_exists()
            
        except Exception as e:
            logger.error(f"Failed to initialize S3 client: {e}")
            raise
    
    def _initialize_local_storage(self):
        """Initialize local storage."""
        try:
            storage_path = Path(settings.LOCAL_STORAGE_PATH)
            storage_path.mkdir(parents=True, exist_ok=True)
            
            # Create subdirectories for organization
            for subdir in ['documents', 'temp', 'thumbnails']:
                (storage_path / subdir).mkdir(exist_ok=True)
            
        except Exception as e:
            logger.error(f"Failed to initialize local storage: {e}")
            raise
    
    def _ensure_bucket_exists(self):
        """Ensure S3 bucket exists, create if it doesn't."""
        try:
            self._s3_client.head_bucket(Bucket=settings.S3_BUCKET_NAME)
            logger.info(f"S3 bucket '{settings.S3_BUCKET_NAME}' exists")
            
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == '404':
                # Bucket doesn't exist, create it
                try:
                    if settings.S3_REGION == 'us-east-1':
                        self._s3_client.create_bucket(Bucket=settings.S3_BUCKET_NAME)
                    else:
                        self._s3_client.create_bucket(
                            Bucket=settings.S3_BUCKET_NAME,
                            CreateBucketConfiguration={'LocationConstraint': settings.S3_REGION}
                        )
                    logger.info(f"Created S3 bucket '{settings.S3_BUCKET_NAME}'")
                    
                except ClientError as create_error:
                    logger.error(f"Failed to create S3 bucket: {create_error}")
                    raise
            else:
                logger.error(f"Error checking S3 bucket: {e}")
                raise
    
    async def store_file(
        self,
        file_content: bytes,
        filename: str,
        content_type: str,
        metadata: Optional[Dict[str, str]] = None
    ) -> str:
        """
        Store file and return storage path.
        
        Args:
            file_content: Binary file content
            filename: Original filename
            content_type: MIME type
            metadata: Additional metadata
            
        Returns:
            str: Storage path/key for the file
        """
        try:
            # Generate unique file key
            file_key = self._generate_file_key(filename, file_content)
            
            if self.storage_type in ["s3", "minio"]:
                await self._store_file_s3(file_key, file_content, content_type, metadata)
            else:  # local
                await self._store_file_local(file_key, file_content, metadata)
            
            logger.info(f"File stored successfully: {file_key}")
            return file_key
            
        except Exception as e:
            logger.error(f"Error storing file {filename}: {e}")
            raise
    
    async def _store_file_s3(
        self,
        file_key: str,
        file_content: bytes,
        content_type: str,
        metadata: Optional[Dict[str, str]] = None
    ):
        """Store file in S3/MinIO."""
        try:
            # Prepare metadata
            s3_metadata = metadata or {}
            s3_metadata.update({
                'uploaded_at': datetime.utcnow().isoformat(),
                'content_length': str(len(file_content)),
                'content_hash': hashlib.sha256(file_content).hexdigest()
            })
            
            # Upload file
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None,
                self._s3_client.put_object,
                {
                    'Bucket': settings.S3_BUCKET_NAME,
                    'Key': file_key,
                    'Body': file_content,
                    'ContentType': content_type,
                    'Metadata': s3_metadata,
                    'ServerSideEncryption': 'AES256' if settings.ENABLE_DATA_ENCRYPTION else None
                }
            )
            
        except Exception as e:
            logger.error(f"Error storing file in S3: {e}")
            raise
    
    async def _store_file_local(
        self,
        file_key: str,
        file_content: bytes,
        metadata: Optional[Dict[str, str]] = None
    ):
        """Store file locally."""
        try:
            file_path = Path(settings.LOCAL_STORAGE_PATH) / "documents" / file_key
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Write file
            async with aiofiles.open(file_path, 'wb') as f:
                await f.write(file_content)
            
            # Store metadata if provided
            if metadata:
                metadata_path = file_path.with_suffix(file_path.suffix + '.meta')
                metadata_content = {
                    **metadata,
                    'uploaded_at': datetime.utcnow().isoformat(),
                    'content_length': len(file_content),
                    'content_hash': hashlib.sha256(file_content).hexdigest()
                }
                
                async with aiofiles.open(metadata_path, 'w') as f:
                    import json
                    await f.write(json.dumps(metadata_content, indent=2))
            
        except Exception as e:
            logger.error(f"Error storing file locally: {e}")
            raise
    
    async def retrieve_file(self, file_key: str) -> Optional[bytes]:
        """
        Retrieve file content by key.
        
        Args:
            file_key: Storage key for the file
            
        Returns:
            Optional[bytes]: File content or None if not found
        """
        try:
            if self.storage_type in ["s3", "minio"]:
                return await self._retrieve_file_s3(file_key)
            else:  # local
                return await self._retrieve_file_local(file_key)
                
        except Exception as e:
            logger.error(f"Error retrieving file {file_key}: {e}")
            return None
    
    async def _retrieve_file_s3(self, file_key: str) -> Optional[bytes]:
        """Retrieve file from S3/MinIO."""
        try:
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                self._s3_client.get_object,
                settings.S3_BUCKET_NAME,
                file_key
            )
            
            return response['Body'].read()
            
        except ClientError as e:
            if e.response['Error']['Code'] == 'NoSuchKey':
                logger.warning(f"File not found in S3: {file_key}")
                return None
            else:
                logger.error(f"Error retrieving file from S3: {e}")
                raise
    
    async def _retrieve_file_local(self, file_key: str) -> Optional[bytes]:
        """Retrieve file from local storage."""
        try:
            file_path = Path(settings.LOCAL_STORAGE_PATH) / "documents" / file_key
            
            if not file_path.exists():
                logger.warning(f"File not found locally: {file_key}")
                return None
            
            async with aiofiles.open(file_path, 'rb') as f:
                return await f.read()
                
        except Exception as e:
            logger.error(f"Error retrieving file locally: {e}")
            raise
    
    async def delete_file(self, file_key: str) -> bool:
        """
        Delete file by key.
        
        Args:
            file_key: Storage key for the file
            
        Returns:
            bool: True if deleted successfully, False otherwise
        """
        try:
            if self.storage_type in ["s3", "minio"]:
                return await self._delete_file_s3(file_key)
            else:  # local
                return await self._delete_file_local(file_key)
                
        except Exception as e:
            logger.error(f"Error deleting file {file_key}: {e}")
            return False
    
    async def _delete_file_s3(self, file_key: str) -> bool:
        """Delete file from S3/MinIO."""
        try:
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None,
                self._s3_client.delete_object,
                settings.S3_BUCKET_NAME,
                file_key
            )
            
            logger.info(f"File deleted from S3: {file_key}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting file from S3: {e}")
            return False
    
    async def _delete_file_local(self, file_key: str) -> bool:
        """Delete file from local storage."""
        try:
            file_path = Path(settings.LOCAL_STORAGE_PATH) / "documents" / file_key
            metadata_path = file_path.with_suffix(file_path.suffix + '.meta')
            
            # Delete main file
            if file_path.exists():
                file_path.unlink()
            
            # Delete metadata file
            if metadata_path.exists():
                metadata_path.unlink()
            
            logger.info(f"File deleted locally: {file_key}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting file locally: {e}")
            return False
    
    async def file_exists(self, file_key: str) -> bool:
        """
        Check if file exists.
        
        Args:
            file_key: Storage key for the file
            
        Returns:
            bool: True if file exists, False otherwise
        """
        try:
            if self.storage_type in ["s3", "minio"]:
                return await self._file_exists_s3(file_key)
            else:  # local
                return await self._file_exists_local(file_key)
                
        except Exception as e:
            logger.error(f"Error checking file existence {file_key}: {e}")
            return False
    
    async def _file_exists_s3(self, file_key: str) -> bool:
        """Check if file exists in S3/MinIO."""
        try:
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None,
                self._s3_client.head_object,
                settings.S3_BUCKET_NAME,
                file_key
            )
            return True
            
        except ClientError as e:
            if e.response['Error']['Code'] == '404':
                return False
            else:
                logger.error(f"Error checking file existence in S3: {e}")
                raise
    
    async def _file_exists_local(self, file_key: str) -> bool:
        """Check if file exists locally."""
        file_path = Path(settings.LOCAL_STORAGE_PATH) / "documents" / file_key
        return file_path.exists()
    
    async def get_file_metadata(self, file_key: str) -> Optional[Dict[str, Any]]:
        """
        Get file metadata.
        
        Args:
            file_key: Storage key for the file
            
        Returns:
            Optional[Dict[str, Any]]: File metadata or None if not found
        """
        try:
            if self.storage_type in ["s3", "minio"]:
                return await self._get_file_metadata_s3(file_key)
            else:  # local
                return await self._get_file_metadata_local(file_key)
                
        except Exception as e:
            logger.error(f"Error getting file metadata {file_key}: {e}")
            return None
    
    async def _get_file_metadata_s3(self, file_key: str) -> Optional[Dict[str, Any]]:
        """Get file metadata from S3/MinIO."""
        try:
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                self._s3_client.head_object,
                settings.S3_BUCKET_NAME,
                file_key
            )
            
            return {
                'content_length': response.get('ContentLength'),
                'content_type': response.get('ContentType'),
                'last_modified': response.get('LastModified'),
                'etag': response.get('ETag'),
                'metadata': response.get('Metadata', {})
            }
            
        except ClientError as e:
            if e.response['Error']['Code'] == '404':
                return None
            else:
                logger.error(f"Error getting file metadata from S3: {e}")
                raise
    
    async def _get_file_metadata_local(self, file_key: str) -> Optional[Dict[str, Any]]:
        """Get file metadata from local storage."""
        try:
            file_path = Path(settings.LOCAL_STORAGE_PATH) / "documents" / file_key
            metadata_path = file_path.with_suffix(file_path.suffix + '.meta')
            
            if not file_path.exists():
                return None
            
            # Get basic file stats
            stat = file_path.stat()
            metadata = {
                'content_length': stat.st_size,
                'last_modified': datetime.fromtimestamp(stat.st_mtime),
                'content_type': mimetypes.guess_type(str(file_path))[0]
            }
            
            # Load additional metadata if available
            if metadata_path.exists():
                async with aiofiles.open(metadata_path, 'r') as f:
                    import json
                    additional_metadata = json.loads(await f.read())
                    metadata.update(additional_metadata)
            
            return metadata
            
        except Exception as e:
            logger.error(f"Error getting file metadata locally: {e}")
            return None
    
    def _generate_file_key(self, filename: str, file_content: bytes) -> str:
        """
        Generate unique file key based on content hash and timestamp.
        
        Args:
            filename: Original filename
            file_content: File content for hashing
            
        Returns:
            str: Unique file key
        """
        # Get file extension
        file_path = Path(filename)
        extension = file_path.suffix.lower()
        
        # Generate content hash
        content_hash = hashlib.sha256(file_content).hexdigest()[:16]
        
        # Generate timestamp
        timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
        
        # Combine to create unique key
        file_key = f"{timestamp}_{content_hash}{extension}"
        
        return file_key
    
    async def generate_presigned_url(
        self,
        file_key: str,
        expiration: int = 3600,
        method: str = "GET"
    ) -> Optional[str]:
        """
        Generate presigned URL for direct file access (S3/MinIO only).
        
        Args:
            file_key: Storage key for the file
            expiration: URL expiration time in seconds
            method: HTTP method (GET, PUT, etc.)
            
        Returns:
            Optional[str]: Presigned URL or None if not supported
        """
        if self.storage_type not in ["s3", "minio"]:
            return None
        
        try:
            loop = asyncio.get_event_loop()
            url = await loop.run_in_executor(
                None,
                self._s3_client.generate_presigned_url,
                f'{method.lower()}_object',
                {'Bucket': settings.S3_BUCKET_NAME, 'Key': file_key},
                expiration
            )
            
            return url
            
        except Exception as e:
            logger.error(f"Error generating presigned URL: {e}")
            return None
    
    async def cleanup_temp_files(self, older_than_hours: int = 24):
        """Clean up temporary files older than specified hours."""
        try:
            cutoff_time = datetime.utcnow() - timedelta(hours=older_than_hours)
            
            if self.storage_type == "local":
                temp_path = Path(settings.LOCAL_STORAGE_PATH) / "temp"
                if temp_path.exists():
                    for file_path in temp_path.iterdir():
                        if file_path.is_file():
                            file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                            if file_time < cutoff_time:
                                file_path.unlink()
                                logger.info(f"Cleaned up temp file: {file_path}")
            
            # For S3/MinIO, this would require listing objects with prefix and checking dates
            # Implementation would depend on specific cleanup requirements
            
        except Exception as e:
            logger.error(f"Error cleaning up temp files: {e}")
    
    def get_storage_info(self) -> Dict[str, Any]:
        """Get storage service information."""
        return {
            "storage_type": self.storage_type,
            "bucket_name": settings.S3_BUCKET_NAME if self.storage_type in ["s3", "minio"] else None,
            "local_path": settings.LOCAL_STORAGE_PATH if self.storage_type == "local" else None,
            "encryption_enabled": settings.ENABLE_DATA_ENCRYPTION
        }
