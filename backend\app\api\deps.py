"""
KnowledgeBot - Corporate Memory System
API Dependencies

This module provides dependency injection for FastAPI endpoints including
authentication, database sessions, and permission checking.
"""

import logging
from typing import Generator, Optional, List, Callable
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.core.database import get_async_db
from app.core.security import verify_token, rate_limiter
from app.models.user import User
from app.core.config import settings

logger = logging.getLogger(__name__)

# Security scheme
security = HTTPBearer()


async def get_db() -> AsyncSession:
    """
    Database dependency.
    
    Returns:
        AsyncSession: Database session
    """
    async with get_async_db() as session:
        yield session


async def get_current_user(
    db: AsyncSession = Depends(get_db),
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> User:
    """
    Get current authenticated user.
    
    Args:
        db: Database session
        credentials: JWT credentials
        
    Returns:
        User: Current authenticated user
        
    Raises:
        HTTPException: If authentication fails
    """
    try:
        # Verify JWT token
        payload = verify_token(credentials.credentials)
        if not payload:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # Get user ID from token
        user_id = payload.get("sub")
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token payload",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # Get user from database
        stmt = select(User).where(User.id == user_id, User.is_active == True)
        result = await db.execute(stmt)
        user = result.scalar_one_or_none()
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not found or inactive",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        return user
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting current user: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication failed",
            headers={"WWW-Authenticate": "Bearer"},
        )


async def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    Get current active user.
    
    Args:
        current_user: Current user from token
        
    Returns:
        User: Active user
        
    Raises:
        HTTPException: If user is inactive
    """
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    return current_user


async def get_current_superuser(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    Get current superuser.
    
    Args:
        current_user: Current user from token
        
    Returns:
        User: Superuser
        
    Raises:
        HTTPException: If user is not a superuser
    """
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    return current_user


def require_permissions(required_permissions: List[str]) -> Callable:
    """
    Dependency factory for permission-based access control.
    
    Args:
        required_permissions: List of required permissions
        
    Returns:
        Callable: Dependency function
    """
    async def permission_checker(
        current_user: User = Depends(get_current_user)
    ) -> User:
        """Check if user has required permissions."""
        if current_user.is_superuser:
            return current_user
        
        user_permissions = current_user.get_permissions()
        
        for permission in required_permissions:
            if permission not in user_permissions:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Missing required permission: {permission}"
                )
        
        return current_user
    
    return permission_checker


def require_roles(required_roles: List[str]) -> Callable:
    """
    Dependency factory for role-based access control.
    
    Args:
        required_roles: List of required roles
        
    Returns:
        Callable: Dependency function
    """
    async def role_checker(
        current_user: User = Depends(get_current_user)
    ) -> User:
        """Check if user has required roles."""
        if current_user.is_superuser:
            return current_user
        
        user_roles = [role.name for role in current_user.roles]
        
        if not any(role in user_roles for role in required_roles):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Missing required role. Required: {required_roles}"
            )
        
        return current_user
    
    return role_checker


def require_departments(required_departments: List[str]) -> Callable:
    """
    Dependency factory for department-based access control.
    
    Args:
        required_departments: List of required departments
        
    Returns:
        Callable: Dependency function
    """
    async def department_checker(
        current_user: User = Depends(get_current_user)
    ) -> User:
        """Check if user belongs to required departments."""
        if current_user.is_superuser:
            return current_user
        
        user_departments = current_user.get_department_names()
        
        if not any(dept in user_departments for dept in required_departments):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Access denied. Required department access: {required_departments}"
            )
        
        return current_user
    
    return department_checker


def rate_limit(
    requests_per_minute: int = 60,
    per_user: bool = True
) -> Callable:
    """
    Dependency factory for rate limiting.
    
    Args:
        requests_per_minute: Maximum requests per minute
        per_user: Whether to apply limit per user or globally
        
    Returns:
        Callable: Dependency function
    """
    async def rate_limiter_checker(
        request: Request,
        current_user: Optional[User] = Depends(get_current_user) if per_user else None
    ):
        """Check rate limits."""
        # Determine rate limit key
        if per_user and current_user:
            limit_key = f"rate_limit:user:{current_user.id}"
        else:
            limit_key = f"rate_limit:ip:{request.client.host}"
        
        # Check rate limit
        if not rate_limiter.is_allowed(limit_key, requests_per_minute, 60):
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Rate limit exceeded. Please try again later."
            )
    
    return rate_limiter_checker


async def get_optional_user(
    db: AsyncSession = Depends(get_db),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False))
) -> Optional[User]:
    """
    Get current user if authenticated, None otherwise.
    
    Args:
        db: Database session
        credentials: Optional JWT credentials
        
    Returns:
        Optional[User]: Current user or None
    """
    if not credentials:
        return None
    
    try:
        # Verify JWT token
        payload = verify_token(credentials.credentials)
        if not payload:
            return None
        
        # Get user ID from token
        user_id = payload.get("sub")
        if not user_id:
            return None
        
        # Get user from database
        stmt = select(User).where(User.id == user_id, User.is_active == True)
        result = await db.execute(stmt)
        user = result.scalar_one_or_none()
        
        return user
        
    except Exception as e:
        logger.warning(f"Error getting optional user: {e}")
        return None


def api_key_auth(
    api_key_header: str = "X-API-Key"
) -> Callable:
    """
    Dependency factory for API key authentication.
    
    Args:
        api_key_header: Header name for API key
        
    Returns:
        Callable: Dependency function
    """
    async def api_key_checker(
        request: Request,
        db: AsyncSession = Depends(get_db)
    ) -> User:
        """Check API key authentication."""
        api_key = request.headers.get(api_key_header)
        
        if not api_key:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="API key required"
            )
        
        # Find user by API key
        stmt = select(User).where(
            User.api_key == api_key,
            User.is_active == True
        )
        result = await db.execute(stmt)
        user = result.scalar_one_or_none()
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid API key"
            )
        
        # Update last used timestamp
        from datetime import datetime
        user.api_key_last_used = datetime.utcnow()
        await db.commit()
        
        return user
    
    return api_key_checker


class CommonQueryParams:
    """Common query parameters for list endpoints."""
    
    def __init__(
        self,
        skip: int = 0,
        limit: int = 50,
        sort_by: str = "created_at",
        sort_order: str = "desc"
    ):
        self.skip = max(0, skip)
        self.limit = min(100, max(1, limit))  # Limit between 1 and 100
        self.sort_by = sort_by
        self.sort_order = sort_order.lower() if sort_order.lower() in ["asc", "desc"] else "desc"


def get_common_params(
    skip: int = 0,
    limit: int = 50,
    sort_by: str = "created_at",
    sort_order: str = "desc"
) -> CommonQueryParams:
    """
    Get common query parameters.
    
    Args:
        skip: Number of items to skip
        limit: Maximum number of items to return
        sort_by: Field to sort by
        sort_order: Sort order (asc/desc)
        
    Returns:
        CommonQueryParams: Validated query parameters
    """
    return CommonQueryParams(skip, limit, sort_by, sort_order)


async def validate_content_type(
    request: Request,
    allowed_types: List[str] = None
):
    """
    Validate request content type.
    
    Args:
        request: HTTP request
        allowed_types: List of allowed content types
        
    Raises:
        HTTPException: If content type is not allowed
    """
    if allowed_types is None:
        allowed_types = ["application/json"]
    
    content_type = request.headers.get("content-type", "").split(";")[0]
    
    if content_type not in allowed_types:
        raise HTTPException(
            status_code=status.HTTP_415_UNSUPPORTED_MEDIA_TYPE,
            detail=f"Content type '{content_type}' not supported. Allowed: {allowed_types}"
        )


async def get_request_id(request: Request) -> str:
    """
    Get or generate request ID for tracing.
    
    Args:
        request: HTTP request
        
    Returns:
        str: Request ID
    """
    request_id = request.headers.get("X-Request-ID")
    if not request_id:
        import uuid
        request_id = str(uuid.uuid4())
        request.state.request_id = request_id
    
    return request_id
