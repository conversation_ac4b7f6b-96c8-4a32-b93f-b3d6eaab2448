"""
KnowledgeBot - Corporate Memory System
Authentication Service

This module handles user authentication, SSO integration, and session management
with enterprise security features and audit logging.
"""

import asyncio
import logging
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
from uuid import UUID
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, update
from sqlalchemy.orm import selectinload

from app.models.user import User, UserSession, Role, Permission, Department
from app.core.security import verify_password, get_password_hash
from app.core.config import settings

logger = logging.getLogger(__name__)


class AuthService:
    """
    Enterprise authentication service with SSO and session management.
    """
    
    def __init__(self):
        self.max_failed_attempts = 5
        self.lockout_duration = timedelta(minutes=30)
        self.session_timeout = timedelta(hours=24)
    
    async def authenticate_user(
        self,
        db: AsyncSession,
        email: str,
        password: str,
        ip_address: Optional[str] = None
    ) -> Optional[User]:
        """
        Authenticate user with email and password.
        
        Args:
            db: Database session
            email: User email
            password: User password
            ip_address: Client IP address for logging
            
        Returns:
            Optional[User]: Authenticated user or None
        """
        try:
            # Get user with roles and departments
            stmt = select(User).options(
                selectinload(User.roles).selectinload(Role.permissions),
                selectinload(User.departments)
            ).where(
                and_(
                    User.email == email.lower(),
                    User.is_active == True
                )
            )
            
            result = await db.execute(stmt)
            user = result.scalar_one_or_none()
            
            if not user:
                logger.warning(f"Authentication failed: User not found - {email}")
                return None
            
            # Check if account is locked
            if user.is_locked:
                logger.warning(f"Authentication failed: Account locked - {email}")
                return None
            
            # Check password
            if not user.hashed_password or not verify_password(password, user.hashed_password):
                # Increment failed attempts
                user.failed_login_attempts += 1
                
                # Lock account if too many failed attempts
                if user.failed_login_attempts >= self.max_failed_attempts:
                    user.locked_until = datetime.utcnow() + self.lockout_duration
                    logger.warning(f"Account locked due to failed attempts - {email}")
                
                await db.commit()
                logger.warning(f"Authentication failed: Invalid password - {email}")
                return None
            
            # Check if password change is required
            if user.must_change_password:
                logger.info(f"Password change required for user - {email}")
                # Return user but client should handle password change flow
            
            # Reset failed attempts on successful authentication
            user.failed_login_attempts = 0
            user.locked_until = None
            user.last_login = datetime.utcnow()
            
            await db.commit()
            
            logger.info(f"User authenticated successfully - {email}")
            return user
            
        except Exception as e:
            logger.error(f"Error during authentication: {e}")
            return None
    
    async def authenticate_with_sso(
        self,
        db: AsyncSession,
        sso_provider: str,
        sso_subject_id: str,
        sso_attributes: Dict[str, Any]
    ) -> Optional[User]:
        """
        Authenticate user with SSO provider.
        
        Args:
            db: Database session
            sso_provider: SSO provider name (saml, oauth, etc.)
            sso_subject_id: Subject ID from SSO provider
            sso_attributes: User attributes from SSO
            
        Returns:
            Optional[User]: Authenticated user or None
        """
        try:
            # Find user by SSO subject ID
            stmt = select(User).options(
                selectinload(User.roles).selectinload(Role.permissions),
                selectinload(User.departments)
            ).where(
                and_(
                    User.sso_provider == sso_provider,
                    User.sso_subject_id == sso_subject_id,
                    User.is_active == True
                )
            )
            
            result = await db.execute(stmt)
            user = result.scalar_one_or_none()
            
            if not user:
                # Try to find by email if provided in attributes
                email = sso_attributes.get("email")
                if email:
                    email_stmt = select(User).where(
                        and_(
                            User.email == email.lower(),
                            User.is_active == True
                        )
                    )
                    email_result = await db.execute(email_stmt)
                    user = email_result.scalar_one_or_none()
                    
                    if user:
                        # Link existing user to SSO
                        user.sso_provider = sso_provider
                        user.sso_subject_id = sso_subject_id
                        user.sso_attributes = sso_attributes
                    else:
                        # Create new user from SSO attributes
                        user = await self._create_user_from_sso(db, sso_provider, sso_subject_id, sso_attributes)
                else:
                    logger.warning(f"SSO authentication failed: No email in attributes - {sso_provider}:{sso_subject_id}")
                    return None
            
            if not user:
                return None
            
            # Update SSO attributes and last login
            user.sso_attributes = sso_attributes
            user.last_login = datetime.utcnow()
            user.is_verified = True  # SSO users are considered verified
            
            await db.commit()
            
            logger.info(f"SSO authentication successful - {sso_provider}:{sso_subject_id}")
            return user
            
        except Exception as e:
            logger.error(f"Error during SSO authentication: {e}")
            return None
    
    async def _create_user_from_sso(
        self,
        db: AsyncSession,
        sso_provider: str,
        sso_subject_id: str,
        sso_attributes: Dict[str, Any]
    ) -> Optional[User]:
        """Create new user from SSO attributes."""
        try:
            email = sso_attributes.get("email")
            if not email:
                return None
            
            # Extract user information from SSO attributes
            first_name = sso_attributes.get("first_name") or sso_attributes.get("given_name")
            last_name = sso_attributes.get("last_name") or sso_attributes.get("family_name")
            display_name = sso_attributes.get("display_name") or sso_attributes.get("name")
            
            # Create new user
            user = User(
                email=email.lower(),
                first_name=first_name,
                last_name=last_name,
                display_name=display_name,
                sso_provider=sso_provider,
                sso_subject_id=sso_subject_id,
                sso_attributes=sso_attributes,
                is_active=True,
                is_verified=True,  # SSO users are pre-verified
                hashed_password=None  # No password for SSO users
            )
            
            db.add(user)
            await db.flush()  # Get user ID
            
            # Assign default role
            await self._assign_default_role(db, user)
            
            # Assign to departments based on SSO attributes
            await self._assign_departments_from_sso(db, user, sso_attributes)
            
            await db.commit()
            
            logger.info(f"Created new user from SSO - {email}")
            return user
            
        except Exception as e:
            logger.error(f"Error creating user from SSO: {e}")
            return None
    
    async def _assign_default_role(self, db: AsyncSession, user: User):
        """Assign default role to new user."""
        try:
            # Get default user role
            stmt = select(Role).where(Role.name == "user")
            result = await db.execute(stmt)
            default_role = result.scalar_one_or_none()
            
            if default_role:
                user.roles.append(default_role)
            
        except Exception as e:
            logger.error(f"Error assigning default role: {e}")
    
    async def _assign_departments_from_sso(
        self,
        db: AsyncSession,
        user: User,
        sso_attributes: Dict[str, Any]
    ):
        """Assign user to departments based on SSO attributes."""
        try:
            # Extract department information from SSO attributes
            departments = sso_attributes.get("departments", [])
            if isinstance(departments, str):
                departments = [departments]
            
            for dept_name in departments:
                # Find or create department
                stmt = select(Department).where(Department.name == dept_name)
                result = await db.execute(stmt)
                department = result.scalar_one_or_none()
                
                if not department:
                    department = Department(
                        name=dept_name,
                        display_name=dept_name.title(),
                        is_active=True
                    )
                    db.add(department)
                    await db.flush()
                
                user.departments.append(department)
            
        except Exception as e:
            logger.error(f"Error assigning departments from SSO: {e}")
    
    async def create_user_session(
        self,
        db: AsyncSession,
        user: User,
        session_token: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> UserSession:
        """
        Create new user session.
        
        Args:
            db: Database session
            user: User object
            session_token: Session token
            ip_address: Client IP address
            user_agent: Client user agent
            
        Returns:
            UserSession: Created session
        """
        try:
            session = UserSession(
                user_id=user.id,
                session_token=session_token,
                ip_address=ip_address,
                user_agent=user_agent,
                expires_at=datetime.utcnow() + self.session_timeout
            )
            
            db.add(session)
            await db.commit()
            
            return session
            
        except Exception as e:
            logger.error(f"Error creating user session: {e}")
            raise
    
    async def invalidate_user_sessions(
        self,
        db: AsyncSession,
        user_id: UUID,
        except_session_id: Optional[UUID] = None
    ):
        """
        Invalidate all user sessions except optionally one.
        
        Args:
            db: Database session
            user_id: User ID
            except_session_id: Session ID to keep active
        """
        try:
            stmt = update(UserSession).where(
                and_(
                    UserSession.user_id == user_id,
                    UserSession.is_active == True,
                    UserSession.id != except_session_id if except_session_id else True
                )
            ).values(
                is_active=False,
                logout_reason="forced"
            )
            
            await db.execute(stmt)
            await db.commit()
            
            logger.info(f"Invalidated sessions for user {user_id}")
            
        except Exception as e:
            logger.error(f"Error invalidating user sessions: {e}")
            raise
    
    async def cleanup_expired_sessions(self, db: AsyncSession):
        """Clean up expired sessions."""
        try:
            cutoff_time = datetime.utcnow()
            
            stmt = update(UserSession).where(
                and_(
                    UserSession.expires_at < cutoff_time,
                    UserSession.is_active == True
                )
            ).values(
                is_active=False,
                logout_reason="expired"
            )
            
            result = await db.execute(stmt)
            await db.commit()
            
            if result.rowcount > 0:
                logger.info(f"Cleaned up {result.rowcount} expired sessions")
            
        except Exception as e:
            logger.error(f"Error cleaning up expired sessions: {e}")
    
    async def change_password(
        self,
        db: AsyncSession,
        user: User,
        old_password: str,
        new_password: str
    ) -> bool:
        """
        Change user password.
        
        Args:
            db: Database session
            user: User object
            old_password: Current password
            new_password: New password
            
        Returns:
            bool: True if password changed successfully
        """
        try:
            # Verify old password (skip for SSO users or if must_change_password is True)
            if user.hashed_password and not user.must_change_password:
                if not verify_password(old_password, user.hashed_password):
                    logger.warning(f"Password change failed: Invalid old password - {user.email}")
                    return False
            
            # Set new password
            user.hashed_password = get_password_hash(new_password)
            user.last_password_change = datetime.utcnow()
            user.must_change_password = False
            
            await db.commit()
            
            logger.info(f"Password changed successfully - {user.email}")
            return True
            
        except Exception as e:
            logger.error(f"Error changing password: {e}")
            return False
    
    async def reset_password(
        self,
        db: AsyncSession,
        email: str,
        new_password: str
    ) -> bool:
        """
        Reset user password (admin function).
        
        Args:
            db: Database session
            email: User email
            new_password: New password
            
        Returns:
            bool: True if password reset successfully
        """
        try:
            stmt = select(User).where(
                and_(
                    User.email == email.lower(),
                    User.is_active == True
                )
            )
            
            result = await db.execute(stmt)
            user = result.scalar_one_or_none()
            
            if not user:
                logger.warning(f"Password reset failed: User not found - {email}")
                return False
            
            # Set new password and require change on next login
            user.hashed_password = get_password_hash(new_password)
            user.last_password_change = datetime.utcnow()
            user.must_change_password = True
            user.failed_login_attempts = 0
            user.locked_until = None
            
            await db.commit()
            
            logger.info(f"Password reset successfully - {email}")
            return True
            
        except Exception as e:
            logger.error(f"Error resetting password: {e}")
            return False
    
    async def unlock_user_account(
        self,
        db: AsyncSession,
        email: str
    ) -> bool:
        """
        Unlock user account (admin function).
        
        Args:
            db: Database session
            email: User email
            
        Returns:
            bool: True if account unlocked successfully
        """
        try:
            stmt = select(User).where(User.email == email.lower())
            result = await db.execute(stmt)
            user = result.scalar_one_or_none()
            
            if not user:
                logger.warning(f"Account unlock failed: User not found - {email}")
                return False
            
            user.failed_login_attempts = 0
            user.locked_until = None
            
            await db.commit()
            
            logger.info(f"Account unlocked successfully - {email}")
            return True
            
        except Exception as e:
            logger.error(f"Error unlocking account: {e}")
            return False
    
    async def get_user_permissions(
        self,
        db: AsyncSession,
        user_id: UUID
    ) -> List[str]:
        """
        Get all permissions for a user.
        
        Args:
            db: Database session
            user_id: User ID
            
        Returns:
            List[str]: List of permission names
        """
        try:
            stmt = select(User).options(
                selectinload(User.roles).selectinload(Role.permissions)
            ).where(User.id == user_id)
            
            result = await db.execute(stmt)
            user = result.scalar_one_or_none()
            
            if not user:
                return []
            
            return user.get_permissions()
            
        except Exception as e:
            logger.error(f"Error getting user permissions: {e}")
            return []
