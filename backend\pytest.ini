[tool:pytest]
# KnowledgeBot Backend - Pytest Configuration

# Test discovery
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Minimum version
minversion = 7.0

# Add options
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=app
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml:coverage.xml
    --cov-fail-under=90
    --durations=10
    --maxfail=5

# Async support
asyncio_mode = auto

# Markers
markers =
    unit: Unit tests
    integration: Integration tests
    e2e: End-to-end tests
    slow: Slow running tests
    auth: Authentication tests
    documents: Document management tests
    knowledge: Knowledge query tests
    search: Search functionality tests
    analytics: Analytics tests
    admin: Admin functionality tests
    security: Security tests
    performance: Performance tests
    api: API endpoint tests
    services: Service layer tests
    models: Database model tests
    external: Tests requiring external services

# Test timeout
timeout = 300

# Warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:sqlalchemy.*
    ignore::pytest.PytestUnraisableExceptionWarning

# Environment variables for testing
env =
    TESTING = true
    DATABASE_URL = postgresql+asyncpg://test:test@localhost:5432/knowledgebot_test
    REDIS_URL = redis://localhost:6379/15
    JWT_SECRET_KEY = test-secret-key-for-testing-only
    MEMVID_MODEL_PATH = ./tests/fixtures/models
    STORAGE_TYPE = local
    LOCAL_STORAGE_PATH = ./tests/fixtures/storage
    ENABLE_VIRUS_SCANNING = false
    ENABLE_DOCUMENT_OCR = false
    LOG_LEVEL = WARNING
