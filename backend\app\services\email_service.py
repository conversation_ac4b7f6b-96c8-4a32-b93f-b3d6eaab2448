"""
KnowledgeBot - Corporate Memory System
Email Service for Enterprise Communications

This module handles email notifications, user verification, password resets,
and system alerts with template support and delivery tracking.
"""

import asyncio
import logging
import smtplib
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>
from email.mime.base import MI<PERSON><PERSON><PERSON>
from email import encoders
from pathlib import Path
import aiosmtplib
from jinja2 import Environment, FileSystemLoader, Template
from uuid import UUID

from app.core.config import settings
from app.core.security import create_access_token, generate_password_reset_token

logger = logging.getLogger(__name__)


class EmailService:
    """
    Enterprise email service with template support and delivery tracking.
    """
    
    def __init__(self):
        self.smtp_host = settings.SMTP_HOST
        self.smtp_port = settings.SMTP_PORT
        self.smtp_user = settings.SMTP_USER
        self.smtp_password = settings.SMTP_PASSWORD
        self.smtp_tls = settings.SMTP_TLS
        self.from_email = settings.EMAILS_FROM_EMAIL
        self.from_name = settings.EMAILS_FROM_NAME
        
        # Initialize Jinja2 environment for templates
        template_dir = Path(__file__).parent.parent / "templates" / "email"
        template_dir.mkdir(parents=True, exist_ok=True)
        
        self.jinja_env = Environment(
            loader=FileSystemLoader(str(template_dir)),
            autoescape=True
        )
        
        # Create default templates if they don't exist
        self._create_default_templates()
    
    def _create_default_templates(self):
        """Create default email templates if they don't exist."""
        templates = {
            "verification.html": self._get_verification_template(),
            "password_reset.html": self._get_password_reset_template(),
            "welcome.html": self._get_welcome_template(),
            "document_processed.html": self._get_document_processed_template(),
            "system_alert.html": self._get_system_alert_template()
        }
        
        template_dir = Path(__file__).parent.parent / "templates" / "email"
        
        for template_name, template_content in templates.items():
            template_path = template_dir / template_name
            if not template_path.exists():
                try:
                    template_path.write_text(template_content)
                    logger.info(f"Created email template: {template_name}")
                except Exception as e:
                    logger.error(f"Error creating template {template_name}: {e}")
    
    async def send_email(
        self,
        to_email: str,
        subject: str,
        html_content: str,
        text_content: Optional[str] = None,
        attachments: Optional[List[Dict[str, Any]]] = None
    ) -> bool:
        """
        Send email with HTML and optional text content.
        
        Args:
            to_email: Recipient email address
            subject: Email subject
            html_content: HTML email content
            text_content: Plain text content (optional)
            attachments: List of attachments (optional)
            
        Returns:
            bool: True if email sent successfully
        """
        if not self._is_email_configured():
            logger.warning("Email service not configured, skipping email send")
            return False
        
        try:
            # Create message
            message = MIMEMultipart("alternative")
            message["Subject"] = subject
            message["From"] = f"{self.from_name} <{self.from_email}>"
            message["To"] = to_email
            
            # Add text content if provided
            if text_content:
                text_part = MIMEText(text_content, "plain")
                message.attach(text_part)
            
            # Add HTML content
            html_part = MIMEText(html_content, "html")
            message.attach(html_part)
            
            # Add attachments if provided
            if attachments:
                for attachment in attachments:
                    await self._add_attachment(message, attachment)
            
            # Send email
            await self._send_message(message, to_email)
            
            logger.info(f"Email sent successfully to {to_email}")
            return True
            
        except Exception as e:
            logger.error(f"Error sending email to {to_email}: {e}")
            return False
    
    async def send_verification_email(self, email: str, user_id: UUID) -> bool:
        """
        Send email verification email.
        
        Args:
            email: User email address
            user_id: User ID for verification token
            
        Returns:
            bool: True if email sent successfully
        """
        try:
            # Generate verification token
            verification_token = create_access_token(
                subject=str(user_id),
                expires_delta=timedelta(hours=24),
                additional_claims={"type": "email_verification", "email": email}
            )
            
            # Generate verification URL
            verification_url = f"{settings.FRONTEND_URL}/verify-email?token={verification_token}"
            
            # Render template
            template = self.jinja_env.get_template("verification.html")
            html_content = template.render(
                verification_url=verification_url,
                user_email=email,
                project_name=settings.PROJECT_NAME
            )
            
            # Send email
            return await self.send_email(
                to_email=email,
                subject=f"Verify your {settings.PROJECT_NAME} account",
                html_content=html_content
            )
            
        except Exception as e:
            logger.error(f"Error sending verification email: {e}")
            return False
    
    async def send_password_reset_email(self, email: str) -> bool:
        """
        Send password reset email.
        
        Args:
            email: User email address
            
        Returns:
            bool: True if email sent successfully
        """
        try:
            # Generate password reset token
            reset_token = generate_password_reset_token(email)
            
            # Generate reset URL
            reset_url = f"{settings.FRONTEND_URL}/reset-password?token={reset_token}"
            
            # Render template
            template = self.jinja_env.get_template("password_reset.html")
            html_content = template.render(
                reset_url=reset_url,
                user_email=email,
                project_name=settings.PROJECT_NAME,
                expiry_hours=settings.EMAIL_RESET_TOKEN_EXPIRE_HOURS
            )
            
            # Send email
            return await self.send_email(
                to_email=email,
                subject=f"Reset your {settings.PROJECT_NAME} password",
                html_content=html_content
            )
            
        except Exception as e:
            logger.error(f"Error sending password reset email: {e}")
            return False
    
    async def send_welcome_email(self, email: str, user_name: str) -> bool:
        """
        Send welcome email to new users.
        
        Args:
            email: User email address
            user_name: User's display name
            
        Returns:
            bool: True if email sent successfully
        """
        try:
            # Render template
            template = self.jinja_env.get_template("welcome.html")
            html_content = template.render(
                user_name=user_name,
                project_name=settings.PROJECT_NAME,
                login_url=f"{settings.FRONTEND_URL}/login",
                support_email=settings.EMAILS_FROM_EMAIL
            )
            
            # Send email
            return await self.send_email(
                to_email=email,
                subject=f"Welcome to {settings.PROJECT_NAME}!",
                html_content=html_content
            )
            
        except Exception as e:
            logger.error(f"Error sending welcome email: {e}")
            return False
    
    async def send_document_processed_notification(
        self,
        email: str,
        document_title: str,
        document_id: UUID,
        processing_status: str
    ) -> bool:
        """
        Send document processing notification.
        
        Args:
            email: User email address
            document_title: Document title
            document_id: Document ID
            processing_status: Processing status (completed, failed)
            
        Returns:
            bool: True if email sent successfully
        """
        try:
            # Generate document URL
            document_url = f"{settings.FRONTEND_URL}/documents/{document_id}"
            
            # Render template
            template = self.jinja_env.get_template("document_processed.html")
            html_content = template.render(
                document_title=document_title,
                document_url=document_url,
                processing_status=processing_status,
                project_name=settings.PROJECT_NAME
            )
            
            # Determine subject based on status
            if processing_status == "completed":
                subject = f"Document processed successfully: {document_title}"
            else:
                subject = f"Document processing failed: {document_title}"
            
            # Send email
            return await self.send_email(
                to_email=email,
                subject=subject,
                html_content=html_content
            )
            
        except Exception as e:
            logger.error(f"Error sending document notification email: {e}")
            return False
    
    async def send_system_alert(
        self,
        admin_emails: List[str],
        alert_type: str,
        alert_message: str,
        severity: str = "warning"
    ) -> bool:
        """
        Send system alert to administrators.
        
        Args:
            admin_emails: List of admin email addresses
            alert_type: Type of alert
            alert_message: Alert message
            severity: Alert severity (info, warning, error, critical)
            
        Returns:
            bool: True if emails sent successfully
        """
        try:
            # Render template
            template = self.jinja_env.get_template("system_alert.html")
            html_content = template.render(
                alert_type=alert_type,
                alert_message=alert_message,
                severity=severity,
                timestamp=datetime.utcnow().isoformat(),
                project_name=settings.PROJECT_NAME
            )
            
            # Send to all admin emails
            success_count = 0
            for admin_email in admin_emails:
                success = await self.send_email(
                    to_email=admin_email,
                    subject=f"[{severity.upper()}] {settings.PROJECT_NAME} Alert: {alert_type}",
                    html_content=html_content
                )
                if success:
                    success_count += 1
            
            return success_count > 0
            
        except Exception as e:
            logger.error(f"Error sending system alert: {e}")
            return False
    
    async def _send_message(self, message: MIMEMultipart, to_email: str):
        """Send email message using SMTP."""
        try:
            if self.smtp_tls:
                # Use STARTTLS
                await aiosmtplib.send(
                    message,
                    hostname=self.smtp_host,
                    port=self.smtp_port,
                    username=self.smtp_user,
                    password=self.smtp_password,
                    start_tls=True
                )
            else:
                # Use plain SMTP
                await aiosmtplib.send(
                    message,
                    hostname=self.smtp_host,
                    port=self.smtp_port,
                    username=self.smtp_user,
                    password=self.smtp_password
                )
                
        except Exception as e:
            logger.error(f"SMTP error: {e}")
            raise
    
    async def _add_attachment(self, message: MIMEMultipart, attachment: Dict[str, Any]):
        """Add attachment to email message."""
        try:
            filename = attachment.get("filename", "attachment")
            content = attachment.get("content", b"")
            content_type = attachment.get("content_type", "application/octet-stream")
            
            part = MIMEBase(*content_type.split("/"))
            part.set_payload(content)
            encoders.encode_base64(part)
            
            part.add_header(
                "Content-Disposition",
                f"attachment; filename= {filename}"
            )
            
            message.attach(part)
            
        except Exception as e:
            logger.error(f"Error adding attachment: {e}")
            raise
    
    def _is_email_configured(self) -> bool:
        """Check if email service is properly configured."""
        return all([
            self.smtp_host,
            self.smtp_port,
            self.smtp_user,
            self.smtp_password,
            self.from_email
        ])
    
    # Template content methods
    def _get_verification_template(self) -> str:
        return """
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Verify Your Email</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #007bff; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background: #f9f9f9; }
        .button { display: inline-block; padding: 12px 24px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; }
        .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{ project_name }}</h1>
        </div>
        <div class="content">
            <h2>Verify Your Email Address</h2>
            <p>Hello,</p>
            <p>Thank you for signing up for {{ project_name }}. Please click the button below to verify your email address:</p>
            <p style="text-align: center;">
                <a href="{{ verification_url }}" class="button">Verify Email</a>
            </p>
            <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
            <p><a href="{{ verification_url }}">{{ verification_url }}</a></p>
            <p>This verification link will expire in 24 hours.</p>
        </div>
        <div class="footer">
            <p>This email was sent to {{ user_email }}. If you didn't request this, please ignore this email.</p>
        </div>
    </div>
</body>
</html>
"""
    
    def _get_password_reset_template(self) -> str:
        return """
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Reset Your Password</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #dc3545; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background: #f9f9f9; }
        .button { display: inline-block; padding: 12px 24px; background: #dc3545; color: white; text-decoration: none; border-radius: 4px; }
        .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{ project_name }}</h1>
        </div>
        <div class="content">
            <h2>Reset Your Password</h2>
            <p>Hello,</p>
            <p>You requested a password reset for your {{ project_name }} account. Click the button below to reset your password:</p>
            <p style="text-align: center;">
                <a href="{{ reset_url }}" class="button">Reset Password</a>
            </p>
            <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
            <p><a href="{{ reset_url }}">{{ reset_url }}</a></p>
            <p>This reset link will expire in {{ expiry_hours }} hours.</p>
            <p>If you didn't request this password reset, please ignore this email.</p>
        </div>
        <div class="footer">
            <p>This email was sent to {{ user_email }}.</p>
        </div>
    </div>
</body>
</html>
"""
    
    def _get_welcome_template(self) -> str:
        return """
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Welcome to {{ project_name }}</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #28a745; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background: #f9f9f9; }
        .button { display: inline-block; padding: 12px 24px; background: #28a745; color: white; text-decoration: none; border-radius: 4px; }
        .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Welcome to {{ project_name }}!</h1>
        </div>
        <div class="content">
            <h2>Hello {{ user_name }},</h2>
            <p>Welcome to {{ project_name }}! Your account has been successfully created.</p>
            <p>You can now access your corporate knowledge base and start asking questions to get instant answers from your organization's documents.</p>
            <p style="text-align: center;">
                <a href="{{ login_url }}" class="button">Login Now</a>
            </p>
            <p>If you have any questions or need assistance, please contact our support team at {{ support_email }}.</p>
        </div>
        <div class="footer">
            <p>Thank you for choosing {{ project_name }}!</p>
        </div>
    </div>
</body>
</html>
"""
    
    def _get_document_processed_template(self) -> str:
        return """
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Document Processing Update</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: {% if processing_status == 'completed' %}#28a745{% else %}#dc3545{% endif %}; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background: #f9f9f9; }
        .button { display: inline-block; padding: 12px 24px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; }
        .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{ project_name }}</h1>
        </div>
        <div class="content">
            <h2>Document Processing {% if processing_status == 'completed' %}Complete{% else %}Failed{% endif %}</h2>
            <p>Your document "{{ document_title }}" has been {% if processing_status == 'completed' %}successfully processed{% else %}failed to process{% endif %}.</p>
            {% if processing_status == 'completed' %}
            <p>The document is now available for search and knowledge queries.</p>
            <p style="text-align: center;">
                <a href="{{ document_url }}" class="button">View Document</a>
            </p>
            {% else %}
            <p>Please check the document format and try uploading again, or contact support if the issue persists.</p>
            {% endif %}
        </div>
        <div class="footer">
            <p>{{ project_name }} - Corporate Memory System</p>
        </div>
    </div>
</body>
</html>
"""
    
    def _get_system_alert_template(self) -> str:
        return """
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>System Alert</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: {% if severity == 'critical' %}#dc3545{% elif severity == 'error' %}#fd7e14{% elif severity == 'warning' %}#ffc107{% else %}#17a2b8{% endif %}; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background: #f9f9f9; }
        .alert-box { padding: 15px; margin: 10px 0; border-left: 4px solid {% if severity == 'critical' %}#dc3545{% elif severity == 'error' %}#fd7e14{% elif severity == 'warning' %}#ffc107{% else %}#17a2b8{% endif %}; background: white; }
        .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{ project_name }} System Alert</h1>
        </div>
        <div class="content">
            <div class="alert-box">
                <h3>{{ alert_type }} ({{ severity|upper }})</h3>
                <p>{{ alert_message }}</p>
                <p><strong>Timestamp:</strong> {{ timestamp }}</p>
            </div>
            <p>Please investigate this alert and take appropriate action if necessary.</p>
        </div>
        <div class="footer">
            <p>{{ project_name }} System Monitoring</p>
        </div>
    </div>
</body>
</html>
"""
