/**
 * KnowledgeBot - Corporate Memory System
 * useTheme Hook
 * 
 * Custom hook for theme management and preferences.
 * This is a temporary implementation until we can create the full ThemeContext.
 */

import { useState, useEffect } from 'react'
import { ThemeMode } from '@/types'

interface ThemeState {
  theme: ThemeMode
  primaryColor: string
  borderRadius: number
  compactMode: boolean
  systemPreference: 'light' | 'dark'
  effectiveTheme: 'light' | 'dark'
}

const defaultTheme: ThemeState = {
  theme: 'auto',
  primaryColor: '#3b82f6',
  borderRadius: 8,
  compactMode: false,
  systemPreference: 'light',
  effectiveTheme: 'light',
}

const THEME_STORAGE_KEY = 'knowledgebot-theme-preferences'

export function useTheme() {
  const [themeState, setThemeState] = useState<ThemeState>(defaultTheme)

  // Load theme preferences from localStorage on mount
  useEffect(() => {
    try {
      const stored = localStorage.getItem(THEME_STORAGE_KEY)
      if (stored) {
        const preferences = JSON.parse(stored)
        setThemeState(prev => ({
          ...prev,
          ...preferences,
          effectiveTheme: preferences.theme === 'auto' 
            ? prev.systemPreference
            : preferences.theme,
        }))
      }
    } catch (error) {
      console.warn('Failed to load theme preferences:', error)
    }
  }, [])

  // Save theme preferences to localStorage when state changes
  useEffect(() => {
    try {
      const preferences = {
        theme: themeState.theme,
        primaryColor: themeState.primaryColor,
        borderRadius: themeState.borderRadius,
        compactMode: themeState.compactMode,
      }
      localStorage.setItem(THEME_STORAGE_KEY, JSON.stringify(preferences))
    } catch (error) {
      console.warn('Failed to save theme preferences:', error)
    }
  }, [themeState.theme, themeState.primaryColor, themeState.borderRadius, themeState.compactMode])

  // Listen for system theme changes
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    
    const handleChange = (e: MediaQueryListEvent) => {
      const systemPreference = e.matches ? 'dark' : 'light'
      setThemeState(prev => ({
        ...prev,
        systemPreference,
        effectiveTheme: prev.theme === 'auto' ? systemPreference : prev.effectiveTheme,
      }))
    }

    // Set initial system preference
    const systemPreference = mediaQuery.matches ? 'dark' : 'light'
    setThemeState(prev => ({
      ...prev,
      systemPreference,
      effectiveTheme: prev.theme === 'auto' ? systemPreference : prev.effectiveTheme,
    }))

    // Listen for changes
    mediaQuery.addEventListener('change', handleChange)
    
    return () => mediaQuery.removeEventListener('change', handleChange)
  }, [])

  // Apply CSS custom properties when theme changes
  useEffect(() => {
    const root = document.documentElement
    
    // Set CSS custom properties
    root.style.setProperty('--primary-color', themeState.primaryColor)
    root.style.setProperty('--border-radius', `${themeState.borderRadius}px`)
    
    // Apply theme class
    root.classList.remove('light', 'dark')
    root.classList.add(themeState.effectiveTheme)
    
    // Apply compact mode
    if (themeState.compactMode) {
      root.classList.add('compact')
    } else {
      root.classList.remove('compact')
    }
  }, [themeState.effectiveTheme, themeState.primaryColor, themeState.borderRadius, themeState.compactMode])

  // Theme management functions
  const setTheme = (theme: ThemeMode) => {
    setThemeState(prev => ({
      ...prev,
      theme,
      effectiveTheme: theme === 'auto' ? prev.systemPreference : theme,
    }))
  }

  const setPrimaryColor = (color: string) => {
    setThemeState(prev => ({ ...prev, primaryColor: color }))
  }

  const setBorderRadius = (radius: number) => {
    setThemeState(prev => ({ ...prev, borderRadius: radius }))
  }

  const setCompactMode = (compact: boolean) => {
    setThemeState(prev => ({ ...prev, compactMode: compact }))
  }

  const toggleTheme = () => {
    const newTheme = themeState.effectiveTheme === 'light' ? 'dark' : 'light'
    setTheme(newTheme)
  }

  const resetTheme = () => {
    setThemeState(defaultTheme)
  }

  return {
    ...themeState,
    setTheme,
    setPrimaryColor,
    setBorderRadius,
    setCompactMode,
    toggleTheme,
    resetTheme,
  }
}
