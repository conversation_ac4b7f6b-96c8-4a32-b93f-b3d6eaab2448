# KnowledgeBot - Test Environment Docker Compose
# Configuration for running integration tests with all dependencies

version: '3.8'

services:
  # Test Database
  postgres-test:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: knowledgebot_test
      POSTGRES_USER: test
      POSTGRES_PASSWORD: test
    ports:
      - "5433:5432"
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U test -d knowledgebot_test"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Test Redis
  redis-test:
    image: redis:7-alpine
    ports:
      - "6380:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_test_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Test MinIO (S3 compatible storage)
  minio-test:
    image: minio/minio:latest
    ports:
      - "9001:9000"
      - "9091:9090"
    environment:
      MINIO_ROOT_USER: testuser
      MINIO_ROOT_PASSWORD: testpassword
    command: server /data --console-address ":9090"
    volumes:
      - minio_test_data:/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  # Backend Tests
  backend-test:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: testing
    depends_on:
      postgres-test:
        condition: service_healthy
      redis-test:
        condition: service_healthy
      minio-test:
        condition: service_healthy
    environment:
      # Test Database
      DATABASE_URL: postgresql+asyncpg://test:test@postgres-test:5432/knowledgebot_test
      
      # Test Redis
      REDIS_URL: redis://redis-test:6379/0
      CELERY_BROKER_URL: redis://redis-test:6379/1
      CELERY_RESULT_BACKEND: redis://redis-test:6379/2
      
      # Test Storage
      STORAGE_TYPE: minio
      S3_ENDPOINT_URL: http://minio-test:9000
      S3_ACCESS_KEY: testuser
      S3_SECRET_KEY: testpassword
      S3_BUCKET_NAME: test-bucket
      
      # Test Configuration
      TESTING: true
      JWT_SECRET_KEY: test-secret-key-for-testing-only
      MEMVID_MODEL_PATH: ./tests/fixtures/models
      ENABLE_VIRUS_SCANNING: false
      ENABLE_DOCUMENT_OCR: false
      LOG_LEVEL: WARNING
      
      # Disable external services
      ENABLE_SSO: false
      ENABLE_METRICS: false
      ENABLE_TRACING: false
    volumes:
      - ./backend:/app
      - test_storage:/app/tests/fixtures/storage
    command: >
      sh -c "
        echo 'Waiting for services to be ready...' &&
        sleep 10 &&
        echo 'Running database migrations...' &&
        alembic upgrade head &&
        echo 'Running tests...' &&
        pytest tests/ -v --cov=app --cov-report=html --cov-report=term-missing --cov-fail-under=90
      "

  # Frontend Tests
  frontend-test:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: test
    environment:
      VITE_API_BASE_URL: http://backend-test:8000/api/v1
      NODE_ENV: test
    volumes:
      - ./frontend:/app
      - /app/node_modules
    command: >
      sh -c "
        echo 'Running frontend tests...' &&
        npm test -- --coverage --watchAll=false --coverageThreshold='{\"global\":{\"branches\":90,\"functions\":90,\"lines\":90,\"statements\":90}}'
      "

  # Integration Tests
  integration-test:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: testing
    depends_on:
      - backend-test
      - frontend-test
    environment:
      API_BASE_URL: http://backend-test:8000
      FRONTEND_URL: http://frontend-test:3000
    volumes:
      - ./tests/integration:/app/tests/integration
    command: >
      sh -c "
        echo 'Running integration tests...' &&
        pytest tests/integration/ -v --tb=short
      "

  # Load Tests
  load-test:
    image: grafana/k6:latest
    depends_on:
      - backend-test
    volumes:
      - ./tests/load:/scripts
    environment:
      API_BASE_URL: http://backend-test:8000
    command: run /scripts/api-load-test.js

volumes:
  postgres_test_data:
  redis_test_data:
  minio_test_data:
  test_storage:
