"""
KnowledgeBot - Corporate Memory System
Admin API Endpoints

This module provides administrative endpoints for system management,
including roles, permissions, departments, and system configuration.
"""

from typing import List, Dict, Any, Optional
from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, desc
from sqlalchemy.orm import selectinload

from app.api.deps import get_current_user, get_db, require_permissions, get_common_params, CommonQueryParams
from app.models.user import User, Role, Permission, Department
from app.schemas.admin import (
    RoleResponse,
    RoleCreate,
    RoleUpdate,
    PermissionResponse,
    DepartmentResponse,
    DepartmentCreate,
    DepartmentUpdate,
    SystemStatsResponse
)
from app.services.analytics_service import AnalyticsService
from app.services.document_service import DocumentService

router = APIRouter()
analytics_service = AnalyticsService()
document_service = DocumentService()


# Role Management
@router.get("/roles", response_model=List[RoleResponse])
async def list_roles(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    _: None = Depends(require_permissions(["admin.roles.read"]))
) -> List[Role]:
    """
    List all roles with permissions.
    
    Args:
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        List[Role]: List of roles
    """
    try:
        stmt = select(Role).options(selectinload(Role.permissions)).order_by(Role.name)
        result = await db.execute(stmt)
        return result.scalars().all()
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list roles: {str(e)}"
        )


@router.get("/roles/{role_id}", response_model=RoleResponse)
async def get_role(
    role_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    _: None = Depends(require_permissions(["admin.roles.read"]))
) -> Role:
    """
    Get role by ID.
    
    Args:
        role_id: Role UUID
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Role: Role object
    """
    try:
        stmt = select(Role).options(selectinload(Role.permissions)).where(Role.id == role_id)
        result = await db.execute(stmt)
        role = result.scalar_one_or_none()
        
        if not role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Role not found"
            )
        
        return role
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get role: {str(e)}"
        )


@router.post("/roles", response_model=RoleResponse)
async def create_role(
    role_data: RoleCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    _: None = Depends(require_permissions(["admin.roles.create"]))
) -> Role:
    """
    Create new role.
    
    Args:
        role_data: Role creation data
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Role: Created role object
    """
    try:
        # Check if role already exists
        stmt = select(Role).where(Role.name == role_data.name)
        result = await db.execute(stmt)
        existing_role = result.scalar_one_or_none()
        
        if existing_role:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Role with this name already exists"
            )
        
        # Create new role
        role = Role(
            name=role_data.name,
            display_name=role_data.display_name,
            description=role_data.description,
            is_system_role=False,
            created_by=current_user.id
        )
        
        db.add(role)
        await db.flush()  # Get role ID
        
        # Assign permissions if provided
        if role_data.permission_ids:
            perms_stmt = select(Permission).where(Permission.id.in_(role_data.permission_ids))
            perms_result = await db.execute(perms_stmt)
            permissions = perms_result.scalars().all()
            role.permissions.extend(permissions)
        
        await db.commit()
        await db.refresh(role)
        
        return role
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create role: {str(e)}"
        )


@router.put("/roles/{role_id}", response_model=RoleResponse)
async def update_role(
    role_id: UUID,
    role_data: RoleUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    _: None = Depends(require_permissions(["admin.roles.update"]))
) -> Role:
    """
    Update role.
    
    Args:
        role_id: Role UUID
        role_data: Role update data
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Role: Updated role object
    """
    try:
        stmt = select(Role).options(selectinload(Role.permissions)).where(Role.id == role_id)
        result = await db.execute(stmt)
        role = result.scalar_one_or_none()
        
        if not role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Role not found"
            )
        
        if role.is_system_role:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot modify system role"
            )
        
        # Update fields
        update_data = role_data.dict(exclude_unset=True, exclude={"permission_ids"})
        for field, value in update_data.items():
            setattr(role, field, value)
        
        # Update permissions if provided
        if role_data.permission_ids is not None:
            perms_stmt = select(Permission).where(Permission.id.in_(role_data.permission_ids))
            perms_result = await db.execute(perms_stmt)
            permissions = perms_result.scalars().all()
            role.permissions.clear()
            role.permissions.extend(permissions)
        
        role.updated_by = current_user.id
        
        await db.commit()
        await db.refresh(role)
        
        return role
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update role: {str(e)}"
        )


@router.delete("/roles/{role_id}")
async def delete_role(
    role_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    _: None = Depends(require_permissions(["admin.roles.delete"]))
) -> Dict[str, str]:
    """
    Delete role.
    
    Args:
        role_id: Role UUID
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Dict[str, str]: Deletion confirmation
    """
    try:
        stmt = select(Role).where(Role.id == role_id)
        result = await db.execute(stmt)
        role = result.scalar_one_or_none()
        
        if not role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Role not found"
            )
        
        if role.is_system_role:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot delete system role"
            )
        
        # Check if role is assigned to any users
        users_count = await db.scalar(
            select(func.count(User.id)).where(User.roles.any(Role.id == role_id))
        )
        
        if users_count > 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Cannot delete role assigned to {users_count} users"
            )
        
        await db.delete(role)
        await db.commit()
        
        return {"message": "Role successfully deleted"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete role: {str(e)}"
        )


# Permission Management
@router.get("/permissions", response_model=List[PermissionResponse])
async def list_permissions(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    _: None = Depends(require_permissions(["admin.permissions.read"]))
) -> List[Permission]:
    """
    List all permissions.
    
    Args:
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        List[Permission]: List of permissions
    """
    try:
        stmt = select(Permission).order_by(Permission.category, Permission.name)
        result = await db.execute(stmt)
        return result.scalars().all()
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list permissions: {str(e)}"
        )


# Department Management
@router.get("/departments", response_model=List[DepartmentResponse])
async def list_departments(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    _: None = Depends(require_permissions(["admin.departments.read"]))
) -> List[Department]:
    """
    List all departments.
    
    Args:
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        List[Department]: List of departments
    """
    try:
        stmt = select(Department).order_by(Department.name)
        result = await db.execute(stmt)
        return result.scalars().all()
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list departments: {str(e)}"
        )


@router.post("/departments", response_model=DepartmentResponse)
async def create_department(
    dept_data: DepartmentCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    _: None = Depends(require_permissions(["admin.departments.create"]))
) -> Department:
    """
    Create new department.
    
    Args:
        dept_data: Department creation data
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Department: Created department object
    """
    try:
        # Check if department already exists
        stmt = select(Department).where(Department.name == dept_data.name)
        result = await db.execute(stmt)
        existing_dept = result.scalar_one_or_none()
        
        if existing_dept:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Department with this name already exists"
            )
        
        # Create new department
        department = Department(
            name=dept_data.name,
            display_name=dept_data.display_name,
            description=dept_data.description,
            is_active=dept_data.is_active,
            created_by=current_user.id
        )
        
        db.add(department)
        await db.commit()
        await db.refresh(department)
        
        return department
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create department: {str(e)}"
        )


@router.put("/departments/{dept_id}", response_model=DepartmentResponse)
async def update_department(
    dept_id: UUID,
    dept_data: DepartmentUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    _: None = Depends(require_permissions(["admin.departments.update"]))
) -> Department:
    """
    Update department.
    
    Args:
        dept_id: Department UUID
        dept_data: Department update data
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Department: Updated department object
    """
    try:
        stmt = select(Department).where(Department.id == dept_id)
        result = await db.execute(stmt)
        department = result.scalar_one_or_none()
        
        if not department:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Department not found"
            )
        
        # Update fields
        update_data = dept_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(department, field, value)
        
        department.updated_by = current_user.id
        
        await db.commit()
        await db.refresh(department)
        
        return department
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update department: {str(e)}"
        )


@router.delete("/departments/{dept_id}")
async def delete_department(
    dept_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    _: None = Depends(require_permissions(["admin.departments.delete"]))
) -> Dict[str, str]:
    """
    Delete department.
    
    Args:
        dept_id: Department UUID
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Dict[str, str]: Deletion confirmation
    """
    try:
        stmt = select(Department).where(Department.id == dept_id)
        result = await db.execute(stmt)
        department = result.scalar_one_or_none()
        
        if not department:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Department not found"
            )
        
        # Check if department has users
        users_count = await db.scalar(
            select(func.count(User.id)).where(User.departments.any(Department.id == dept_id))
        )
        
        if users_count > 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Cannot delete department with {users_count} users"
            )
        
        # Check if department has documents
        from app.models.document import Document
        docs_count = await db.scalar(
            select(func.count(Document.id)).where(Document.departments.any(Department.id == dept_id))
        )
        
        if docs_count > 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Cannot delete department with {docs_count} documents"
            )
        
        await db.delete(department)
        await db.commit()
        
        return {"message": "Department successfully deleted"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete department: {str(e)}"
        )


# System Statistics
@router.get("/stats", response_model=SystemStatsResponse)
async def get_system_stats(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    _: None = Depends(require_permissions(["admin.stats.read"]))
) -> Dict[str, Any]:
    """
    Get system statistics.
    
    Args:
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Dict[str, Any]: System statistics
    """
    try:
        # Get user statistics
        total_users = await db.scalar(select(func.count(User.id)))
        active_users = await db.scalar(select(func.count(User.id)).where(User.is_active == True))
        
        # Get document statistics
        doc_stats = await document_service.get_document_statistics(db)
        
        # Get analytics metrics
        user_departments = [dept.id for dept in current_user.departments]
        dashboard_metrics = await analytics_service.get_dashboard_metrics(db, user_departments)
        
        return {
            "users": {
                "total": total_users,
                "active": active_users,
                "inactive": total_users - active_users
            },
            "documents": doc_stats,
            "metrics": {metric.name: metric.value for metric in dashboard_metrics},
            "generated_at": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get system stats: {str(e)}"
        )


# System Maintenance
@router.post("/maintenance/cleanup-sessions")
async def cleanup_expired_sessions(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    _: None = Depends(require_permissions(["admin.maintenance"]))
) -> Dict[str, str]:
    """
    Clean up expired user sessions.
    
    Args:
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Dict[str, str]: Cleanup result
    """
    try:
        from app.services.auth_service import AuthService
        auth_service = AuthService()
        await auth_service.cleanup_expired_sessions(db)
        
        return {"message": "Expired sessions cleaned up successfully"}
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to cleanup sessions: {str(e)}"
        )


@router.post("/maintenance/reindex-documents")
async def reindex_documents(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    _: None = Depends(require_permissions(["admin.maintenance"]))
) -> Dict[str, str]:
    """
    Trigger document reindexing.
    
    Args:
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Dict[str, str]: Reindexing status
    """
    try:
        # This would trigger a background task to reindex all documents
        # For now, return a placeholder response
        return {"message": "Document reindexing initiated"}
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to initiate reindexing: {str(e)}"
        )
