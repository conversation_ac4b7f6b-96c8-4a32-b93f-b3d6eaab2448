/**
 * KnowledgeBot - Corporate Memory System
 * Authentication Service
 * 
 * Handles all authentication-related API calls including login, registration,
 * token management, and user profile operations.
 */

import { apiClient } from './apiClient'
import { 
  LoginRequest, 
  LoginResponse, 
  RegisterRequest, 
  User,
  ApiResponse 
} from '@/types'
import { tokenStorage } from '@/utils/tokenStorage'

class AuthService {
  /**
   * <PERSON>gin user with email and password
   */
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    const response = await apiClient.post<LoginResponse>('/auth/login', credentials)
    return response.data
  }

  /**
   * Register new user account
   */
  async register(userData: RegisterRequest): Promise<ApiResponse> {
    const response = await apiClient.post<ApiResponse>('/auth/register', userData)
    return response.data
  }

  /**
   * Logout current user
   */
  async logout(): Promise<void> {
    const refreshToken = tokenStorage.getRefreshToken()
    if (refreshToken) {
      try {
        await apiClient.post('/auth/logout', { refresh_token: refreshToken })
      } catch (error) {
        // Ignore logout errors - we'll clear tokens anyway
        console.warn('Logout request failed:', error)
      }
    }
  }

  /**
   * Refresh access token using refresh token
   */
  async refreshToken(): Promise<{ access_token: string; refresh_token: string }> {
    const refreshToken = tokenStorage.getRefreshToken()
    if (!refreshToken) {
      throw new Error('No refresh token available')
    }

    const response = await apiClient.post<{ access_token: string; refresh_token: string }>(
      '/auth/refresh',
      { refresh_token: refreshToken }
    )
    return response.data
  }

  /**
   * Get current user information
   */
  async getCurrentUser(): Promise<{
    user: User
    permissions: string[]
    departments: string[]
  } | null> {
    const token = tokenStorage.getAccessToken()
    if (!token) {
      return null
    }

    try {
      const response = await apiClient.get<{
        user: User
        permissions: string[]
        departments: string[]
      }>('/auth/me')
      return response.data
    } catch (error) {
      // If token is invalid, clear it
      tokenStorage.clearTokens()
      return null
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(data: Partial<User>): Promise<User> {
    const response = await apiClient.put<User>('/auth/profile', data)
    return response.data
  }

  /**
   * Change user password
   */
  async changePassword(oldPassword: string, newPassword: string): Promise<void> {
    await apiClient.post('/auth/change-password', {
      current_password: oldPassword,
      new_password: newPassword,
    })
  }

  /**
   * Request password reset
   */
  async requestPasswordReset(email: string): Promise<void> {
    await apiClient.post('/auth/forgot-password', { email })
  }

  /**
   * Reset password with token
   */
  async resetPassword(token: string, newPassword: string): Promise<void> {
    await apiClient.post('/auth/reset-password', {
      token,
      new_password: newPassword,
    })
  }

  /**
   * Verify email with token
   */
  async verifyEmail(token: string): Promise<void> {
    await apiClient.post('/auth/verify-email', { token })
  }

  /**
   * Resend email verification
   */
  async resendVerification(email: string): Promise<void> {
    await apiClient.post('/auth/resend-verification', { email })
  }

  /**
   * Get user sessions
   */
  async getUserSessions(): Promise<any[]> {
    const response = await apiClient.get('/auth/sessions')
    return response.data.sessions || []
  }

  /**
   * Revoke user sessions
   */
  async revokeSessions(sessionIds: string[]): Promise<void> {
    await apiClient.post('/auth/sessions/revoke', { session_ids: sessionIds })
  }

  /**
   * Enable two-factor authentication
   */
  async enableTwoFactor(method: 'totp' | 'sms' | 'email', phoneNumber?: string): Promise<{
    setup_key?: string
    qr_code?: string
    backup_codes: string[]
  }> {
    const response = await apiClient.post('/auth/2fa/setup', {
      method,
      phone_number: phoneNumber,
    })
    return response.data
  }

  /**
   * Verify two-factor authentication setup
   */
  async verifyTwoFactor(code: string, backupCode = false): Promise<void> {
    await apiClient.post('/auth/2fa/verify', {
      code,
      backup_code: backupCode,
    })
  }

  /**
   * Disable two-factor authentication
   */
  async disableTwoFactor(password: string, confirmationCode?: string): Promise<void> {
    await apiClient.post('/auth/2fa/disable', {
      password,
      confirmation_code: confirmationCode,
    })
  }

  /**
   * Get security events
   */
  async getSecurityEvents(limit = 50, skip = 0): Promise<{
    events: any[]
    total: number
  }> {
    const response = await apiClient.get('/auth/security-events', {
      params: { limit, skip },
    })
    return response.data
  }

  /**
   * Get account security status
   */
  async getAccountSecurity(): Promise<{
    two_factor_enabled: boolean
    two_factor_method?: string
    last_password_change?: string
    failed_login_attempts: number
    account_locked: boolean
    locked_until?: string
    recent_security_events: any[]
    trusted_devices: number
    active_sessions: number
  }> {
    const response = await apiClient.get('/auth/security')
    return response.data
  }

  /**
   * Validate token
   */
  async validateToken(token: string, tokenType: 'access' | 'refresh' = 'access'): Promise<{
    valid: boolean
    user_id?: string
    expires_at?: string
    permissions?: string[]
  }> {
    const response = await apiClient.post('/auth/validate-token', {
      token,
      token_type: tokenType,
    })
    return response.data
  }
}

// Export singleton instance
export const authService = new AuthService()
