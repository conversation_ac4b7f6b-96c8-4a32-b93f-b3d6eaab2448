# Contributing to KnowledgeBot - Corporate Memory System

Thank you for your interest in contributing to KnowledgeBot! This document provides guidelines and information for contributors.

## 🚀 Getting Started

### Prerequisites

- Python 3.9+
- Node.js 18+
- Docker & Docker Compose
- Git
- PostgreSQL 14+ (for local development)

### Development Environment Setup

1. **Fork and Clone**
   ```bash
   git clone https://github.com/YOUR_USERNAME/knowledgebot-corporate-memory.git
   cd knowledgebot-corporate-memory
   ```

2. **Set up Development Environment**
   ```bash
   cp .env.example .env.dev
   # Edit .env.dev with your local configuration
   ```

3. **Start Development Services**
   ```bash
   docker-compose -f docker-compose.dev.yml up -d
   ```

4. **Install Dependencies**
   ```bash
   # Backend
   cd backend
   python -m venv venv
   source venv/bin/activate  # Windows: venv\Scripts\activate
   pip install -r requirements-dev.txt
   
   # Frontend
   cd ../frontend
   npm install
   ```

5. **Run Database Migrations**
   ```bash
   cd backend
   alembic upgrade head
   ```

## 📋 Development Guidelines

### Code Style

#### Python (Backend)
- Follow PEP 8 style guide
- Use Black for code formatting: `black app/`
- Use isort for import sorting: `isort app/`
- Use mypy for type checking: `mypy app/`
- Maximum line length: 88 characters

#### TypeScript/React (Frontend)
- Follow Airbnb TypeScript style guide
- Use Prettier for formatting: `npm run format`
- Use ESLint for linting: `npm run lint`
- Use strict TypeScript configuration

#### General
- Write descriptive variable and function names
- Add docstrings for all public functions and classes
- Include type hints for Python code
- Write comprehensive comments for complex logic

### Commit Message Format

We use [Conventional Commits](https://www.conventionalcommits.org/) format:

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

**Types:**
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

**Examples:**
```
feat(auth): add SAML SSO integration
fix(search): resolve vector similarity scoring issue
docs(api): update authentication endpoint documentation
test(memvid): add unit tests for encoding service
```

### Branch Naming

- `feature/description` - New features
- `fix/description` - Bug fixes
- `docs/description` - Documentation updates
- `refactor/description` - Code refactoring
- `test/description` - Test additions/updates

### Testing Requirements

#### Backend Testing
```bash
cd backend
pytest tests/ -v --cov=app --cov-report=html --cov-fail-under=90
```

#### Frontend Testing
```bash
cd frontend
npm test -- --coverage --watchAll=false --coverageThreshold='{"global":{"branches":90,"functions":90,"lines":90,"statements":90}}'
```

#### Integration Testing
```bash
docker-compose -f docker-compose.test.yml up --abort-on-container-exit
```

### Code Review Process

1. **Self Review**
   - Test your changes thoroughly
   - Run all tests and ensure they pass
   - Check code coverage meets requirements
   - Verify documentation is updated

2. **Pull Request Requirements**
   - Clear, descriptive title
   - Detailed description of changes
   - Link to related issues
   - Screenshots for UI changes
   - Test results and coverage reports

3. **Review Criteria**
   - Code quality and style compliance
   - Test coverage and quality
   - Documentation completeness
   - Security considerations
   - Performance impact

## 🐛 Bug Reports

### Before Submitting
- Check existing issues for duplicates
- Test with the latest version
- Gather relevant system information

### Bug Report Template
```markdown
**Bug Description**
A clear description of the bug.

**Steps to Reproduce**
1. Go to '...'
2. Click on '...'
3. See error

**Expected Behavior**
What you expected to happen.

**Actual Behavior**
What actually happened.

**Environment**
- OS: [e.g., Ubuntu 20.04]
- Python version: [e.g., 3.9.7]
- Node.js version: [e.g., 18.17.0]
- Browser: [e.g., Chrome 91.0]

**Additional Context**
Any other relevant information.
```

## 💡 Feature Requests

### Feature Request Template
```markdown
**Feature Description**
A clear description of the feature you'd like to see.

**Problem Statement**
What problem does this feature solve?

**Proposed Solution**
How would you like this feature to work?

**Alternatives Considered**
Other solutions you've considered.

**Additional Context**
Any other relevant information.
```

## 🔒 Security

### Reporting Security Issues
- **DO NOT** create public issues for security vulnerabilities
- Email <EMAIL> with details
- Include steps to reproduce if possible
- We'll respond within 48 hours

### Security Guidelines
- Never commit secrets or credentials
- Use environment variables for configuration
- Validate all user inputs
- Follow OWASP security guidelines
- Use secure coding practices

## 📚 Documentation

### Documentation Standards
- Update README.md for significant changes
- Add docstrings for all public APIs
- Include code examples where helpful
- Update API documentation for endpoint changes
- Write clear, concise explanations

### API Documentation
- Use OpenAPI/Swagger specifications
- Include request/response examples
- Document error codes and messages
- Provide authentication requirements

## 🏗️ Architecture Decisions

### Adding New Dependencies
- Justify the need for new dependencies
- Consider security implications
- Check license compatibility
- Update requirements files
- Document in pull request

### Database Changes
- Create Alembic migrations for schema changes
- Test migrations on sample data
- Consider backward compatibility
- Document breaking changes

### API Changes
- Follow semantic versioning
- Maintain backward compatibility when possible
- Document breaking changes clearly
- Update client SDKs if applicable

## 🤝 Community

### Communication Channels
- GitHub Issues: Bug reports and feature requests
- GitHub Discussions: General questions and ideas
- Slack: Real-time development chat
- Email: <EMAIL> for security issues

### Code of Conduct
- Be respectful and inclusive
- Welcome newcomers and help them learn
- Focus on constructive feedback
- Respect different viewpoints and experiences

## 📝 License

By contributing to KnowledgeBot, you agree that your contributions will be licensed under the MIT License.

---

Thank you for contributing to KnowledgeBot! 🎉
