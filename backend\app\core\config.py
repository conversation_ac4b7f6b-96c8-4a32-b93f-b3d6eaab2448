"""
KnowledgeBot - Corporate Memory System
Core Configuration Module

This module contains all configuration settings for the application,
including database, authentication, external services, and environment-specific settings.
"""

import secrets
from typing import List, Optional, Union
from pydantic import BaseSettings, validator, AnyHttpUrl, PostgresDsn


class Settings(BaseSettings):
    """
    Application settings with environment variable support.
    
    All settings can be overridden via environment variables.
    """
    
    # Project Information
    PROJECT_NAME: str = "KnowledgeBot - Corporate Memory System"
    VERSION: str = "1.0.0"
    DESCRIPTION: str = "Enterprise Knowledge Management System with MemVid Integration"
    API_V1_STR: str = "/api/v1"
    
    # Environment
    ENVIRONMENT: str = "development"  # development, staging, production
    DEBUG: bool = False
    
    # Security
    SECRET_KEY: str = secrets.token_urlsafe(32)
    JWT_SECRET_KEY: str = secrets.token_urlsafe(32)
    JWT_ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    
    # CORS
    CORS_ORIGINS: List[AnyHttpUrl] = []
    ALLOWED_HOSTS: List[str] = ["localhost", "127.0.0.1"]
    
    @validator("CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)
    
    # Database Configuration
    POSTGRES_SERVER: str = "localhost"
    POSTGRES_USER: str = "knowledgebot"
    POSTGRES_PASSWORD: str = "password"
    POSTGRES_DB: str = "knowledgebot"
    POSTGRES_PORT: str = "5432"
    DATABASE_URL: Optional[PostgresDsn] = None
    
    @validator("DATABASE_URL", pre=True)
    def assemble_db_connection(cls, v: Optional[str], values: dict) -> str:
        if isinstance(v, str):
            return v
        return PostgresDsn.build(
            scheme="postgresql",
            user=values.get("POSTGRES_USER"),
            password=values.get("POSTGRES_PASSWORD"),
            host=values.get("POSTGRES_SERVER"),
            port=values.get("POSTGRES_PORT"),
            path=f"/{values.get('POSTGRES_DB') or ''}",
        )
    
    # Redis Configuration
    REDIS_URL: str = "redis://localhost:6379/0"
    REDIS_CACHE_TTL: int = 3600  # 1 hour
    
    # MemVid Configuration (Open Source - No API Key Required)
    MEMVID_MODEL_PATH: str = "./models/memvid"
    MEMVID_MODEL_NAME: str = "sentence-transformers/all-MiniLM-L6-v2"
    MEMVID_MAX_CHUNK_SIZE: int = 1000
    MEMVID_OVERLAP_SIZE: int = 200
    MEMVID_BATCH_SIZE: int = 32
    MEMVID_DEVICE: str = "cpu"  # or "cuda" for GPU acceleration
    
    # Vector Database Configuration
    VECTOR_DB_TYPE: str = "pinecone"  # pinecone, weaviate, qdrant
    
    # Pinecone Configuration
    PINECONE_API_KEY: str = ""
    PINECONE_ENVIRONMENT: str = "us-west1-gcp"
    PINECONE_INDEX_NAME: str = "knowledgebot"
    PINECONE_DIMENSION: int = 1536
    
    # Weaviate Configuration (Alternative)
    WEAVIATE_URL: str = "http://localhost:8080"
    WEAVIATE_API_KEY: str = ""
    
    # File Storage Configuration
    STORAGE_TYPE: str = "s3"  # s3, minio, local
    
    # S3/MinIO Configuration
    S3_BUCKET_NAME: str = "knowledgebot-documents"
    S3_ACCESS_KEY: str = ""
    S3_SECRET_KEY: str = ""
    S3_ENDPOINT_URL: Optional[str] = None  # For MinIO
    S3_REGION: str = "us-east-1"
    
    # Local Storage Configuration
    LOCAL_STORAGE_PATH: str = "./storage/documents"
    
    # Authentication & SSO
    ENABLE_SSO: bool = False
    
    # SAML Configuration
    SAML_ENTITY_ID: str = "knowledgebot"
    SAML_SSO_URL: str = ""
    SAML_SLO_URL: str = ""
    SAML_X509_CERT: str = ""
    SAML_PRIVATE_KEY: str = ""
    
    # OAuth Configuration
    OAUTH_CLIENT_ID: str = ""
    OAUTH_CLIENT_SECRET: str = ""
    OAUTH_AUTHORIZATION_URL: str = ""
    OAUTH_TOKEN_URL: str = ""
    OAUTH_USER_INFO_URL: str = ""
    
    # Email Configuration
    SMTP_TLS: bool = True
    SMTP_PORT: Optional[int] = None
    SMTP_HOST: Optional[str] = None
    SMTP_USER: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    EMAILS_FROM_EMAIL: Optional[str] = None
    EMAILS_FROM_NAME: Optional[str] = None
    
    # Slack Integration
    SLACK_BOT_TOKEN: str = ""
    SLACK_SIGNING_SECRET: str = ""
    SLACK_APP_TOKEN: str = ""
    
    # Microsoft Teams Integration
    TEAMS_APP_ID: str = ""
    TEAMS_APP_PASSWORD: str = ""
    TEAMS_TENANT_ID: str = ""
    
    # Rate Limiting
    RATE_LIMIT_REQUESTS: int = 100
    RATE_LIMIT_WINDOW: int = 60  # seconds
    
    # File Upload Limits
    MAX_FILE_SIZE: int = 50 * 1024 * 1024  # 50MB
    ALLOWED_FILE_TYPES: List[str] = [
        "application/pdf",
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "application/vnd.ms-powerpoint",
        "application/vnd.openxmlformats-officedocument.presentationml.presentation",
        "text/html",
        "text/plain",
        "text/markdown"
    ]
    
    # Logging Configuration
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "json"  # json, text
    LOG_FILE: Optional[str] = None
    
    # Monitoring & Observability
    ENABLE_METRICS: bool = True
    METRICS_PORT: int = 9090
    ENABLE_TRACING: bool = False
    JAEGER_ENDPOINT: str = "http://localhost:14268/api/traces"
    
    # Background Tasks
    CELERY_BROKER_URL: str = "redis://localhost:6379/1"
    CELERY_RESULT_BACKEND: str = "redis://localhost:6379/2"
    
    # Security Headers
    SECURITY_HEADERS: dict = {
        "X-Content-Type-Options": "nosniff",
        "X-Frame-Options": "DENY",
        "X-XSS-Protection": "1; mode=block",
        "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
        "Content-Security-Policy": "default-src 'self'",
        "Referrer-Policy": "strict-origin-when-cross-origin"
    }
    
    # Audit & Compliance
    AUDIT_LOG_RETENTION_DAYS: int = 2555  # 7 years
    ENABLE_DATA_ENCRYPTION: bool = True
    ENCRYPTION_KEY: str = secrets.token_urlsafe(32)
    
    # Performance Settings
    DATABASE_POOL_SIZE: int = 20
    DATABASE_MAX_OVERFLOW: int = 30
    QUERY_TIMEOUT: int = 30
    
    # Feature Flags
    ENABLE_DOCUMENT_OCR: bool = True
    ENABLE_REAL_TIME_SEARCH: bool = True
    ENABLE_ANALYTICS: bool = True
    ENABLE_VIRUS_SCANNING: bool = True
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """
    Get application settings instance.
    
    Returns:
        Settings: Application settings
    """
    return settings
