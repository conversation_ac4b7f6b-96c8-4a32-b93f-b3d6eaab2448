"""
KnowledgeBot - Corporate Memory System
Security and Authentication Module

This module handles JWT tokens, password hashing, RBAC, and security middleware
for the enterprise knowledge management system.
"""

import secrets
from datetime import datetime, timedelta
from typing import Any, Dict, Optional, Union
from passlib.context import CryptContext
from jose import <PERSON><PERSON><PERSON><PERSON>r, jwt
from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, status, Request, Response
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from starlette.middleware.base import BaseHTTPMiddleware

from app.core.config import settings

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT Bearer token scheme
security = HTTPBearer()


class SecurityHeaders(BaseHTTPMiddleware):
    """
    Middleware to add security headers to all responses.
    """
    
    async def dispatch(self, request: Request, call_next):
        response = await call_next(request)
        
        # Add security headers
        for header, value in settings.SECURITY_HEADERS.items():
            response.headers[header] = value
        
        # Add request ID for tracing
        request_id = getattr(request.state, "request_id", secrets.token_hex(8))
        response.headers["X-Request-ID"] = request_id
        
        return response


def create_access_token(
    subject: Union[str, Any], 
    expires_delta: Optional[timedelta] = None,
    additional_claims: Optional[Dict[str, Any]] = None
) -> str:
    """
    Create JWT access token.
    
    Args:
        subject: Token subject (usually user ID)
        expires_delta: Token expiration time
        additional_claims: Additional claims to include
        
    Returns:
        str: Encoded JWT token
    """
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode = {
        "exp": expire,
        "sub": str(subject),
        "iat": datetime.utcnow(),
        "type": "access"
    }
    
    if additional_claims:
        to_encode.update(additional_claims)
    
    encoded_jwt = jwt.encode(
        to_encode, 
        settings.JWT_SECRET_KEY, 
        algorithm=settings.JWT_ALGORITHM
    )
    return encoded_jwt


def create_refresh_token(subject: Union[str, Any]) -> str:
    """
    Create JWT refresh token.
    
    Args:
        subject: Token subject (usually user ID)
        
    Returns:
        str: Encoded JWT refresh token
    """
    expire = datetime.utcnow() + timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)
    
    to_encode = {
        "exp": expire,
        "sub": str(subject),
        "iat": datetime.utcnow(),
        "type": "refresh"
    }
    
    encoded_jwt = jwt.encode(
        to_encode,
        settings.JWT_SECRET_KEY,
        algorithm=settings.JWT_ALGORITHM
    )
    return encoded_jwt


def verify_token(token: str) -> Optional[Dict[str, Any]]:
    """
    Verify and decode JWT token.
    
    Args:
        token: JWT token string
        
    Returns:
        Optional[Dict[str, Any]]: Decoded token payload or None if invalid
    """
    try:
        payload = jwt.decode(
            token,
            settings.JWT_SECRET_KEY,
            algorithms=[settings.JWT_ALGORITHM]
        )
        return payload
    except JWTError:
        return None


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Verify password against hash.
    
    Args:
        plain_password: Plain text password
        hashed_password: Hashed password
        
    Returns:
        bool: True if password matches
    """
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """
    Hash password using bcrypt.
    
    Args:
        password: Plain text password
        
    Returns:
        str: Hashed password
    """
    return pwd_context.hash(password)


def generate_password_reset_token(email: str) -> str:
    """
    Generate password reset token.
    
    Args:
        email: User email address
        
    Returns:
        str: Password reset token
    """
    delta = timedelta(hours=settings.EMAIL_RESET_TOKEN_EXPIRE_HOURS)
    now = datetime.utcnow()
    expires = now + delta
    exp = expires.timestamp()
    encoded_jwt = jwt.encode(
        {"exp": exp, "nbf": now, "sub": email, "type": "password_reset"},
        settings.JWT_SECRET_KEY,
        algorithm=settings.JWT_ALGORITHM,
    )
    return encoded_jwt


def verify_password_reset_token(token: str) -> Optional[str]:
    """
    Verify password reset token and return email.
    
    Args:
        token: Password reset token
        
    Returns:
        Optional[str]: Email if token is valid, None otherwise
    """
    try:
        decoded_token = jwt.decode(
            token, 
            settings.JWT_SECRET_KEY, 
            algorithms=[settings.JWT_ALGORITHM]
        )
        if decoded_token.get("type") != "password_reset":
            return None
        return decoded_token["sub"]
    except JWTError:
        return None


class RoleChecker:
    """
    Role-based access control checker.
    """
    
    def __init__(self, allowed_roles: list):
        self.allowed_roles = allowed_roles
    
    def __call__(self, user_roles: list) -> bool:
        """
        Check if user has required roles.
        
        Args:
            user_roles: List of user roles
            
        Returns:
            bool: True if user has required roles
        """
        return any(role in self.allowed_roles for role in user_roles)


class PermissionChecker:
    """
    Permission-based access control checker.
    """
    
    def __init__(self, required_permissions: list):
        self.required_permissions = required_permissions
    
    def __call__(self, user_permissions: list) -> bool:
        """
        Check if user has required permissions.
        
        Args:
            user_permissions: List of user permissions
            
        Returns:
            bool: True if user has required permissions
        """
        return all(perm in user_permissions for perm in self.required_permissions)


class DepartmentAccessChecker:
    """
    Department-based access control for multi-tenant isolation.
    """
    
    def __init__(self, required_departments: list):
        self.required_departments = required_departments
    
    def __call__(self, user_departments: list) -> bool:
        """
        Check if user has access to required departments.
        
        Args:
            user_departments: List of user departments
            
        Returns:
            bool: True if user has department access
        """
        if not self.required_departments:  # No department restriction
            return True
        return any(dept in user_departments for dept in self.required_departments)


def create_api_key() -> str:
    """
    Generate secure API key for third-party integrations.
    
    Returns:
        str: API key
    """
    return f"kb_{secrets.token_urlsafe(32)}"


def verify_api_key(api_key: str) -> bool:
    """
    Verify API key format and structure.
    
    Args:
        api_key: API key to verify
        
    Returns:
        bool: True if API key format is valid
    """
    return api_key.startswith("kb_") and len(api_key) == 46


class RateLimiter:
    """
    Simple in-memory rate limiter for API endpoints.
    """
    
    def __init__(self):
        self.requests = {}
    
    def is_allowed(self, key: str, limit: int, window: int) -> bool:
        """
        Check if request is allowed based on rate limit.
        
        Args:
            key: Unique identifier (IP, user ID, etc.)
            limit: Maximum requests allowed
            window: Time window in seconds
            
        Returns:
            bool: True if request is allowed
        """
        now = datetime.utcnow().timestamp()
        
        if key not in self.requests:
            self.requests[key] = []
        
        # Remove old requests outside the window
        self.requests[key] = [
            req_time for req_time in self.requests[key]
            if now - req_time < window
        ]
        
        # Check if limit exceeded
        if len(self.requests[key]) >= limit:
            return False
        
        # Add current request
        self.requests[key].append(now)
        return True


# Global rate limiter instance
rate_limiter = RateLimiter()


def encrypt_sensitive_data(data: str) -> str:
    """
    Encrypt sensitive data using application encryption key.
    
    Args:
        data: Data to encrypt
        
    Returns:
        str: Encrypted data
    """
    # Implementation would use proper encryption library like cryptography
    # This is a placeholder for the actual implementation
    from cryptography.fernet import Fernet
    
    # In production, use settings.ENCRYPTION_KEY properly derived
    key = Fernet.generate_key()  # This should be settings.ENCRYPTION_KEY
    f = Fernet(key)
    encrypted_data = f.encrypt(data.encode())
    return encrypted_data.decode()


def decrypt_sensitive_data(encrypted_data: str) -> str:
    """
    Decrypt sensitive data using application encryption key.
    
    Args:
        encrypted_data: Encrypted data
        
    Returns:
        str: Decrypted data
    """
    # Implementation would use proper encryption library like cryptography
    # This is a placeholder for the actual implementation
    from cryptography.fernet import Fernet
    
    # In production, use settings.ENCRYPTION_KEY properly derived
    key = Fernet.generate_key()  # This should be settings.ENCRYPTION_KEY
    f = Fernet(key)
    decrypted_data = f.decrypt(encrypted_data.encode())
    return decrypted_data.decode()
