/**
 * KnowledgeBot - Corporate Memory System
 * Card Component
 * 
 * Flexible card component with header, body, and footer sections.
 * Supports various layouts and interactive states.
 */

import React from 'react'
import { clsx } from 'clsx'

export interface CardProps {
  children: React.ReactNode
  className?: string
  padding?: 'none' | 'sm' | 'md' | 'lg'
  shadow?: 'none' | 'sm' | 'md' | 'lg' | 'xl'
  rounded?: 'none' | 'sm' | 'md' | 'lg' | 'xl'
  border?: boolean
  hover?: boolean
  clickable?: boolean
  onClick?: () => void
}

export const Card: React.FC<CardProps> = ({
  children,
  className,
  padding = 'md',
  shadow = 'md',
  rounded = 'lg',
  border = true,
  hover = false,
  clickable = false,
  onClick,
}) => {
  const paddingClasses = {
    none: '',
    sm: 'p-3',
    md: 'p-6',
    lg: 'p-8',
  }

  const shadowClasses = {
    none: '',
    sm: 'shadow-sm',
    md: 'shadow-md',
    lg: 'shadow-lg',
    xl: 'shadow-xl',
  }

  const roundedClasses = {
    none: '',
    sm: 'rounded-sm',
    md: 'rounded-md',
    lg: 'rounded-lg',
    xl: 'rounded-xl',
  }

  const cardClasses = clsx(
    'bg-white dark:bg-gray-800',
    paddingClasses[padding],
    shadowClasses[shadow],
    roundedClasses[rounded],
    {
      'border border-gray-200 dark:border-gray-700': border,
      'hover:shadow-lg transition-shadow duration-200': hover,
      'cursor-pointer': clickable || onClick,
      'hover:bg-gray-50 dark:hover:bg-gray-750': clickable || onClick,
    },
    className
  )

  const Component = onClick ? 'button' : 'div'

  return (
    <Component className={cardClasses} onClick={onClick}>
      {children}
    </Component>
  )
}

// Card Header
export interface CardHeaderProps {
  children: React.ReactNode
  className?: string
  divider?: boolean
}

export const CardHeader: React.FC<CardHeaderProps> = ({
  children,
  className,
  divider = true,
}) => {
  return (
    <div
      className={clsx(
        'mb-4',
        {
          'pb-4 border-b border-gray-200 dark:border-gray-700': divider,
        },
        className
      )}
    >
      {children}
    </div>
  )
}

// Card Title
export interface CardTitleProps {
  children: React.ReactNode
  className?: string
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6'
}

export const CardTitle: React.FC<CardTitleProps> = ({
  children,
  className,
  as: Component = 'h3',
}) => {
  return (
    <Component
      className={clsx(
        'text-lg font-semibold text-gray-900 dark:text-gray-100',
        className
      )}
    >
      {children}
    </Component>
  )
}

// Card Description
export interface CardDescriptionProps {
  children: React.ReactNode
  className?: string
}

export const CardDescription: React.FC<CardDescriptionProps> = ({
  children,
  className,
}) => {
  return (
    <p
      className={clsx(
        'text-sm text-gray-600 dark:text-gray-400 mt-1',
        className
      )}
    >
      {children}
    </p>
  )
}

// Card Body
export interface CardBodyProps {
  children: React.ReactNode
  className?: string
}

export const CardBody: React.FC<CardBodyProps> = ({
  children,
  className,
}) => {
  return (
    <div className={clsx('flex-1', className)}>
      {children}
    </div>
  )
}

// Card Footer
export interface CardFooterProps {
  children: React.ReactNode
  className?: string
  divider?: boolean
  justify?: 'start' | 'center' | 'end' | 'between'
}

export const CardFooter: React.FC<CardFooterProps> = ({
  children,
  className,
  divider = true,
  justify = 'end',
}) => {
  const justifyClasses = {
    start: 'justify-start',
    center: 'justify-center',
    end: 'justify-end',
    between: 'justify-between',
  }

  return (
    <div
      className={clsx(
        'mt-4 flex items-center',
        justifyClasses[justify],
        {
          'pt-4 border-t border-gray-200 dark:border-gray-700': divider,
        },
        className
      )}
    >
      {children}
    </div>
  )
}

// Stats Card
export interface StatsCardProps {
  title: string
  value: string | number
  change?: {
    value: number
    type: 'increase' | 'decrease'
    period?: string
  }
  icon?: React.ReactNode
  className?: string
}

export const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  change,
  icon,
  className,
}) => {
  return (
    <Card className={clsx('relative overflow-hidden', className)}>
      <div className="flex items-center">
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-600 dark:text-gray-400 truncate">
            {title}
          </p>
          <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
            {value}
          </p>
          {change && (
            <div className="flex items-center mt-1">
              <span
                className={clsx('text-sm font-medium', {
                  'text-green-600 dark:text-green-400': change.type === 'increase',
                  'text-red-600 dark:text-red-400': change.type === 'decrease',
                })}
              >
                {change.type === 'increase' ? '+' : '-'}{Math.abs(change.value)}%
              </span>
              {change.period && (
                <span className="text-sm text-gray-500 dark:text-gray-400 ml-1">
                  {change.period}
                </span>
              )}
            </div>
          )}
        </div>
        {icon && (
          <div className="flex-shrink-0">
            <div className="w-8 h-8 text-gray-400 dark:text-gray-500">
              {icon}
            </div>
          </div>
        )}
      </div>
    </Card>
  )
}

// Feature Card
export interface FeatureCardProps {
  title: string
  description: string
  icon?: React.ReactNode
  action?: {
    label: string
    onClick: () => void
  }
  className?: string
}

export const FeatureCard: React.FC<FeatureCardProps> = ({
  title,
  description,
  icon,
  action,
  className,
}) => {
  return (
    <Card className={clsx('text-center', className)} hover>
      {icon && (
        <div className="flex justify-center mb-4">
          <div className="w-12 h-12 text-blue-600 dark:text-blue-400">
            {icon}
          </div>
        </div>
      )}
      <CardTitle className="mb-2">{title}</CardTitle>
      <CardDescription className="mb-4">{description}</CardDescription>
      {action && (
        <button
          onClick={action.onClick}
          className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium text-sm"
        >
          {action.label}
        </button>
      )}
    </Card>
  )
}
